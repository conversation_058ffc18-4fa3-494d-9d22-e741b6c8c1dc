# 地理编码与边界数据验证解决方案

## 问题描述

在房源数据处理过程中，`setPropGeoQAndBoundaryTags` 函数会对比有经纬度的房源city与boundary city，当不一致时会调用 `doGeoCoding` 函数进行地理编码。需要在geocoding后重新获取boundary信息，并判断是房源数据错误还是boundary数据错误。

## 当前流程分析

### 现有代码流程
1. `setPropGeoQAndBoundaryTags` 调用 `shouldSkipGeoCoding`
2. `shouldSkipGeoCoding` 中通过 `getBoundaryTagAsync` 获取边界标签
3. 比较房源city与boundary city (`tagCity` vs `propCity`)
4. 如果不一致，返回 `{isSkip:false}`，触发地理编码
5. `doGeoCoding` 调用 `GeoCoder.geocoding` 进行地理编码
6. 地理编码完成后调用 `checkProvAndCity` 进行省份城市匹配检查

### 涉及的关键文件
- `/src/libapp/saveToMaster.coffee` - 主要逻辑文件
- `/src/model/020_geoCoderLocal.coffee` - 地理编码实现
- `/src/libapp/propertyTagHelper.coffee` - 边界标签获取

## 解决方案设计

### 1. 核心思路

在 `doGeoCoding` 函数中，geocoding完成后：
1. 保存geocoding前的原始经纬度和boundary信息
2. 使用新的经纬度重新获取boundary信息
3. 对比分析geocoding前后的数据一致性
4. 基于多维度判断确定数据错误类型

### 2. 实现方案

#### 2.1 修改 `doGeoCoding` 函数

在 `doGeoCoding` 函数中添加boundary验证逻辑：

```coffeescript
# 在geocoding成功后添加boundary验证
if geoResult and geoResult.lat and geoResult.lng and Boundary
  try
    # 执行boundary验证和错误判断
    boundaryValidation = await validateBoundaryAfterGeocoding({
      originalProp: {
        lat: item.lat,
        lng: item.lng,
        city: item.city,
        prov: item.prov
      },
      geocodedProp: {
        lat: geoResult.lat,
        lng: geoResult.lng,
        city: item.city,
        prov: item.prov
      },
      Boundary,
      metaInfo
    })
    
    # 将验证结果添加到metaInfo
    metaInfo.boundaryValidation = boundaryValidation
  catch err
    debug.error "Boundary validation error:", err
```

#### 2.2 创建 `validateBoundaryAfterGeocoding` 函数

```coffeescript
validateBoundaryAfterGeocoding = ({originalProp, geocodedProp, Boundary, metaInfo}) ->
  result = {
    errorType: null,           # 'property_data' | 'boundary_data' | 'both' | 'none'
    confidence: 0,             # 0-100 置信度
    originalBoundary: null,    # 原始经纬度的boundary信息
    geocodedBoundary: null,    # geocoding后的boundary信息
    distanceMeters: 0,         # 两个坐标点的距离
    analysis: {}               # 详细分析信息
  }
  
  # 1. 获取原始经纬度的boundary信息（如果存在）
  if originalProp.lat and originalProp.lng
    result.originalBoundary = await getBoundaryTagAsync({
      Boundary,
      prop: {
        lat: originalProp.lat,
        lng: originalProp.lng
      }
    })
  
  # 2. 获取geocoding后经纬度的boundary信息
  result.geocodedBoundary = await getBoundaryTagAsync({
    Boundary,
    prop: {
      lat: geocodedProp.lat,
      lng: geocodedProp.lng
    }
  })
  
  # 3. 计算两个坐标点的距离
  if originalProp.lat and originalProp.lng
    result.distanceMeters = geolib.getDistance(
      { latitude: originalProp.lat, longitude: originalProp.lng },
      { latitude: geocodedProp.lat, longitude: geocodedProp.lng }
    )
  
  # 4. 执行错误类型判断
  result = await analyzeBoundaryDataError(result, originalProp, geocodedProp)
  
  return result
```

#### 2.3 创建 `analyzeBoundaryDataError` 函数

```coffeescript
analyzeBoundaryDataError = (result, originalProp, geocodedProp) ->
  { originalBoundary, geocodedBoundary, distanceMeters } = result
  
  # 获取boundary city信息
  originalBoundaryCity = originalBoundary?.bndCity?.nm?.toUpperCase() or ''
  geocodedBoundaryCity = geocodedBoundary?.bndCity?.nm?.toUpperCase() or ''
  propCity = originalProp.city.toUpperCase()
  
  result.analysis = {
    propCity,
    originalBoundaryCity,
    geocodedBoundaryCity,
    distanceMeters,
    originalCityMatch: originalBoundaryCity is propCity,
    geocodedCityMatch: geocodedBoundaryCity is propCity,
    boundariesMatch: originalBoundaryCity is geocodedBoundaryCity
  }
  
  # 判断逻辑
  if not originalBoundary and not geocodedBoundary
    # 两个位置都没有boundary信息，可能是偏远地区
    result.errorType = 'none'
    result.confidence = 60
    
  else if not originalBoundary and geocodedBoundary
    # 原始位置无boundary，geocoding后有boundary
    if geocodedBoundaryCity is propCity
      result.errorType = 'property_data'  # 原始经纬度错误
      result.confidence = 85
    else
      result.errorType = 'both'  # 可能都有问题
      result.confidence = 40
      
  else if originalBoundary and not geocodedBoundary
    # 原始位置有boundary，geocoding后无boundary
    if originalBoundaryCity is propCity
      result.errorType = 'boundary_data'  # geocoding结果可能错误
      result.confidence = 70
    else
      result.errorType = 'property_data'  # 原始数据错误
      result.confidence = 75
      
  else if originalBoundary and geocodedBoundary
    # 两个位置都有boundary信息
    if originalBoundaryCity is propCity and geocodedBoundaryCity is propCity
      # 两个boundary都匹配房源city
      if originalBoundaryCity is geocodedBoundaryCity
        result.errorType = 'none'  # 数据一致，可能是边界重叠区域
        result.confidence = 90
      else
        result.errorType = 'boundary_data'  # boundary数据可能有重叠或错误
        result.confidence = 60
        
    else if originalBoundaryCity is propCity and geocodedBoundaryCity isnt propCity
      # 原始boundary匹配，geocoding后不匹配
      if distanceMeters > 5000  # 距离较远
        result.errorType = 'property_data'  # 可能是地址描述不准确
        result.confidence = 70
      else
        result.errorType = 'boundary_data'  # boundary边界可能有问题
        result.confidence = 65
        
    else if originalBoundaryCity isnt propCity and geocodedBoundaryCity is propCity
      # 原始boundary不匹配，geocoding后匹配
      result.errorType = 'property_data'  # 原始经纬度错误
      result.confidence = 85
      
    else
      # 两个boundary都不匹配房源city
      if distanceMeters < 1000  # 距离很近
        result.errorType = 'boundary_data'  # boundary数据可能错误
        result.confidence = 70
      else
        result.errorType = 'both'  # 可能都有问题
        result.confidence = 50
  
  return result
```

### 3. 数据记录和日志

#### 3.1 在metaInfo中记录验证结果

```coffeescript
metaInfo.boundaryValidation = {
  errorType: 'property_data',
  confidence: 85,
  originalBoundary: { bndCity: { nm: 'Toronto' } },
  geocodedBoundary: { bndCity: { nm: 'Mississauga' } },
  distanceMeters: 15000,
  analysis: {
    propCity: 'MISSISSAUGA',
    originalBoundaryCity: 'TORONTO',
    geocodedBoundaryCity: 'MISSISSAUGA',
    originalCityMatch: false,
    geocodedCityMatch: true,
    boundariesMatch: false
  }
}
```

#### 3.2 添加详细日志记录

```coffeescript
# 在doGeoCoding函数中添加日志
if boundaryValidation.errorType isnt 'none'
  debug.warn "Boundary validation [#{item._id}]: #{boundaryValidation.errorType} 
    (confidence: #{boundaryValidation.confidence}%), 
    distance: #{boundaryValidation.distanceMeters}m, 
    prop city: #{boundaryValidation.analysis.propCity}, 
    original boundary: #{boundaryValidation.analysis.originalBoundaryCity}, 
    geocoded boundary: #{boundaryValidation.analysis.geocodedBoundaryCity}"
```

### 4. 配置和优化

#### 4.1 添加配置参数

```coffeescript
# 在函数参数中添加Boundary参数
module.exports.doGeoCoding = doGeoCoding = (GeoCoder, item, metaInfo, Boundary, cb) ->
```

#### 4.2 性能优化考虑

1. **缓存机制**: 对相同经纬度的boundary查询结果进行缓存
2. **批量处理**: 如果有多个相近的坐标点，可以批量查询boundary
3. **异步处理**: boundary验证不影响主流程，可以异步执行

### 5. 测试方案

#### 5.1 单元测试用例

```coffeescript
describe 'validateBoundaryAfterGeocoding', ->
  tests = [
    {
      desc: '原始经纬度错误，geocoding后正确',
      originalProp: { lat: 43.0, lng: -79.0, city: 'MISSISSAUGA' },
      geocodedProp: { lat: 43.6, lng: -79.6, city: 'MISSISSAUGA' },
      expected: { errorType: 'property_data', confidence: 85 }
    },
    {
      desc: 'boundary数据重叠或错误',
      originalProp: { lat: 43.65, lng: -79.38, city: 'TORONTO' },
      geocodedProp: { lat: 43.66, lng: -79.39, city: 'TORONTO' },
      expected: { errorType: 'boundary_data', confidence: 60 }
    }
  ]
```

#### 5.2 集成测试

1. 使用真实的房源数据进行测试
2. 验证不同场景下的判断准确性
3. 测试性能影响

### 6. 部署和监控

#### 6.1 渐进式部署

1. 首先在测试环境验证功能
2. 在生产环境以只记录不影响业务的方式部署
3. 收集数据分析准确性后，逐步应用到业务逻辑

#### 6.2 监控指标

1. boundary验证执行次数
2. 各种错误类型的分布
3. 置信度分布
4. 性能影响监控

## 总结

该解决方案通过在geocoding后重新获取boundary信息，并基于多维度分析来判断数据错误类型。方案具有以下特点：

1. **全面性**: 考虑了各种可能的数据错误情况
2. **可扩展性**: 可以根据实际情况调整判断逻辑和置信度
3. **性能友好**: 不影响主流程，可以异步执行
4. **可监控**: 提供详细的日志和统计信息
5. **渐进式**: 支持逐步部署和优化

通过这个方案，可以有效识别房源数据和boundary数据的错误，为数据质量改进提供依据。
