<template>
  <div class="search-container-input" :class="page">
    <span class="fa fa-search searchInputIcon"></span>
    <input v-model="searchStr" id="searchAutocomplete" name="search" :placeholder='_(searchPlaceHolder)'>
    <span v-show="searchStr" class="fa fa-times-circle-o searchClear" @click="searchStr=''"></span>
    <div class="searchResult" v-if="searchStr.length > 3">
      <div class="searchResultTabs">
        <a class="searchResultTab" v-for="t in tabs" :key="t.tl" :class="t.tl == tab?'active':''" @click="setTab(t.tl)">
          <span class="searchResultTabTl">{{_(t.tl,'autocompletes')}}</span>
          <span v-show="searching" class="searchResultTabCnt fa fa-spinner"></span>
          <span v-show="!searching" class="searchResultTabCnt">({{t.cnt}})</span>
        </a>
      </div>
      <div class="searchResultList" v-show="tab == 'listings'">
        <div class="searchResultItem" v-for="p in autocompletes" :key="p._id" @click="openProp(p)">
          <div v-if="needLogin || p.login" class="searchResultItemNeedLogin">
            <span>{{_('Need Login to View')}}</span>
          </div>
          <div class="searchResultImgContainer" :class="{'blur':p.login}">
            <lazy-image class="searchResultImg" :src="p.thumbUrl || '/img/noPic.png'" @error="handleErrorImg" :imgstyle="'width: 80px;height: 60px;'" referrerpolicy="same-origin"/>
            <div class="searchResultSid">{{p.sid || p.id}}</div>
          </div>
          <div class="searchResultDetail" :class="{'blur':p.login}">
            <div class="searchResultAddr">{{p.origUnt||p.unt||''}} {{p.addr}}, {{p.city}}, {{p.prov}}</div>
            <div class="searchResultPriceType">
              <span v-if="p.lp == 0 || p.lpr == 0" class="searchResultPrice">{{_('To Be Negotiated')}}</span>
              <span v-else class="searchResultPrice">{{computedPrice(p.sp||p.lp || p.lpr)}}</span>
              <span v-show="p.stplabel == 'sold' || p.ltp == 'sold'" class="searchResultSold">{{computedPrice(p.lp ||p.lpr)}}</span>
              <span class="searchResultType" :class="p.stplabel||p.ltp">{{p.stplabel||p.ltp}}</span>
            </div>
            <div class="searchResultRooms" v-if="p.rmbdrm||p.tbdrms||p.bdrms||p.rmbthrm||p.tbthrms||p.bthrms||p.rmgr||p.tgr||p.gr">
              <div class="searchResultRoom" v-if="p.rmbdrm || p.tbdrms || p.bdrms">
                <span class="searchResultRoomIcon fa fa-bed"></span>
                <span class="searchResultRoomNum">{{p.rmbdrm || p.tbdrms || p.bdrms}}</span>
              </div>
              <div class="searchResultRoom" v-if="p.rmbthrm || p.tbthrms || p.bthrms">
                <span class="searchResultRoomIcon fa fa-bath"></span>
                <span class="searchResultRoomNum">{{p.rmbthrm || p.tbthrms || p.bthrms}}</span>
              </div>
              <div class="searchResultRoom" v-if="p.rmgr || p.gr ||p.tgr">
                <span class="searchResultRoomIcon fa fa-car"></span>
                <span class="searchResultRoomNum">{{ p.rmgr || p.tgr || p.gr}}</span>
              </div>
              <span class="searchResultTp">{{p.ptp || p.ptype2.join(' ')}}</span>
            </div>
            <div class="searchResultDate" v-if="p.ts">{{p.ts.substr(0,10)}} {{_('listed')}}</div>
            <div class="searchResultRltr" v-if="p.rltr">{{(_('Brokerage'))}}: {{p.rltr}}</div>
          </div>
          <div class="searchResultActions">
            <!-- <span class="fa fa-map-marker" @click="gotoMap(p)"></span> -->
            <span class="fa fa-angle-right" @click="openProp(p)"></span>
          </div>
        </div>
        <div class="searchResultEmpty" v-if="autocompletes.length == 0">{{_('No related listing found')}}</div>
        <!-- <div class="searchResultSeeAll" @click="showAll('listings')" v-if="autocompletes.length > 9">{{_('See all results')}}</div> -->
      </div>
      <div class="searchResultList" v-show="tab == 'new condos'">
        <div class="searchResultItem" v-for="p in projs" :key="p._id" @click="openProp(p,'proj')">
          <div v-if="needLogin" class="searchResultItemNeedLogin">
            <span>{{_('Need Login to View')}}</span>
          </div>
          <lazy-image class="searchResultImg" :src="p.thumbUrl" @error="handleErrorImg" :imgstyle="'width: 80px;height: 60px;'" referrerpolicy="same-origin"/>
          <div class="searchResultDetail">
            <div class="projNm">{{p.nmOrig || (lang!='en'?(p.nm||p.nm_en):(p.nm_en||p.nm))}}</div>
            <!-- <div class="projStatus">{{_('Status')}}: {{p.saleStatus}}</div>
            <div>{{lang!='en'?(p.desc||p.desc_en):p.desc_en}}</div> -->
            <div>{{_('Builder')}}: {{p.builder}}</div>
            <div class="projLoc">{{p.city}} {{p.prov}}</div>
          </div>
        </div>
        <div class="searchResultEmpty" v-if="projs.length == 0">{{_('No related project found')}}</div>
        <!-- <div class="searchResultSeeAll" @click="showAll('pre-construction')" v-if="projs.length > 9">{{_('See all results')}}</div> -->
      </div>
      <!-- <div class="searchResultList" v-show="tab == 'school'">
        <div class="searchResultItem" v-for="s in schools" :key="s._id">
          <div class="searchResultSchoolIcon" :class="s.schTp"></div>
          <div class="searchResultDetail">
            <div class="searchResultSchoolNm">{{s.nm}}</div>
            <div>
              <span class="searchResultSchoolTp" :class="s.schTp">{{s.schTp}}</span>
              <span>{{s.city}}</span>
            </div>
            <div class="searchResultSchoolInfo">{{s.grade}} {{_('grades')}} | {{_('Rank')}} {{s.schRank}}</div>
          </div>
        </div>
        <div class="searchResultEmpty" v-if="schools.length == 0">{{_('No related school found')}}</div>
        <div class="searchResultSeeAll" @click="showAll('school')" v-if="schools.length > 9">{{_('See all results')}}</div>
      </div> -->
    </div>
  </div>
</template>

<script>
import LazyImage from '../../coffee4client/components/frac/LazyImage.vue'
export default {
  mixins: [],
  props:{
    page:{
      type:String,
      default:function(){
        return '';
      }
    },
    searchPlaceHolder:{
      type:String,
      default:function() {
        return 'Enter address, MLS#...';
      }
    }
  },
  data() {
    return {
      searching:'false',
      lang:'en',
      searchStr:'',
      autocompletes:[],
      schools:[],
      projs:[],
      tabs:[
        {tl:'listings',cnt:0},
        {tl:'new condos',cnt:0},
        // {tl:'school',cnt:0}
      ],
      needLogin:false,
      tab:'listings'
    }
  },
  components:{
    LazyImage
  },
  mounted: function() {
    if(vars.lang || lang){
      this.lang = vars.lang || lang
    }
    if(vars.isMLS){
      this.tabs.pop()
    }
  },
  computed:{
  },
  watch:{
    searchStr:function(val){
      var self = this;
      if (val.length >= 4) {
        clearTimeout(self.debounceTimeout);
        self.debounceTimeout = setTimeout(function () {
          self.debounceTimeout = null;
          self.getAutoCompletes();
          // if(self.searchGoogle) {
          //   self.searchGoogleAutoComplete();
          // }
        }, 500);
      }
    }
  },
  methods: {
    handleErrorImg() {
      return '/img/noPic.png';
    },
    isSold(p) {
      return p.showSoldPrice && p.sp && p.status_en !== 'A'
    },
    setTab(tab) {
      this.tab = tab;
    },
    computedPrice(lp) {
      return currencyFormat(lp, '$', 0);
    },
    openProp(p,tp='prop') {
      if(vars.isPopup){
        var ret = JSON.stringify({id:p._id,src:p.src});
        return window.rmCall(':ctx:' + ret);
      }
      if (this.needLogin) {
        return window.location = '/'+lang+'/www/login';
      }
      if (tp == 'proj') {
        var link = p.url;
      }
      if (tp == 'prop') {
        var link = p.webUrl;
      }
      window.open(link,'_blank');
    },
    gotoMap(p) {
      var saletp = p.stplabel||p.ltp;
      if (saletp != 'sale' || saletp != 'rent') {
        saletp = 'sale';
      }
      var link = '/'+lang+'/for-'+saletp+'/'+p.city_en+'/view=map.ptype=Residential.mlsonly=1.hasCenter=1';
      p.isCenter = 1;
      localStorage.setItem('centerProp',JSON.stringify(p));
      window.open(link,'_blank');
    },
    showAll(tp) {
      switch (tp) {
        case 'listings':
          window.location = '/'+this.lang+'/for-sale/canada/view=map.search='+this.searchStr;
          break;
      
        default:
          break;
      }
    },
    // getSchools() {
    //   var self = this;
    //   var data = {nm:self.searchStr};
    //   self.$http.post('/1.5/searchSchools', data).then(
    //     function (ret) {
    //       self.schools = ret.data;
    //       self.tabs[2].cnt = self.schools.length;
    //     },
    //     function (ret) {
    //       console.error( "server-error" );
    //     }
    //   );
    // },
    getProjects() {
      var self = this;
      var data = {nm:self.searchStr};
      self.$http.post('/1.5/prop/projects/projectList', data).then(
        function (ret) {
          ret = ret.data;
          self.searching = false;
          if (ret.ok) {
            self.projs = ret.l
            self.tabs[1].cnt = ret.l.length;
            // if (self.projs.length == 0) {
            //   self.getSchools();
            // } else {
            //   setTimeout(()=>{
            //     self.getSchools();
            //   },1000);
            // }
          }
          if (self.projs.length == 0) {
            self.tab = 'listings';
          }
        },
        function(err){
          ajaxError(err);
        }
      );
    },
    getAutoCompletes(){
      var self = this;
      var data = {s:self.searchStr,page:'home',loc:1};
      self.autoPreviousRequestTs = Date.now();
      data.ts = self.autoPreviousRequestTs;
      self.acloading = true;
      self.searching = true;
      self.$http.post('/1.5/props/autocompleteGetNext', data, {
        // use before callback
        before(request) {
          // abort previous request, if exists
          if (this.previousRequest) {
            this.previousRequest.abort();
          }
          // set previous request on Vue instance
          this.previousRequest = request;
        },
        _timeout:8000
      }).then(
        function (ret) {
          ret = ret.data;
          if (ret.ok) {
            self.autocompletes = ret.l;
          } else {
            self.autocompletes = [];
          }
          if (ret.needLogin) {
            self.needLogin = true;
          }
          self.tabs[0].cnt = self.autocompletes.length;
          if(vars.isMLS){
            self.searching = false;
            return ;
          }
          if (self.autocompletes.length == 0) {
            self.tab = 'new condos';
            self.getProjects();
          } else {
            setTimeout(()=>{
              self.getProjects();
            },1000);
          }
        },
        function (err) {
          self.autocompletes = [];
          ajaxError(err);
        }
      );
    }
  }
};
</script>
<style scope>
#autocompleteSearchBar > .search-container-input{
  max-width: calc(100% - 30px);
  margin: 0 15px;
}
.search-container-input {
	position: relative;
  width: 100%;
  max-width: 400px;
}
.search-container-input.home {
  max-width: 100%;
}
#searchAutocomplete {
	width: 100%;
	padding: 10px 30px;
	border-radius: 3px;
	outline: none;
  border: none;
  font-size: 17px;
  -webkit-appearance:none;
  border:1px solid rgba(0,0,0,.1);
  box-shadow: 0 0 0 1px rgba(0,0,0,.02),0 1px 1px 0 rgba(0,0,0,.08);
}
.newCondosList #searchAutocomplete {
  padding:5px 30px;
}
.searchInputIcon {
	position:absolute;
	font-size:16px;
	color:rgb(185,185,185);
	left:10px;
  top:50%;
  transform: translateY(-50%);
}
.searchClear {
  font-size:16px;
  color:rgb(185,185,185);
  position:absolute;
  right:10px;
  top:50%;
  transform: translateY(-50%);
  cursor: pointer;
}
.searchResult {
  border: 1px solid rgba(0,0,0,.1);
  position: absolute;
  background-color: #fff;
  z-index: 1;
  width: 100%;
	top:42px;
  height: calc(100vh - 40px - 41px);
	overflow: hidden;
	overflow-y: scroll;
}
.searchResultEmpty {
  padding:10px 15px;
}
.searchResultImgContainer {
  width:80px;
  position: relative;
}
.searchResultSid {
  font-size:10px;
  text-align:center;
  position:absolute;
  bottom:0;
  left:0;
  width:100%;
  padding:2px;
  color:#fff;
  background-color:rgba(0,0,0,0.5);
}
.searchResultImg {
  width:80px;
  height:60px;
}
.searchResultTabs {
  margin-bottom:10px;
  padding:10px 15px;
}
.searchResultTab {
	color:#443f3f;
  cursor: pointer;
  margin-right:15px;
  padding-bottom:4px;
}
.searchResultTab:hover {
	color:#d65050;
}
.searchResultTab.active {
	color:#d65050;
  border-bottom:3px solid rgb(255,90,90);
}
.searchResultTabTl {
  margin-right:2px;
  text-transform: capitalize;
  display: inline-block;
}
.searchResultItem {
  align-items: flex-start;
  position: relative;
  border-bottom:0.5px solid rgb(240,240,240);
  display: flex;
  cursor: pointer;
  padding:15px;
  transition: 0.3s;
}
.searchResultItem:hover {
  background-color:#f9f9f9;
  padding-left: 20px;
}
.searchResultActions {
  height: 100%;
  position: absolute;
  right:15px;
  display: flex;
  align-items: center;
}
.searchResultActions .fa {
  cursor: pointer;
  font-size:20px;
}
.searchResultActions .fa-map-marker {
  margin-right:20px;
}
.searchResultItemNeedLogin {
  position: absolute;
  width:100%;
  height: 100%;
  background-color: rgba(255,255,255,0.8);
  display:flex;
  align-items: center;
  justify-content: center;
  color:#d23933;
  top: 50%;
  transform: translateY(-50%);
}
.searchResultDetail {
  margin-left: 15px;
}
.searchResultAddr {
  line-height: 1;
}
.searchResultPrice {
  color:rgb(250,62,62);
  font-size:14px;
  font-weight:bold;
}
.searchResultSold {
  margin-left:2px;
  color:rgb(177,177,177);
  font-size:10px;
  font-weight:bold;
  text-decoration:line-through;
}
.searchResultType {
  background-color:rgb(255,90,90);
  border-radius:1px;
  text-transform:capitalize;
  margin-left:4px;
  color:#fff;
  padding:0 3px;
  font-size:10px;
}
.searchResultRooms {
	display:flex;
}
.searchResultRoom {
  font-size:14px;
  color:#6D6D6D;
}
.searchResultRoomIcon {
  
}
.searchResultRoomNum {
  margin:0 5px 0 3px;
}
.searchResultTp {
	width: calc(100% - 80px - 40px);
	overflow: hidden;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
}
.searchResultType.sale{
  background-color:rgb(255,90,90);
}
.searchResultType.rent{
  background-color:rgb(254,155,38);
}
.searchResultType.sold{
  background-color:rgb(199,199,199);
}
.searchResultType.leased{
  background-color:rgb(199,199,199);
}
.searchResultType.inactive{
  background-color:rgb(199,199,199);
}
.searchResultType.assignment{
  background-color:#4a148c;
}
.projNm {
  color:rgb(80,80,80);
  font-size:14px;
  margin-bottom:5px;
  font-weight:bold;
}
.projLoc {
  font-size:12px;
  color:rgb(177,177,177);
}
.searchResultSeeAll {
  padding:10px;
  text-align: center;
  cursor: pointer;
}
.fa-spinner {
  animation: lds-dual-ring 1.2s linear infinite;
  -webkit-animation: lds-dual-ring 1.2s linear infinite;
  -moz-animation: lds-dual-ring 1.2s linear infinite;
  -ms-animation: lds-dual-ring 1.2s linear infinite;
  -o-animation: lds-dual-ring 1.2s linear infinite;
}
@keyframes lds-dual-ring {
  0% {
    transform: rotate(0deg);
    -webkit-transform: rotate(0deg);
    -moz-transform: rotate(0deg);
    -o-transform: rotate(0deg);
    -ms-transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
    -webkit-transform: rotate(360deg);
    -moz-transform: rotate(360deg);
    -o-transform: rotate(360deg);
    -ms-transform: rotate(360deg);
  }
}
@media screen and (min-width: 768px) {
  .searchResult {
    height: auto;
    max-height: 400px;
  }
}
</style>