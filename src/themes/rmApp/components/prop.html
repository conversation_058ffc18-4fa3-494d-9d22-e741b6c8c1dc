{{&
  var {prop,lang,rcmdCity} = it;
  var his = prop.his;
  if (his && his.length > 0) {
    for (var i = his.length; i >= 0; i--) {
      if (his[i] && his[i].s == 'Pc') {
        prop.pc = his[i].c;
      }
    }
  }
  lang = lang || 'en';
}}
<div class="prop" onclick="openPopup('/1.5/prop/detail/inapp?lang={{=lang}}&id={{= (/^RM/.test(prop.id))?prop.id:prop._id}}','{{- RealMaster}}')">
  <div class="detail">
    <div class="addr one-line">
      {{? prop.daddr != 'N'}}
      <span>{{=prop.addr}}</span>
      {{??}}
      <span>{{=prop.city}} {{=prop.prov}} {{=prop.zip}}</span>
      {{?}}
    </div>
    {{? prop.daddr != 'N'}}
    <div class="prov">{{=prop.city}}, {{=prop.prov}}</div>
    {{?}}
    <div class="bdrms">
      {{? prop.bdrms}}
      <span><span class="fa fa-rmbed"></span> {{=prop.bdrms}}{{? prop.br_plus}}+{{=prop.br_plus}}{{??}}{{?}}</span>
      {{?}}
      {{? prop.rmbthrm || prop.tbthrms || prop.bthrms}}
      <span><span class="fa fa-rmbath"></span> {{=prop.rmbthrm || prop.tbthrms || prop.bthrms}}</span>
      {{?}}
      {{? prop.rmgr || prop.tgr || prop.gr}}
      <span><span class="fa fa-rmcar"></span> {{=prop.rmgr || prop.tgr || prop.gr}}</span>
      {{?}}
    </div>
    {{? prop.lp == '$0'}}
    <div class="price">{{- To Be Negotiated}}</div>
    {{??}}
    <div class="price">
      <span>{{=prop.lp}}</span>
      {{?prop.pc}}
      <span class="price-change">
        <span class="{{=prop.pc > 0?'plus':'mins'}}">{{=prop.pc>0?'+':'-'}}</span>
        ${{=Math.abs(prop.pc)}}
      </span>
      {{?}}
    </div>
    {{?}}
    {{? rcmdCity}}
    {{? prop.ltp == 'exlisting'}}
    <div class="exlinks" onclick="showMoreProps(event,{o:'{{=rcmdCity.o}}',p:'{{=rcmdCity.p}}',pn:'{{=rcmdCity.pn}}',n:'{{=rcmdCity.n}}'},{tp:'exlisting'})">{{- Exclusive}}</div>
    {{?}}
    {{? prop.ltp == 'assignment'}}
    <div class="exlinks" onclick="showMoreProps(event,{o:'{{=rcmdCity.o}}',p:'{{=rcmdCity.p}}',pn:'{{=rcmdCity.pn}}',n:'{{=rcmdCity.n}}'},{tp:'assignment'})">{{- Assignment}}</div>
    {{?}}
    {{? (prop.ltp == 'rent') && (!prop.cmstn)}}
    <div class="exlinks" onclick="showMoreProps(event,{o:'{{=rcmdCity.o}}',p:'{{=rcmdCity.p}}',pn:'{{=rcmdCity.pn}}',n:'{{=rcmdCity.n}}'},{tp:'rent'})">{{- Landlord Rental}}</div>
    {{?}}
    {{? (prop.ltp == 'rent') && (prop.cmstn)}}
    <div class="exlinks" onclick="showMoreProps(event,{o:'{{=rcmdCity.o}}',p:'{{=rcmdCity.p}}',pn:'{{=rcmdCity.pn}}',n:'{{=rcmdCity.n}}'},{tp:'exrent'})">{{- Exclusive Rental}}</div>
    {{?}}
    {{?}}
    {{? prop.ohdate}}
    <div class="oh"><span class="fa fa-rmhistory"></span>{{- Open House}}: {{=prop.ohdate}}</div>
    {{?}}
  </div>
  <div class="img">
    <img class="lazy" data-src={{=prop.thumbUrl}} referrerpolicy="same-origin" />
    <div class="tag">
      {{? prop.ytvid || prop.vurlcn}}
      <span class="vid"><span class="fa fa-youtube-play"></span></span>
      {{?}}
      {{? prop.ltp == 'exlisting'}}
      <span class="ltp">{{- Exclusive}}</span>
      {{?}}
      {{? prop.ltp == 'assignment'}}
      <span class="ltp">{{- Assignment}}</span>
      {{?}}
      {{? prop.ltp == 'rent'}}
      <span class="ltp">{{- Rental}}</span>
      {{?}}
      {{? prop.type}}
      <span class="type">{{=prop.type}}</span>
      {{?}}
      {{? prop.saletp}}
      <span class="">{{=prop.saletp[0]}}</span>
      {{?}}
    </div>
  </div>
</div>