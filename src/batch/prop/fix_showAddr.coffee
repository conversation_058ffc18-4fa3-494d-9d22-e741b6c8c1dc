###
Description:    修复房源showAddr字段问题，重新计算并更新showAddr字段
Usage:          ./start.sh -n fix_showAddr -cmd "lib/batchBase.coffee batch/prop/fix_showAddr.coffee dryrun -d [YYYY-MM-DD]"
Create date:    2025-08-14
Author:         <PERSON><PERSON>
Run frequency:  Once
###

debug = DEBUG()
speed = INCLUDE 'lib.speed'
helpers = INCLUDE 'lib.helpers'
{formatProvAndCity} = INCLUDE 'lib.propAddress'
PropertiesCol = COLLECTION 'vow', 'properties'

speedMeter = speed.createSpeedMeter {
  intervalTriggerCount: 5000,
  intervalCallback: (speedMeter) ->
    debug.info speedMeter.toString()
}

yargs = require('yargs')(AVGS)
yargs.option 'date', { alias: 'd', description: 'query date gte ts,formate:YYYY-MM-DD', required: false, type: 'string'}
dryRun = AVGS.indexOf('dryrun') >= 0
startTs = new Date()

main = () ->
  # 查询非RM房源
  query = {
    src: {$ne: 'RM'}
  }
  if yargs.argv.date
    query.ts = {$gte:new Date(yargs.argv.date)}

  projection = {
    addr: 1
    origAddr: 1
    unt: 1
    origUnt: 1
    ptype2: 1
    saletp: 1
    mfee: 1
    showAddr: 1
    city: 1
    prov: 1
    cnty: 1
    cmty: 1
    zip: 1
    st: 1
    st_num: 1
    st_dir: 1
    st_sfx: 1
    src: 1
  }
  
  cur = await PropertiesCol.find query, {projection}
  cur.maxTimeMS 3600000 # 设置查询超时时间为1小时
  stream = cur.stream()
  
  obj =
    verbose: 1
    stream: stream
    high: 30
    end: (err) ->
      processTs = (new Date().getTime() - startTs.getTime())/(1000*60)
      if err
        debug.error "Error exit. Total process time #{processTs} mins. ", speedMeter.toString()
        return EXIT 1, err
      debug.info "Completed, Total process time #{processTs} mins, #{speedMeter.toString()}"
      EXIT 0
    process: (prop, cb) ->
      speedMeter.check { processed: 1 }

      # 保存原始showAddr用于比较
      originalShowAddr = prop.showAddr

      # 创建一个副本用于处理，避免修改原始对象
      propCopy = Object.assign({}, prop)
      delete propCopy.showAddr

      
      if propCopy.origAddr
        # TRB/BRE/DDF/CAR房源需要对origAddr拆分后计算
        if propCopy.src in ['DDF','TRB','BRE','CAR']
          delete propCopy.addr
        else
          propCopy.addr = propCopy.origAddr
      propCopy.unt = propCopy.origUnt if propCopy.origUnt
      propCopy.city = propCopy.origCity if propCopy.origCity
      
      try
        # 使用formatProvAndCity函数重新计算showAddr
        formatProvAndCity(propCopy)
        newShowAddr = propCopy.showAddr
      catch err
        debug.error "Failed to format prop #{prop._id}", err
        speedMeter.check { formatError: 1 }
        return cb()

      # 检查是否需要更新
      if newShowAddr is originalShowAddr
        speedMeter.check { noChange: 1 }
        return cb()

      update = {noModifyMt: true, $set: {showAddr: newShowAddr}}

      if dryRun
        debug.info "dryRun mode, skip update, propId:#{prop._id}, " +
          "old:#{originalShowAddr}, new:#{newShowAddr}"
        speedMeter.check { dryRun: 1 }
        return cb()

      try
        await PropertiesCol.updateOne {_id: prop._id}, update
        speedMeter.check { updated: 1 }
      catch err
        debug.error "Failed to update prop #{prop._id}", err
        speedMeter.check { error: 1 }

      return cb()

  helpers.streaming obj
      
main()
