helpers_object = INCLUDE 'lib.helpers_object'
helpers_string = INCLUDE 'lib.helpers_string'
{ date2num } = INCLUDE 'lib.helpers_date'
{fetch} = INCLUDE 'lib.fetch'
DEFAULT_FETCH_OPTIONS = { method:'GET', timeout:3000 }
verbose = 1
cityHelper = INCLUDE 'lib.cityHelper'
debugHelper = INCLUDE 'lib.debug'
debug = debugHelper.getDebugger()
propAddress = INCLUDE 'lib.propAddress'
STREET_ABBR_CA_EN = require('../lib/streetAbbr/CA_EN').STREET_ABBR
{isSameFirstPartOfZip,isSimilarAddr} = INCLUDE 'libapp.properties'
UPDATE_GEOCACHE_WHEN_DIFF_IS_BIGGER_THEN = 0 #save geocache when input > geocache
mapServer = INCLUDE 'lib.mapServer'
{
  adjustGeocodingQuality,
  parseGoogleResult,
  parseBing<PERSON>esult,
  parseMapquestResult,
  parseHereResult,
  parseMapboxResult,
  parseAzureResult
} = INCLUDE 'libapp.geoCodeHelper'
geolib = require 'geolib'
GeoCache = COLLECTION 'vow', 'geo_cache'
GeoCacheOrig = COLLECTION 'vow', 'geoCacheOrig'
GeoStat = COLLECTION 'vow', 'geo_stat'
GeoStat.stats {},(err,stats)->
  if err
    throw err
  if not stats?.timeseries?
    throw new Error('geo_stat.timeseries is not found')

ChomeSysData = COLLECTION 'chome', 'sysdata'
GeoCacheDelCol = COLLECTION 'vow', 'geo_cache_del'
config = CONFIG(['google','mapbox','serverBase','here','mapquest','azureGeocoder'])

mapServer._config(config)

NO_GEOCODER = 'No geocoder'
NO_GEOCODING_INPUT = 'No GeoCoding input'
NO_UADDR = 'No uaddr'
NO_ID_FOR_CHECK_GEO_CACHE ='No id for check geo cache'
NO_QADDR = 'No qaddr'
NO_SUPPORT = 'Reverse geocoding not supported yet!'
NO_RESULTS = 'No result'
# NOT_SUPPORT_REVERSE_GEOCODING_FOR_HERE = 'Not support reverse geocoding using HERE engine'

CHECK_MAPSERVER_CONFIG = 'No map url or key found, please check mapServer config!'
GEO_CACHE_FIELDS = [
  'addr',
  'aUaddr',
  'avgLat',
  'avgLng',
  'bingGeocode',
  'city',
  'cmty',
  'cnty',
  'dec',
  'dist',
  'distAvgToG',
  'distOrigToG',
  'faddr',
  'geoConflict',
  'goLat',
  'goLng',
  'googleGeocode',
  'goq',
  'hereGeocode',
  'his',
  'lat',
  'lng',
  'mapquestGeocode',
  'nelat',
  'nelng',
  'nost',
  'origId',
  'pclass',
  'pcls',
  'prov',
  'ptype',
  'ptype2',
  'q',
  'qaddr',
  'refId',
  'rlat',
  'rlng',
  'rq',
  'rsrc',
  'src',
  'st',
  'st_num',
  'st_sfx',
  'swlat',
  'swlng',
  'uaddr',
  'zip',
]

AUTO_FIX_ADDR_RANGE_METER = 40
AUTO_FIX_ADDR_ST_ALLOWANCE_DIFF_RATIO = 0.1

gNoGeoCoding = false

saveToOriginalGeoCache = (type, param, result) ->
  return unless param.qaddr
  update = {
    qaddr: param.qaddr.toLowerCase(), # NOTE:大小写统一,提高正则查询效率
    mt: new Date()
  }
  update[type] = result
  GeoCacheOrig.updateOne { _id: param.qaddr },
    { $set: update, $setOnInsert: { ts: new Date() } }, { upsert: true }, (err, ret)->
      debug.error err if err

formatConflict = (oldRecord,newRecord)->
  list = {}
  list.$each =[
    {
      tp: oldRecord.src,
      lat: oldRecord.lat,
      lng: oldRecord.lng
    },
    {
      tp: newRecord.src,
      lat: newRecord.lat,
      lng: newRecord.lng
    }]
  list

###
 * 记录geocoding统计信息到数据库
 * @param {Object} options - 统计参数对象
 * @param {string} [options.src] - 数据来源，为空时默认为'reverseGeocoding'
 * @param {string} [options.engine] - geocoding engine
 * @param {number} [options.geoq] - geocoding quality score
 * @param {string|Object} [options.err] - 错误信息
 * @param {number} [options.noRs] - 无结果标记
 * @param {string} [options.id] - propId
###
recordGeocodingStats = ({
  src,
  engine,
  geoq,
  err,
  noRs,
  id
})->
  return unless GeoStat
  # NOTE: 当通过API调用时,src为空,此时认为是reverseGeocoding函数调用
  tmpSrc = src or 'reverseGeocoding'
  insertObj = {ts:new Date(),metadata:{src:tmpSrc,engine}}
  
  # 只添加非null/undefined的字段
  insertObj.geoq = geoq if geoq?
  insertObj.err = err if err?
  insertObj.noRs = noRs if noRs?
  insertObj.id = id if id?
  
  try
    await GeoStat.insertOne insertObj
  catch err
    debug.error "insertOne GeoStat Error #{err}",insertObj
  return

googleGeocode = ({qaddr,lat,lng,zip,src,id},cb)->
  debug.debug 'do googleGeocode',qaddr
  unless qaddr
    debug.error 'Error: googleGeocode',NO_QADDR,qaddr,lat,lng
    if lat and lng
      return cb 'Error: googleGeocode '+NO_SUPPORT
    return cb NO_QADDR

  gooleGeocodingUrl = mapServer.getGoogleAPILink({tp:'geocode'})
  return cb CHECK_MAPSERVER_CONFIG unless gooleGeocodingUrl
  #geocodingStat
  #_id:20220124_google, date:20220124, src:google,coded:$inc, errCnt:$inc, noRsCnt:
  statParam = {src,engine:'google',id}
  url = "#{gooleGeocodingUrl}&address=#{encodeURIComponent(qaddr)}"

  try
    response = await fetch url,DEFAULT_FETCH_OPTIONS
  catch err
    debug.error "googleGeocode request #{url} Error #{err}"
    return cb err.toString()
  
  try
    json = await response.json()
  catch e
    statParam.err = e.toString()
    recordGeocodingStats statParam
    return cb e.toString()

  if not response.ok
    debug.error "googleGeocode request #{url} Error ",json
    return cb json

  ret = {src:'google',q:0, qaddr:qaddr, zip}

  if json.status isnt 'OK'
    ret.err = json.status
    statParam.err = json.status
    recordGeocodingStats statParam
    return cb null, ret

  if not r = json.results?[0]
    ret.err = NO_RESULTS
    statParam.noRs = 1
    statParam.err = NO_RESULTS
    recordGeocodingStats statParam
    return cb null, ret

  parseGoogleResult r,ret
  
  try
    # debug.debug 'ret',ret
    propAddress.formatProvAndCity ret
  catch e
    debug.debug e, ret
    statParam.err = e.toString()
    recordGeocodingStats statParam
    return cb e

  adjustGeocodingQuality(ret, qaddr, zip)
  saveToOriginalGeoCache('google',ret,r)
  statParam.geoq = ret.q
  recordGeocodingStats statParam
  cb null,ret

# info@r             "Am4GGBWKOGnmjmYPmV-z_eUUIbuW3jyH5OcFeOn_e2ciPW4N3uwpGNpMdxUOsShU"
# fredxiang@g keys = "EoZkbTrUIkwLOvYMTtSm~ts43UmOwITwr_SN6zkaUxg~AhcLX3qFi7E9on4rbfkUFFk-MZEMiqFejuQlUjdJeSYrlxwEK4Hs3wQWa1A2IZql"
# allen       keys = "Aqag6UCzE6wWtze8KhCPbuB-Y_5pTyGXPe4Oqh43RXysJqhdIRMjIZoSwCDIkr1D"
# tao         keys = "yWdkHwMqUxW5uiBIqyXb~cLrdsxluEMon_ixU6e2W9A~AsnEWS_4lXCO8BIFkpjkA5Ey6VYkeJNI8qcDxQBDv9MX4QK2nGfeAdh3uNb2CM4l"
# bing key is saved in db, do not move to config.
bingKeys =
  "EoZkbTrUIkwLOvYMTtSm~ts43UmOwITwr_SN6zkaUxg~AhcLX3qFi7E9on4rbfkUFFk-MZEMiqFejuQlUjdJeSYrlxwEK4Hs3wQWa1A2IZql":1 # fredxiang
  "Aqag6UCzE6wWtze8KhCPbuB-Y_5pTyGXPe4Oqh43RXysJqhdIRMjIZoSwCDIkr1D":0  # allen
  # "yWdkHwMqUxW5uiBIqyXb~cLrdsxluEMon_ixU6e2W9A~AsnEWS_4lXCO8BIFkpjkA5Ey6VYkeJNI8qcDxQBDv9MX4QK2nGfeAdh3uNb2CM4l":0 # tao
  "AmQM0tIGgCoDg_rAhr9YAgIwowsZm74OLZhwyUMydBKcYd9iicBCNKJC9yRYwgPv":100 # realmasterca@outlook
BING_MAP_KEYS_ID = 'bingMapKeys'
curBingKey = 'AmQM0tIGgCoDg_rAhr9YAgIwowsZm74OLZhwyUMydBKcYd9iicBCNKJC9yRYwgPv'
curBingCnt = 0

bingGeocode = ({qaddr,lat,lng,zip,src,id},cb)->
  debug.debug 'do bingGeocode',qaddr,lat,lng
  unless qaddr
    debug.error 'Error: bingGeocode:',NO_QADDR,qaddr,lat,lng
    # TODO: support reverse geocode(geocode by point)
    # https://learn.microsoft.com/en-us/bingmaps/rest-services/locations/find-a-location-by-point
    if lat and lng
      return cb 'Error: bing '+NO_SUPPORT
    return cb NO_QADDR
  #url = "http://dev.virtualearth.net/REST/v1/Locations?o=json&key=Am4GGBWKOGnmjmYPmV-z_eUUIbuW3jyH5OcFeOn_e2ciPW4N3uwpGNpMdxUOsShU&q=" + encodeURIComponent(param.qaddr)
  url = "https://dev.virtualearth.net/REST/v1/Locations?o=json&inclnb=1&key=\
    #{curBingKey}\&q=" + encodeURIComponent(qaddr)

  statParam = {src,engine:'bing',id}
  try
    response = await fetch url,DEFAULT_FETCH_OPTIONS
  catch err
    debug.error "bingGeocode request #{url} Error #{err}"
    return cb err.toString()

  incBingKey()
  try
    json = await response.json()
  catch e
    statParam.err = e.toString()
    recordGeocodingStats statParam
    return cb e.toString()

  if not response.ok
    debug.error "bingGeocode request #{url} Error ",json
    return cb json
    
  ret = {src:'bing',q:0,qaddr:qaddr,zip}

  if json.statusDescription isnt 'OK'
    ret.err = json.statusDescription
    statParam.err = json.statusDescription
    recordGeocodingStats statParam
    return cb ret

  if json.authenticationResultCode isnt 'ValidCredentials'
    ret.err = json.authenticationResultCode
    statParam.err = json.authenticationResultCode
    recordGeocodingStats statParam
    return cb ret

  unless r = json.resourceSets?[0]?.resources?[0]
    ret.err = NO_RESULTS
    statParam.noRs = 1
    statParam.err = NO_RESULTS
    recordGeocodingStats statParam
    return cb ret
  
  parseBingResult r,ret

  try
    propAddress.formatProvAndCity ret
  catch e
    debug.error e, ret
    statParam.err = e.toString()
    recordGeocodingStats statParam
    return cb e
  adjustGeocodingQuality(ret, qaddr, zip)
  saveToOriginalGeoCache('bing',ret,r)
  statParam.geoq = ret.q
  recordGeocodingStats statParam

  cb null,ret

#bing has addr,but no st_num
# NOTE: unused
mapquestGeocode = ({qaddr,lat,lng,zip,src,id},cb)->
  return cb() unless qaddr
  unless qaddr
    debug.error 'mapquestGeocode: ',NO_QADDR,qaddr,lat,lng
    if lat and lng
      return cb 'Error: mapquest '+NO_SUPPORT
    return cb NO_QADDR
  key = mapServer.getMapquestAPIKey()
  return cb CHECK_MAPSERVER_CONFIG unless key
  statParam = {src,engine:'mapquest',id}
  #********************************
  url = "https://www.mapquestapi.com/geocoding/v1/address?\
    key=#{key}&\
    language=en&location=#{encodeURIComponent(qaddr)}"

  try
    response = await fetch url,DEFAULT_FETCH_OPTIONS
  catch err
    debug.error "mapquestGeocode request #{url} Error #{err}"
    return cb err.toString()

  if /You\shave\sexceeded\sthe\snumber\sof\smonthly\stransactions/.test response.data
    #geocoders.pop() # remove last geoCoder,geocoders is not global.
    # geocoders = [bingGeocode,googleGeocode]
    return cb response.data

  try
    json = await response.json()
  catch e
    statParam.err = e.toString()
    recordGeocodingStats statParam
    return cb e.toString()

  if not response.ok
    debug.error "mapquestGeocode request #{url} Error ",json
    return cb json

  ret =
    src:'mapquest',
    q:0,
    qaddr:qaddr

  if json.info?.statuscode isnt 0
    ret.err = json.info?.message?.toString() or 'Error'
    statParam.err = json.info?.message?.toString() or 'Error'
    recordGeocodingStats statParam
    return cb null, ret

  unless r = json.results?[0]
    ret.err = NO_RESULTS
    statParam.noRs = 1
    statParam.err = NO_RESULTS
    recordGeocodingStats statParam
    return cb null, ret
  parseMapquestResult r,ret
  try
    propAddress.formatProvAndCity ret
  catch e
    debug.error e, ret
    statParam.err = e.toString()
    recordGeocodingStats statParam
    return cb e
  adjustGeocodingQuality(ret, qaddr, zip)
  saveToOriginalGeoCache('mapquest',ret,json)
  statParam.geoq = ret.q
  recordGeocodingStats statParam
  cb null,ret

hereGeocode = ({qaddr=null,lat=null,lng=null,zip,src,id},cb)->
  hereAppCode = mapServer.getHereAPIKey()
  debug.debug 'hereAppCode',hereAppCode
  debug.debug "do hereGeocode #{qaddr}"
  statParam = {src,engine:'here',id}
  return cb CHECK_MAPSERVER_CONFIG unless hereAppCode

  if qaddr
    url = "https://geocode.search.hereapi.com/v1/geocode?q=#{encodeURIComponent(qaddr)}&apiKey=#{hereAppCode}&lang=en"
  else if lat and lng
    return cb('Error: here '+NO_SUPPORT)
  else
    return cb(NO_QADDR)
  debug.debug url
  try
    response = await fetch url,DEFAULT_FETCH_OPTIONS
  catch err
    debug.error "hereGeocode request #{url} Error #{err}"
    return cb err.toString()

  try
    json = await response.json()
  catch e
    statParam.err = e.toString()
    recordGeocodingStats statParam
    return cb e.toString()

  if not response.ok
    debug.error "hereGeocode request #{url} Error ",json
    return cb json

  ret = {src:'here',q:0,qaddr:qaddr,zip}
  # unless r = json?.Response?.View?[0]?.Result?[0]
  unless r = json?.items?[0]
    ret.err = json?.error or NO_RESULTS
    saveToOriginalGeoCache('here',ret,json)
    if json?.error
      statParam.err = json.error or NO_RESULTS
    else
      statParam.noRs = 1
    recordGeocodingStats statParam
    return cb null, ret
  parseHereResult r,ret
  try
    propAddress.formatProvAndCity ret
  catch e
    debug.error e, ret
    statParam.err = e.toString()
    recordGeocodingStats statParam
    return cb e
  # debug.debug 'saveToOriginalGeoCache'
  adjustGeocodingQuality(ret, qaddr, zip)
  saveToOriginalGeoCache('here',ret,json)
  statParam.geoq = ret.q
  recordGeocodingStats statParam
  cb null,ret

#mapboxKey = "pk.eyJ1IjoibWF0dGZpY2tlIiwiYSI6ImNqNnM2YmFoNzAwcTMzM214NTB1NHdwbnoifQ.Or19S7KmYPHW8YjRz82v6g"
mapboxGeocode = ({qaddr=null,lat=null,lng=null,zip,src,id},cb)->
  mapboxKey = mapServer.getMapboxAPIKey({isApp:true})
  
  return cb CHECK_MAPSERVER_CONFIG unless mapboxKey

  statParam = {src,engine:'mapbox',id}
  debug.debug 'mapboxKey',mapboxKey
  if qaddr
    url = "https://api.mapbox.com/geocoding/v5/mapbox.places/\
      #{encodeURIComponent(qaddr)}.json?access_token=\
      #{mapboxKey}&country=ca&language=en"
  else if lng and lat
    url = "https://api.mapbox.com/geocoding/v5/mapbox.places/#{lng},\
      #{lat}.json?access_token=#{mapboxKey}&country=ca&language=en"
  else
    return cb(NO_QADDR)

  try
    response = await fetch url,DEFAULT_FETCH_OPTIONS
  catch err
    debug.error "mapboxGeocode request #{url} Error #{err}"
    return cb err.toString()

  try
    json = await response.json()
  catch e
    statParam.err = e.toString()
    recordGeocodingStats statParam
    return cb e.toString()

  if not response.ok
    debug.error "mapboxGeocode request #{url} Error ",json
    return cb json

  ret = {src:'mapbox',q:0,qaddr:qaddr,zip}
  unless r = json?.features?[0]
    ret.err = NO_RESULTS
    statParam.noRs = 1
    statParam.err = NO_RESULTS
    recordGeocodingStats statParam
    return cb null, ret

  parseMapboxResult r,ret
  
  try
    propAddress.formatProvAndCity ret
  catch e
    debug.error e, ret
    statParam.err = e.toString()
    recordGeocodingStats statParam
    return cb e
  adjustGeocodingQuality(ret, qaddr, zip)
  saveToOriginalGeoCache('mapbox',ret,json)
  statParam.geoq = ret.q
  recordGeocodingStats statParam
  cb null,ret

azureGeocode = ({qaddr,lat,lng,zip,src,id},cb)->
  debug.debug 'do azureGeocode',qaddr
  unless (qaddr or (lat? and lng?))
    debug.error 'Error: azureGeocode',NO_QADDR,qaddr,lat,lng
    return cb NO_QADDR

  azureAPIKey = mapServer.getAzureAPIKey()
  return cb CHECK_MAPSERVER_CONFIG unless azureAPIKey
  
  isQaddr = false
  statParam = {src,engine:'azure',id}
  if qaddr
    isQaddr = true
    url = "https://atlas.microsoft.com/search/address/json?api-version=1.0&subscription-key=#{azureAPIKey}&countrySet=CA&language=en-US&query=#{encodeURIComponent(qaddr)}"
  else if lat and lng
    url = "https://atlas.microsoft.com/search/address/reverse/json?api-version=1.0&subscription-key=#{azureAPIKey}&countrySet=CA&language=en-US&query=#{lat},#{lng}"
  
  try
    response = await fetch url,DEFAULT_FETCH_OPTIONS
  catch err
    debug.error "azureGeocode request #{url} Error #{err}"
    return cb err.toString()
  
  try
    json = await response.json()
  catch e
    statParam.err = e.toString()
    recordGeocodingStats statParam
    return cb e.toString()

  if not response.ok
    debug.error "azureGeocode request #{url} Error ",json
    return cb json

  ret = {src:'azure',q:0, qaddr:qaddr, zip}

  if json.error
    ret.err = json.error.message or json.error.code
    statParam.err = ret.err
    recordGeocodingStats statParam
    return cb null, ret

  if isQaddr
    r = json.results?[0]
  else
    r = json.addresses?[0]

  if not r
    ret.err = NO_RESULTS
    statParam.noRs = 1
    statParam.err = NO_RESULTS
    recordGeocodingStats statParam
    return cb null, ret

  parseAzureResult r,ret
  
  try
    propAddress.formatProvAndCity ret
  catch e
    debug.debug e, ret
    statParam.err = e.toString()
    recordGeocodingStats statParam
    return cb e

  adjustGeocodingQuality(ret, qaddr, zip)
  saveToOriginalGeoCache('azure',ret,r)
  statParam.geoq = ret.q
  recordGeocodingStats statParam
  cb null,ret

getGeocoders = (engineType) ->
  if engineType is 'publicApi'
    return [hereGeocode, mapboxGeocode]
  else if ret = GEO_CODER_ENGINE_MAPPING[engineType]
    return [ret]
  else
    return [googleGeocode, hereGeocode, azureGeocode, mapboxGeocode]

GEO_CODER_ENGINE_MAPPING =
  'azure': azureGeocode
  'mapbox': mapboxGeocode
  'bing': bingGeocode
  'here': hereGeocode
  'google': googleGeocode
  'mapquest': mapquestGeocode

selBingKey = () ->
  ChomeSysData.findOne { _id: BING_MAP_KEYS_ID }, (err, sysBingkeys) ->
    if err then return debug.error 'selBingKey error', err
    keys = sysBingkeys?.keys or []
    # invalidKeys = sysBingkeys?.invalidKeys or []
    bKeys = helpers_object.shallowExtendObject {},bingKeys
    # get all keys first
    for k in keys
      delete bKeys[k.k]
    for k,v of bKeys #bKeys 需要update的key。
      keys.push {k:k,c:v}
    # select least used

    # debug.debug 'bingkeys beofore sort',keys
    keys.sort (a,b)-> a.c - b.c
    # debug.debug 'bingkeys',keys
    toUse = keys[0]
    # save keys
    toSave = { keys: keys }
    ChomeSysData.updateOne { _id: BING_MAP_KEYS_ID }, { $set: toSave }, {  upsert:true }, (err) ->
      if err then return debug.error 'selBingKey error', err
      curBingKey = toUse.k
      curBingCnt = 0

incBingKey = () ->
  curBingCnt++
  if curBingCnt >= 50
    ChomeSysData.updateOne { _id: BING_MAP_KEYS_ID, 'keys.k': curBingKey }, \
    { $inc: { 'keys.$.c': curBingCnt }}, (err)->
      if err then return debug.error 'incBingKey error', err
      curBingCnt = 0
      selBingKey()

checkAndformatGeoInput = (obj)->
  error = null
  uaddrBeforeFormat = helpers_string.unifyAddress obj
  debug.debug 'uaddrBeforeFormat', uaddrBeforeFormat
  unless uaddrBeforeFormat
    debug.error NO_UADDR, obj
    error = NO_UADDR
  if obj.zip
    uaddrZipBeforeFormat = helpers_string.unifyAddress obj,true
    debug.debug 'uaddrZipBeforeFormat', uaddrZipBeforeFormat


  try
    propAddress.formatProvAndCity obj
  catch err
    debug.error err,obj
    error = err
  #format之后没有uaddr，数据不全
  unless uaddrAfterFormat = helpers_string.unifyAddress obj
    debug.error NO_UADDR, obj
    error = NO_UADDR
  debug.debug 'uaddrAfterFormat', uaddrAfterFormat
  if obj.zip
    uaddrZipAfterFormat = helpers_string.unifyAddress obj,true
    debug.debug 'uaddrZipAfterFormat', uaddrZipAfterFormat

  return {error, uaddrBeforeFormat, uaddrZipBeforeFormat, uaddrAfterFormat, uaddrZipAfterFormat}

dateDaysBeforeNow = (validDays=180)->
  # 86400000 = 24*3600*1000 = 1 day
  return new Date(Date.now() - validDays * 86400000)

###
# 检查给定的zip是否与geoCache中的zip前三位匹配
#
# @param {Object} geoCache - 地理缓存对象，包含zip和可能的zipArr字段
# @param {String} zip - 需要比较的zip码
# @returns {Boolean} - 如果zip码匹配返回true，否则返回false
#
# 匹配规则：
# 1. 首先检查geoCache和zip是否有效
# 2. 检查geoCache.zip与输入zip是否匹配（使用isSameFirstPartOfZip比较前三位）
# 3. 如果不匹配，则检查geoCache.zipArr数组中是否有匹配项
# 4. 如果都不匹配，返回false
###
isSameZip3WithGeoCache = (geoCache,zip)->
  # 当zip不存在时，返回true
  return true if not zip?
  # 当geoCache不存在时，error log并返回false
  if not geoCache?
    debug.error 'geoCache is null'
    return false
  # geoCache如果没有zip信息时，error log并则返回false
  if (not geoCache.zip?) and (not geoCache.zipArr?.length)
    debug.error "geoCache #{geoCache._id} has no zip info"
    return false
  
  # 首先使用isSameFirstPartOfZip判断geoCache.zip和zip是否相同
  return true if geoCache.zip? and isSameFirstPartOfZip(geoCache.zip, zip)
  
  # 如果geoCache.zip与zip不同，检查zipArr数组中是否有匹配项
  if geoCache.zipArr?.length > 0
    for cacheZip in geoCache.zipArr
      return true if isSameFirstPartOfZip(cacheZip, zip)
  
  # 如果都不匹配，返回false
  return false
  
# 使用rlat/rlng替换lat/lng
replaceWithReverseGeocodingResult = (record) ->
  if record?.rlat and record?.rlng and record?.rq
    record.lat = record.rlat
    record.lng = record.rlng
    record.q = record.rq
  return record

#query aUaddrs和uaddr的组合。
checkGeoCache = ({uaddrAfterFormat, uaddrZipAfterFormat, propZip}, cb) ->
  return cb NO_ID_FOR_CHECK_GEO_CACHE  unless uaddrAfterFormat
  # query = { _id: uaddrAfterFormat }
  # NOTE:改为aUaddr查询(_id会放在aUaddr中)
  if propZip and uaddrZipAfterFormat
    query = { aUaddr:{$in:[uaddrAfterFormat,uaddrZipAfterFormat]} }
  else
    query = { aUaddr:uaddrAfterFormat }
  debug.debug 'checkGeoCache query',query
  
  try
    results = await GeoCache.findToArray query,{fields:{err:0}}
  catch err
    return cb err
  return cb null,{} unless results?.length

  # 去掉半年以前的质量低的数据,区分含有zip的和不含zip的记录
  halfYearAgo = dateDaysBeforeNow 180
  results = results.filter (item)->
    tooOld = (not item.mt) or (new Date(item.mt) < halfYearAgo)
    return item.q >= 100 or (not tooOld)

  getBestResult = (list)->
    for item in list
      uaddrParts = propAddress.decodeFromUaddr item._id
      # 含有zip3的记录优先
      if uaddrParts?.zip
        return item
      # geo_cache的zip3要与传入的zip3一致
      else if uaddrParts?.addr and (isSameZip3WithGeoCache(item, propZip))
        result = item
      else
        debug.error "uaddr:#{item._id} is invalid or zip3:#{item.zip} is not equal prop.zip3:#{propZip}"
    return result
      
  result = getBestResult results
  result = replaceWithReverseGeocodingResult(result)

  return cb null,result or {}

#输入，qaddr或者lat，lng，得到结果，或者error
#所有的原始结果，都要存，error和result
#parse之后的结果，所有engine的结果存q或者error，存best record
#所有的error都是time out的不保存。error code。
geoCodeOneWithEngine = ({ engineType, qaddr, lat, lng, tryAll , zip,src,id}, cb) ->
  # debug.info 'geoCodeOneWithEngine', qaddr, lat,lng
  if not (qaddr or (lat and lng))
    debug.warn NO_GEOCODING_INPUT
    return cb(null, { err: NO_GEOCODING_INPUT })
  geocoders = getGeocoders(engineType)
  gi = 0
  rs = []
  errAndQ = {}
  # find the best one, save geocoding = 100 的。
  done = (r)->
    bestRecord = null
    if Object.keys(errAndQ).length is 0
      return cb null, {err: NO_GEOCODER}
    if r # find the one q=100 and has valid zip，r的cnty，prov格式不一定一样。
      bestRecord = r
    else
      for r in rs
        if ('object' is typeof r) and r.q > 0
          if (not bestRecord?) or (bestRecord.q < r.q)
            bestRecord = r
          # sometime other providers has better/full zip result
          if (not bestRecord.zip?) or \
            (r.zip? and (bestRecord.zip.length < r.zip.length))
              bestRecord.zip = r.zip
    return cb null,Object.assign {},bestRecord,errAndQ

  doOne = ->
    if gi < geocoders.length
      gc = geocoders[gi++] # 在每个engine里check信息，信息不全的降低q，自定义q
      gc {qaddr,lat,lng,zip,src,id},(err,ret)->
        if err or not ret? # if no result, 继续下一个。rs.push('No Result')
          debug.debug "No Result for #{qaddr}:\
            (or lat #{lat},lng:#{lng}) #{gc.name}",'err:',err
          # rs.push err or 'No Result'
          errAndQ[gc.name] = err or 'No Result'
          return doOne()
        #if ret is not good enough, 继续下一个。rs.push(ret)
        ret.qaddr = qaddr
        # locality字段去重
        if ret.locality?.length
          ret.locality = Array.from(new Set(ret.locality))
        else
          delete ret.locality
        rs.push ret
        errAndQ[gc.name] = ret.q
        if (ret.q < 100) or (not ret.zip?) or (ret.zip.length < 5) or tryAll
          return doOne()
        else
          done ret
    else
      done()
  
  doOne()

###
# @description 判断房源prop和geoCache记录的address是否相似
# @params {object} - prop 房源信息
# @params {object} - geoCache geoCache记录
# @return {boolean} - address相似时返回true,否则返回false
###
isSimilarAddrOfPropAndGeo = (prop={},geoCache={})->
  # 检查st_num是否一致
  return false unless prop.st_num?.toString() is geoCache.st_num?.toString()
  
  # 如果没有st_dir/st_sfx 需要通过addr重新decode
  if (not prop.st_dir) or (not prop.st_sfx)
    {st_sfx,st_dir,addrArr} = propAddress.decodeSfxAndDirFromAddr prop.addr
    prop.st_sfx ?= st_sfx if st_sfx
    prop.st_dir ?= st_dir if st_dir
  if (not geoCache.st_dir) or (not geoCache.st_sfx)
    {st_sfx,st_dir,addrArr} = propAddress.decodeSfxAndDirFromAddr geoCache.addr
    geoCache.st_sfx ?= st_sfx if st_sfx
    geoCache.st_dir ?= st_dir if st_dir

  return false unless prop.st_sfx?.toUpperCase() is geoCache.st_sfx?.toUpperCase()
  return false unless prop.st_dir?.toUpperCase() is geoCache.st_dir?.toUpperCase()

  # 没有st时,使用addr检查相似度
  if (not prop.st) or (not geoCache.st)
    propSt = prop.addr
    geoCacheSt = geoCache.addr
  else
    propSt = prop.st
    geoCacheSt = geoCache.st
  # 判断是否相似
  if not (isSimilarAddr propSt,geoCacheSt)
    return false
  return true

###
addr 比对逻辑
'111 Elizabeth St' and '111 Elizabeths St' is match (st 字段 90% 的内容相同)
'123 Spring Garden Ave' and '123 Spring Ave' is match (st 字段可分为两个部分，少了一段，但其中一段完全相同)
'111 Elizabeth St W' and '111 Elizabeth St' is match (st_dir 不影响判断)
'111 Elizabeth St' and '112 Elizabeth St' is NOT match (st_num 需要完全相同)
###
fixAddrByFindSimilarAddrInGeoCacheResults = (prop, geoResult) ->
  if not (geoResult?.lat and geoResult?.lng)
    return false
  prop.st_dir = propAddress.DIRECTION_ABBR[prop.st_dir.toString().toUpperCase()] if prop.st_dir
  prop.st_sfx = STREET_ABBR_CA_EN[prop.st_sfx.toString().toUpperCase()] if prop.st_sfx
  [sw, ne] = geolib.getBoundsOfDistance {
    latitude: geoResult.lat,
    longitude: geoResult.lng
  }, AUTO_FIX_ADDR_RANGE_METER
  nearByGeoCaches = await GeoCache.findToArray {
    lat: { $gte: sw.latitude, $lte: ne.latitude },
    lng: { $gte: sw.longitude, $lte: ne.longitude },
  },{sort:{q:-1}}

  for geo in nearByGeoCaches
    if geo.q < geoResult.q
      continue

    geo.st_dir = propAddress.DIRECTION_ABBR[geo.st_dir.toString().toUpperCase()] if geo.st_dir
    if geo.dir and (not geo.st_dir)
      geo.st_dir = propAddress.DIRECTION_ABBR[geo.dir.toString().toUpperCase()]
    geo.st_sfx = STREET_ABBR_CA_EN[geo.st_sfx.toString().toUpperCase()] if geo.st_sfx

    # 比较地址相似度,st_num,st,st_dir,st_sfx
    if not isSimilarAddrOfPropAndGeo(prop,geo)
      continue

    # 邮编前三位不一致
    if not isSameZip3WithGeoCache(geo, prop.zip)
      continue

    # NOTE: 仅需要修改uaddr,其它信息都不修改
    # TODO: geoCache的addr/st等添加到新字段,检索时两个地址都可以查到(@fred提出)
    # prop.addr = geo.addr
    # prop.st = geo.st
    # prop.st_num = geo.st_num
    # prop.st_sfx = geo.st_sfx
    prop.uaddr = geo._id
    prop.showAddr = geoResult.showAddr if geoResult.showAddr
    geoResult.fixAddr = true
    return geo._id
  return false

# @input aUaddrArray, afterFormatRecord.aUaddr, uaddrBeforeFormatRecord.aUaddr, geoUaddrRecord.aUaddr
# @returns an array that do not contain any record from src1, src2 and src3
removeDup = (dest=[], src1=[], src2=[], src3=[])->
  ret = []
  for aUaddr in dest
    if (aUaddr in src1) or (aUaddr in src2) or (aUaddr in src3)
      continue
    ret.push aUaddr
  ret

# not (forceUpdate or ((geoCodingResult.q >= (oldRecord.q or 0)) and isCoordinateDiff))
cacheIsBetter = (forceUpdate, q, oldQ=0, isCoordinateDiff)->
  if forceUpdate
    return false
  if (q >= oldQ) and isCoordinateDiff
    return false
  return true

# 获取添加zip后的uaddr
getZipUaddr = (uaddr,zip)->
  if not (uaddr and zip)
    return
  if not (uaddrParts = propAddress.decodeFromUaddr(uaddr))
    return
  # add geoCodingResult.zip
  uaddrParts.zip = zip
  return helpers_string.unifyAddress uaddrParts,true

###
# @description 检查并保存zip码到GeoCache记录中
# @param {Object} record - GeoCache记录
# @param {Array} zipArr - 需要保存的zip码数组
# @returns {Boolean} - 如果zipArr有更新则返回true，否则返回false
###
checkAndSaveZip = (record, zipArr=[]) ->
  return unless record?._id
  
  # 获取record中的zipArr字段，如果没有就生成一个空数组
  recordZipArr = record.zipArr or []
  recordZipArr.push(record.zip) if record.zip?
  
  # 合并zipArr并去重
  combinedZipArr = Array.from(new Set([...recordZipArr, ...zipArr]))
  
  # 如果zipArr没有变化，直接返回
  if helpers_object.equals recordZipArr,combinedZipArr
    return
    
  # 更新GeoCache表中对应记录的zipArr字段
  try
    await GeoCache.updateOne(
      { _id: record._id },
      { $set: { zipArr: combinedZipArr } },
    )
  catch err
    debug.error err, record._id, combinedZipArr
  
  return

class GeoCoderLocal
  @noGeocoding: (v)->
    gNoGeoCoding = v
    debug.info "set gNoGeoCoding #{gNoGeoCoding}"

  # 合并多个geo_cache records
  # TODO: 添加逻辑判断哪个地址更标准,merge后的_id,addr等字段需要使用更标准的内容
  @mergeGeoCache: (records = [])->
    if (not Array.isArray(records)) or (records.length < 2)
      return
    records = records.filter (r)->
      return r?._id? and r?.q? and r?.aUaddr?
    unless records.length
      debug.error 'records isnt geo_cache',records
      return
    
    # geoCache._id可能不在自己record.aUaddr中
    checkIdInAUaddr = (aUaddr = [],geoCache={},pullAuaddr={})->
      if not aUaddr.includes(geoCache._id)
        aUaddr.push geoCache._id
        rec = await GeoCache.findOne {aUaddr: geoCache._id},{projection:{_id:1,aUaddr:1}}
        if rec
          # NOTE:rec可能可以merge到bestRecord
          debug.warn "geoCacheId: #{geoCache._id} isnt in self's aUaddr, it in #{rec._id}"
          pullAuaddr[rec._id] = geoCache._id
      return aUaddr

    records.sort (a,b)-> b.q - a.q
    bestRecord = records.shift()
    bestRecord.pclass ?= []
    bestRecord.ptype2 ?= []
    mergedInfo = {}
    pullAuaddr = {}
    bestRecord.aUaddr =  await checkIdInAUaddr bestRecord.aUaddr,bestRecord,pullAuaddr
    for item in records
      mergedInfo[item._id] = item
      bestRecord.aUaddr = bestRecord.aUaddr.concat item.aUaddr
      bestRecord.aUaddr =  await checkIdInAUaddr bestRecord.aUaddr,item,pullAuaddr
      bestRecord.pclass.concat(item.pclass or [])
      bestRecord.ptype2.concat(item.ptype2 or [])
    arrFields = ['aUaddr','pclass','ptype2']
    # 数组字段去重
    for fld in arrFields
      bestRecord[fld] = Array.from(new Set(bestRecord[fld]))
    {transactionOptions,session} = GETTRANSACTION 'vow'
    try
      session.startTransaction(transactionOptions)
      for key,val of mergedInfo
        await GeoCache.deleteOne {_id:key},{session}
        await GeoCacheDelCol.updateOne {_id:key},{$set:val},{upsert:true,session}
      for key,val of pullAuaddr
        await GeoCache.updateOne {_id:key},{$pull:{aUaddr:val}},{session}
      await GeoCache.updateOne {_id:bestRecord._id},{$set:bestRecord},{session}
      await session.commitTransaction()
    catch err
      debug.error err,"mergedIds:#{Object.keys(mergedInfo)},","mergeInto:#{bestRecord._id}"
      await session.abortTransaction()
      throw err
    finally
      await session.endSession()

    return bestRecord

  @saveGeoCache: ({
    ptype,
    ptype2,
    geoCodingResult, #may not formatted.
    uaddrBeforeFormat,
    uaddrAfterFormat,
    forceUpdate,
    geoUaddr,
    uaddrZipBeforeFormat,
    uaddrZipAfterFormat,
    propZip
  }, cb) ->
    if not geoCodingResult
      debug.error 'no geoCodingResult',uaddrAfterFormat
      return cb 'no geoCodingResult'

    if not uaddrAfterFormat
      debug.error 'no valid geocache id after format',
        'geoCodingResult:',geoCodingResult
      return cb 'no valid geocache id'

    zipArr = []
    zipArr.push geoCodingResult.zip if geoCodingResult.zip
    zipArr.push propZip if propZip
    zipArr = Array.from(new Set(zipArr))

    _handleNoOldRecord = (object,aUaddr,cb)->
      # could happen insert at same time
      object.aUaddr = aUaddr
      object.ts = new Date()
      object.zipArr = zipArr if zipArr.length
      return GeoCache.insertOne object, (err, ret) ->
        if err
          if /E11000|duplicate\skey/ig.test err.toString()
            debug.debug 'duplicate key, ignore'
          else
            debug.error 'Error insert geoCache:',object,err
        return cb err, object

    _getOldRecord = ({uaddrAfterFormat,uaddrBeforeFormat,geoUaddr})->
      # NOTE: now aUaddr is unqiue, so cannot dup with Before and after format
      # aUaddr contains _id
      afterFormatRecord = null
      uaddrBeforeFormatRecord = null
      geoUaddrRecord = null
      oldRecord = null
      mergedIdMap = {}
      uaddrAfterFormatQuery = { $or:[
        {_id: uaddrAfterFormat},{aUaddr:uaddrAfterFormat}
      ]}
      aUaddrArray = [uaddrAfterFormat]

      # uaddrBeforeFormat存在且与uaddrAfterFormat不一致时需要查询uaddrBeforeFormat记录
      if uaddrBeforeFormat? and (uaddrBeforeFormat isnt uaddrAfterFormat)
        aUaddrArray.push uaddrBeforeFormat
        uaddrBeforeFormatQuery = { $or:[
          {_id: uaddrBeforeFormat},{aUaddr:uaddrBeforeFormat}
        ]}
      
      # geoUaddr存在且与uaddrAfterFormat不一致时需要查询geoUaddr记录
      if geoUaddr? and (geoUaddr isnt uaddrAfterFormat)
        aUaddrArray.push geoUaddr
        geoUaddrQuery = { $or:[
          {_id: geoUaddr},{aUaddr:geoUaddr}
        ]}
     
      # NOTE: new afterFormat_id could be in aUaddr of old record
      afterFormatRecord = await GeoCache.findOne uaddrAfterFormatQuery,{ fields: { err: 0 }}
      mergedIdMap[afterFormatRecord._id] = afterFormatRecord if afterFormatRecord
      if uaddrBeforeFormatQuery
        uaddrBeforeFormatRecord = await GeoCache.findOne uaddrBeforeFormatQuery,{ fields: { err: 0 }}
        mergedIdMap[uaddrBeforeFormatRecord._id] = uaddrBeforeFormatRecord if uaddrBeforeFormatRecord
      if geoUaddrQuery
        geoUaddrRecord = await GeoCache.findOne geoUaddrQuery,{ fields: { err: 0 }}
        mergedIdMap[geoUaddrRecord._id] = geoUaddrRecord if geoUaddrRecord
      oldRecord = geoUaddrRecord or afterFormatRecord or uaddrBeforeFormatRecord
      # no old record for either case, insert new.
      return {aUaddrArray,oldRecord,afterFormatRecord,uaddrBeforeFormatRecord,geoUaddrRecord,mergedIdMap}

    geoToSave = {}
    geoToSave.ptype = ptype if ptype
    geoToSave.ptype2 = ptype2 if ptype2
    geoToSave.mt = new Date()
    for c in GEO_CACHE_FIELDS
      geoToSave[c] = geoCodingResult[c] if geoCodingResult[c]?
    #for typo address，aUaddr is new，but _id can find in db
    recordToReturn = Object.assign {},geoToSave
    recordToReturn._id = if geoUaddr then geoUaddr else uaddrAfterFormat

    try
      {aUaddrArray,oldRecord,afterFormatRecord,uaddrBeforeFormatRecord,geoUaddrRecord,mergedIdMap} =\
       await _getOldRecord {uaddrAfterFormat,uaddrBeforeFormat,geoUaddr}
    catch err
      debug.error err, recordToReturn
      return cb err
    
    if not oldRecord
      return _handleNoOldRecord recordToReturn,aUaddrArray,cb

    # 同一个城市存在多个一样的街道名,uaddr需要添加zip前3位进行区分
    if geoCodingResult.zip and (not isSameZip3WithGeoCache(oldRecord, geoCodingResult.zip))
      debug.warn "duplicate street in same city, old:#{oldRecord._id},#{oldRecord.zip},new:#{uaddrZipAfterFormat},#{geoCodingResult.zip}"
      
      uaddrZipAfterFormat ?= getZipUaddr uaddrAfterFormat,geoCodingResult.zip
      if not uaddrZipAfterFormat
        debug.error "no uaddrZipAfterFormat and uaddrAfterFormat:#{uaddrAfterFormat} is error"
        return cb 'Duplicate street with different zip'

      uaddrZipBeforeFormat ?= getZipUaddr uaddrBeforeFormat,geoCodingResult.zip
      geoZipUaddr = getZipUaddr geoUaddr,geoCodingResult.zip
      try
        {aUaddrArray,oldRecord,afterFormatRecord,uaddrBeforeFormatRecord,geoUaddrRecord,mergedIdMap} =\
         await _getOldRecord {uaddrAfterFormat:uaddrZipAfterFormat,uaddrBeforeFormat:uaddrZipBeforeFormat,geoUaddr:geoZipUaddr}
      catch err
        debug.error err, recordToReturn, uaddrZipBeforeFormat, uaddrZipAfterFormat
        return cb err

      if not oldRecord
        recordToReturn._id = uaddrZipAfterFormat
        return _handleNoOldRecord recordToReturn,aUaddrArray,cb
      else
        # NOTE: 当存在oldRecord时,应该更换_id为oldRecord._id
        recordToReturn._id = oldRecord._id


    isCoordinateDiff = true
    dist = null
    if oldRecord?.lat and oldRecord.lng and geoCodingResult.lat and geoCodingResult.lng
      dist = geolib.getDistance(
        {latitude: oldRecord.lat, longitude: oldRecord.lng},
        {latitude: geoCodingResult.lat, longitude: geoCodingResult.lng}
      )
      if dist <= 2
        isCoordinateDiff = false
    # cache 的记录更好，return cache的, no update
    if cacheIsBetter forceUpdate, geoCodingResult.q, oldRecord.q, isCoordinateDiff
      await checkAndSaveZip oldRecord,zipArr
      return cb null, oldRecord
    # 如果新来的大，而且不一样。更新cache。return new geoToSave
    # calc, new and old dist. if dist > 1000, save to geoConfilict
    # oldRecord.src, geoToSave.src ['TRB','DDF']
    # geolib.getDistance
    propSrc = ['TRB','DDF']
    update = { $addToSet:{zipArr:{$each:zipArr}} , $set:geoToSave}
    delete update.$set._id

    update.$addToSet.his =
      lat:oldRecord.lat,
      lng:oldRecord.lng,
      q:oldRecord.q,
      src:oldRecord.src,
      qaddr:oldRecord.qaddr,
      faddr:oldRecord.faddr,
      mt:oldRecord.mt
    if (oldRecord.src in propSrc) or (geoToSave.src in propSrc)
      if dist > 1000
        geoConflict = formatConflict oldRecord,geoToSave
        update.$addToSet.geoConflict = geoConflict

    # NOTE:aUaddr以外内容需要先进行update,因为aUaddr是unique的,更新因为种种原因导致Error E11000(多线程，地址mapping...)需要单独更新
    updateQueryId = {_id:oldRecord._id}
    try
      await GeoCache.updateOne updateQueryId, update, { upsert: false }
    catch err
      debug.error 'Error update geoCache:',updateQueryId,update,err
      return cb err

    aUaddrArray = removeDup aUaddrArray, afterFormatRecord?.aUaddr, uaddrBeforeFormatRecord?.aUaddr, geoUaddrRecord?.aUaddr
    recordToReturn.aUaddr = oldRecord.aUaddr
    if aUaddrArray.length
      recordToReturn.aUaddr = recordToReturn.aUaddr.concat(aUaddrArray)
      update2 = {$addToSet:{ aUaddr: { $each: aUaddrArray }}}
      try
        await GeoCache.updateOne updateQueryId, update2, { upsert: false }
      catch err
        if err and /E11000|duplicate\skey/ig.test(err.toString())
          debug.debug 'duplicate key error, ignore'
        else
          return cb err,recordToReturn if err
      
    # 3个里面存在2个 需要merge
    if (mergedIds = Object.keys(mergedIdMap)).length > 1
      try
        records = await GeoCache.findToArray { _id: { $in: mergedIds } }
        bestRecord = await GeoCoderLocal.mergeGeoCache records
      catch err
        debug.error err, mergedIds
    return cb null, bestRecord or recordToReturn

  # wrapper for unittest
  @_fixAddrByFindSimilarAddrInGeoCacheResults:(obj,ret)->
    return await fixAddrByFindSimilarAddrInGeoCacheResults obj,ret

  # wrapper for unittest
  @_isSimilarAddrOfPropAndGeoCache: (prop, geoCache)->
    return isSimilarAddrOfPropAndGeo prop, geoCache

  ###
  interface obj {
    zip?: string,
    cnty: string,
    prov: string,
    city: string,
    addr?: string, # use addr only or use st + st_num
    st?: string,
    st_num?: string,
    lat?: number,
    lng?: number,
    ptype?: string,
    ptype2?: string[],
    engineType?: string,
    useCache?: boolean, # default true
    saveResult?: boolean, # default true
  }
  ###
  # NOTE: change all of this to async/await, 当前func可以使用addAsyncSupport改为async
  @geocoding: (obj, cb) ->
    obj.useCache ?= true
    obj.saveResult ?= true
    {error, uaddrBeforeFormat, uaddrZipBeforeFormat, uaddrAfterFormat, uaddrZipAfterFormat} = checkAndformatGeoInput obj
    return cb(error) if error
    if not (uaddrZipBeforeFormat or uaddrZipAfterFormat)
      debug.warn 'No zip in obj',obj
    updateRelatedRecord = (geoCacheId,uaddrAfterFormat) ->
      redoUpdate = false
      try
        await GeoCache.updateOne {_id:geoCacheId},{$addToSet:{aUaddr:uaddrAfterFormat}}
      catch err
        if /E11000|duplicate\skey/ig.test err.toString()
          # debug.debug 'duplicate key error, ignore'
          debug.info 'Error: record with this aUaddr exists with different _id!', geoCacheId, uaddrAfterFormat
          redoUpdate = true
        else
          debug.error err,"geoCacheId:#{geoCacheId},uaddrAfterFormat:#{uaddrAfterFormat}"
      if redoUpdate
        {transactionOptions,session} = GETTRANSACTION 'vow'
        try
          session.startTransaction(transactionOptions)
          # NOTE:aUaddr.length === 1 时需要将两条数据合并，否则第二次碰到类似case时会产生错误
          record = await GeoCache.findOne {aUaddr:uaddrAfterFormat},{projection:{aUaddr:1}}
          if record and (record.aUaddr.length is 1)
            await GeoCache.deleteOne {_id:uaddrAfterFormat},{session}
          else
            await GeoCache.updateOne {aUaddr:uaddrAfterFormat},{$pull:{aUaddr:uaddrAfterFormat}},{session}
          await GeoCache.updateOne {_id:geoCacheId},{$addToSet:{aUaddr:uaddrAfterFormat}},{session}
          await session.commitTransaction()
        catch err
          debug.error err,"geoCacheId:#{geoCacheId},uaddrAfterFormat:#{uaddrAfterFormat}"
          await session.abortTransaction()
        finally
          await session.endSession()
    
    doGeocoding = () ->
      if gNoGeoCoding
        debug.debug 'should do geocoding, but gNoGeoCoding', obj, uaddrBeforeFormat
        return cb null, {}

      # query address添加zip,cmty
      qaddr = propAddress.getFullAddress obj,true,true
      geoCodeOneWithEngine {
        engineType: obj.engineType,
        qaddr: qaddr,
        lat: obj.lat,
        lng: obj.lng,
        tryAll: false,
        zip:obj.zip,
        src:obj.src,
        id:obj.id
      }, (err, ret) ->
        return cb err if err
        
        # NOTE: this await changed geocoding function to async, UNEXPECTED error wont be caught inside cb functions or cb err
        # if obj.ptype2?.includes 'Apartment'
        # NOTE: 取消ptype2判断,所有房源都做相似地址查找合并处理
        try
          geoCacheId = await fixAddrByFindSimilarAddrInGeoCacheResults obj, ret
        catch err
          return cb err
        if geoCacheId
          # 找到匹配的geo_cache记录,uaddr写入geo_cache.aUaddr
          updateRelatedRecord(geoCacheId,uaddrAfterFormat)
          ret._id = geoCacheId
          return cb null, ret

        if not obj.saveResult
          return cb(null, ret)
        geoAddr = ret.geoAddr?.toUpperCase()
        objAddr = obj.addr?.toUpperCase()
        if ret.geoAddr and (ret.q is 100) and (geoAddr isnt objAddr)
          geoUaddr = helpers_string.unifyAddress Object.assign obj,{addr:ret.geoAddr}

        #保存所有原始结果 save 的时候 geoCodingResult 的结果不一定比cache里的结果更好
        GeoCoderLocal.saveGeoCache {
          uaddrBeforeFormat,
          uaddrAfterFormat,
          geoUaddr,
          uaddrZipAfterFormat,
          uaddrZipBeforeFormat,
          ptype: obj.ptype,
          ptype2: obj.ptype2,
          geoCodingResult: ret,
          propZip: obj.zip
        }, (err, ret) ->
          return cb(err) if err
          ret.doCoding = true
          ret = replaceWithReverseGeocodingResult(ret)
          return cb(null, ret)

    if obj.useCache
      checkGeoCache {uaddrAfterFormat, uaddrZipAfterFormat, propZip:obj.zip}, (err, result) ->
        debug.error err if err
        if result and result.lat and result.lng
          result.cached = true
          return cb null, result
        doGeocoding()
    else
      doGeocoding()

  #reverse geocoding, do not check cache and do not save
  @reverseGeocoding: ({engineType, lat, lng, qaddr}, cb) ->
    geoCodeOneWithEngine {engineType, lat, lng, qaddr}, cb

  @parseGeocodeResult: (geoCacheOrig,qaddr) ->
    geoRetParseMap = {
      'azure': parseAzureResult
      'bing': parseBingResult
      'here': parseHereResult
      'google': parseGoogleResult
      'mapbox': parseMapboxResult
      'mapquest': parseMapquestResult
    }
    results = []
    for key,parseFn of geoRetParseMap
      if key is 'here'
        geoResult = geoCacheOrig[key]?.items?[0]
      else if key is 'mapquest'
        geoResult = geoCacheOrig[key]?.results?[0]
      else if key is 'mapbox'
        geoResult = geoCacheOrig[key]?.features?[0]
      else if key is 'azure'
        geoResult = geoCacheOrig[key]?.results?[0]
      else
        geoResult = geoCacheOrig[key]
      if geoResult
        # geo获取的数据可能没有addr，添加addr信息
        ret = { src:key, q:0, qaddr , addr:qaddr.split(',')[0]}
        try
          parseFn geoResult,ret
          propAddress.formatProvAndCity ret
        catch e
          debug.error e, ret," OrigId:#{geoCacheOrig._id}"
          return
        adjustGeocodingQuality(ret, qaddr)
        if (ret.q < 100) or (not ret.zip?) or (ret.zip.length < 5)
          results.push ret
          continue
        else
          # bestRecord
          return ret
    for r in results
      if ('object' is typeof r) and r.q > 0
        if (not bestRecord?) or (bestRecord.q < r.q)
          bestRecord = r
        # sometime other providers has better/full zip result
        if (not bestRecord.zip?) or \
          (r.zip? and (bestRecord.zip.length < r.zip.length))
            bestRecord.zip = r.zip
    return bestRecord

MODEL 'GeoCoderLocal', GeoCoderLocal
