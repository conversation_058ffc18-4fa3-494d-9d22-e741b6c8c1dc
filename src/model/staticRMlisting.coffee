###
translate following and modify when request

prop and status fields
  tp:   type of input
  fld:  field name in db
  v:    [] values to be displayed
  dbv:  [] values to be stored in db
  opt:  optional indicator
###

# TODO: expire date has no range
# datePicker show date first



###
prop json
propExample = {
  id : "RM1-234" //generate from createUniqueListingID

  market: {ts:'time'} // ts = new Date()
  58: {ts:'', id:''}

  ml_num : "E3162090" // if from mls

  stus : "Inactive" # Status
  lp_dol : "" // List Price
  ep : "" // Expire Date
  pd : "" // Possession Date   new Date(), if is range this field has pdf(From)/pdt(To), if use ~ string wont be easy to search
  mf : "" // Maintenance Fee
  pt : "" // Property Tax
  addr : "" # Address use treb
  plb : "fa/zip" # Public Full Address/Zip only
# imgs:{
#     base:'f.i.realmaster.com'
#     mlbase:'/01/005/ml_num'
#     l:['C.jpg','D.jpg','E.jpg', '/01', '/02'] # if treb "/01"
#     //use save procedure 1.form url array to object; 2. update last update time
#     //delete also removes tag
}
###

###
# save listing id in user_profile
# A:{
#   i:
#   e:
#   w:
#   h:
#   ts: "create time"
#   mt: "last update date"
#   tags:['RM0100001']
# }

# walk score browser query own url
###
assignList = []
ptpValuesList = [
  ['Detached', '2-Storey'],['Detached', '3-Storey'], ['Detached', 'Bungalow'], ['Detached', 'Split'],
  ['Semi-Detached', '2-Storey'], ['Semi-Detached', '3-Storey'], ['Semi-Detached', 'Bungalow'], ['Semi-Detached',''],
  ['Freehold Townhouse', '2-Storey'],['Freehold Townhouse', '3-Storey'], ['Freehold Townhouse', 'Bungalow'], ['Freehold Townhouse', ''],
  ['Condo Townhouse', '2-Storey'], ['Condo Townhouse', '3-Storey'], ['Condo Townhouse','Bungalow'], ['Condo Townhouse', ''],
  ['Condo Apt',''], ['Condo Apt', 'Loft'], ['Condo Apt','Bachelor/Studio'],
  ['Room',''], ['Parking Space',''], ['Land',''], ['Other', '']]

RM_PROP_FIELDS = {uid:1, avt:1, fn:1, ln:1,cpny:1, bdrms:1, bthrms:1, gr:1, \
    br_plus:1, bat:1, lat:1, lng:1, mt:1, ml_num:1, sid:1, pic:1, \
    pho:1, id:1, status:1, lp:1, addr:1, unt:1, daddr:1, ptp:1, pstyl:1, \
    ts:1, prov:1, city:1, zip:1, market:1, ltp:1, stp:1, ver:1, edm:1, topTs:1, \
    topup_pts:1, src:1}# favU:1

create_listing_templates = {
  psf : [
    {tp:'title',   fld:'tl'}
    {tp:'drpd',    fld:'status', v:['Active','Inactive', 'Sold', 'Leased', 'Terminated']}#'Expired',
    {tp:'numberInt',  fld:'lp' , ph:'0 - To Be Negotiated'}
    {tp:'date',    fld:'exp' , v:['TBA','IMMED']}
    {tp:'date',    fld:'psn' , v:['TBA','IMMED']}
    {tp:'numberInt',  fld:'mfee',   opt:1}
    {tp:'numberInt',  fld:'tax',   opt:1}
  ]
  #location fiekds
  loc : [
    {tp:'text',    fld:'addr' }
    {tp:'cbool2',  fld:'daddr', v:['Full Addr','Zip Only'], dbv:['Y','N'] }
    {tp:'text',    fld:'crsst', opt:1}
  ]
  pjt : [
    {tp:'text', fld:'origpr',ph:'Only visible to VIP agents'}
    {tp:'text', fld:'dpst',ph:'Only visible to VIP agents'}
    {tp:'date', fld:'psn', v:['TBA','IMMED'], ctx:'assigmentListing'}
    {tp:'numberInt', fld:'caplevy',opt:1}
    {tp:'text', fld:'prj'   , opt:1} #Project Name，Phase，Model，Developer，Builder,Original Price,Deposit
    {tp:'text', fld:'fas'   , opt:1}
    {tp:'text', fld:'mdl'   , opt:1}
    {tp:'text', fld:'dvlpr' , opt:1}
    {tp:'text', fld:'bldr'  , opt:1}
  ]
  # prop detail fields
  pd : [
    {tp:'drpd2',    fld:'ptp' ,v:ptpValuesList
    } #style
  ]
  pd1 : [
    {tp:'cnum',    fld:'bdrms' }
    {tp:'cnum',    fld:'br_plus'}
    {tp:'cnum',    fld:'bthrms' }
    {tp:'cnum',    fld:'kch' }
    {tp:'drpd',    fld:'gatp', v:[
      'Built-In', 'Detached', 'Attached',
      'Carport', 'Underground', 'Outside/Surface', 'Other', 'None'
      ]}
    {tp:'cnum',    fld:'gr' }
  ]
  pd1opt : [
    {tp:'cnum',    fld:'bdrms' ,  opt:1}
    {tp:'cnum',    fld:'br_plus', opt:1}
    {tp:'cnum',    fld:'bthrms' , opt:1}
    {tp:'cnum',    fld:'kch' ,    opt:1}
    {tp:'drpd',    fld:'gatp', v:[
      'Built-In', 'Detached', 'Attached',
      'Carport', 'Underground', 'Outside/Surface', 'Other', 'None'
      ], opt:1}
    {tp:'cnum',    fld:'gr' ,     opt:1}
  ]
  pd2 :[
    #age1 : start / age2 : end
    #sqft1: min   / sqft2: max
    {tp:'drpd',    fld:'age', v:['Unknown','New','0-5','6-10', '6-15','11-15', '16-30','31-50','51-99','100+'], opt:1 }
    # {tp:'drpd',    fld:'sqft', v:['0-499','500-599','500-699','600-699','700-799','700-899','700-1100', '800-899', '900-999', '1000-1199',
    # '1100-1299','1100-1500','1200-1399','1300-1499', '1400-1599','1500-2000', '1600-1799','1800-1999', '2000-2249','2000-2500','2250-2499',
    # '2500-2749', '2500-3000','2750-2999','3000-3249', '3000-3500',
    # '3250-3499','3500-3749','3500-5000', '3750-3999','4000-4249','4250-4499','4750-4999', '5000+'], opt:1}
    {tp:'text',    fld:'sqft' , opt:1}
    {tp:'cnum',    fld:'lvl',  opt:1}
    {tp:'text',    fld:'unt' , opt:1}
    {tp:'drpd',    fld:'fce' , v:['N','S','W','E','SW','SE','NW','NE'], opt:1}
  ]
  vid:[
    {tp:'text',    fld:'ytvid' , opt:1}
    {tp:'text',    fld:'vurlcn' , opt:1}
    {tp:'text',    fld:'vimgcn' , opt:1}
  ]
  #amenities options
  amt : [
    {tp:'drpd',  fld:'bsmt' , v:['Apartment','Finish/Walkout','Finished','Crawl Space', 'Part Bsmt', 'Part Fin', 'Sep Entrance',
    'Unfinished','Walkout', 'Walk-Up', 'Other', 'None'] , opt:1}
    {tp:'bool',    fld:'den',   opt:1}
    {tp:'bool',    fld:'blcny', opt:1}
    {tp:'bool',    fld:'grdn',  opt:1}
    {tp:'bool',    fld:'gym',   opt:1}
    {tp:'bool',    fld:'pool',  opt:1}
    {tp:'bool',    fld:'fpop',  opt:1} #prkg_inc / free parking on premises
    {tp:'bool',    fld:'lkr',   opt:1}
    {tp:'bool',    fld:'fpl',   opt:1}
    {tp:'bool',    fld:'htb',   opt:1}
    {tp:'drpd',    fld:'ac', v:['Central Air','Part','None','Other'] ,opt:1}
    {tp:'drpd',    fld:'heat', v:['Gas Forced Air','Elec Forced Air','Radiant','Baseboard', 'Gas Hot Water','Elec Hot Water','Oil Forced Air','Oil Hot Water', 'Woodburning','Heat Pump','Other'] ,opt:1}
  ]
  #rent amenities
  rent_amt : [
    {tp:'bool',    fld:'wifi',   opt:1}
    {tp:'bool',    fld:'tv',     opt:1}
    {tp:'bool',    fld:'wsr',    opt:1}
    {tp:'bool',    fld:'dyr',    opt:1}
    {tp:'bool',    fld:'frdg',   opt:1}
    {tp:'bool',    fld:'movn',   opt:1}
    {tp:'bool',    fld:'frnshd', opt:1}
  ]
  rmrk : [
    {tp:'textbox', fld:'m' }
  ]

  rmrk_zh : [
    {tp:'textbox1', fld:'m_zh', opt:1}
  ]

  #rent policy
  # fld as context name in abbr
  rp : [
    {tp:'cbool3',  fld:'rtp',    v:['Entire Place','Private Room', 'Shared Room'], dbv:['epl','prm','srm'] }
    {tp:'cbool3gdr',  fld:'rgdr',    v:['Male Only','Female Only', 'No Restriction'], dbv:['m','f','n'] , opt:1}
    # {tp:'bool',    fld:'utl' } #utility included
    {tp:'bool',    fld:'water_inc' } #utility included
    {tp:'bool',    fld:'hydro_inc' } #utility included
    {tp:'cnum',    fld:'mintrm' }
    {tp:'cbool2',  fld:'entr',     v:['Shared', 'Separate'] }
    {tp:'cbool2',  fld:'wrmtp' ,    v:['Shared', 'Separate'] }
    {tp:'cbool2',  fld:'kchntp' ,     v:['Shared', 'Separate'] }
    {tp:'cbool2',  fld:'lndrytp' ,     v:['Shared', 'Separate'] }
    {tp:'bool',    fld:'smok' ,  opt:1}
    {tp:'bool',    fld:'pets' ,   opt:1}
    # {tp:'bool',    fld:'fmlyfrnd',   opt:1}
  ]
  fields : []
}

ex_listing_template = {
  tp:   "Exclusive Listing"
  psf:  create_listing_templates.psf
  loc:  create_listing_templates.loc
  pd:   create_listing_templates.pd.concat(create_listing_templates.pd1, create_listing_templates.pd2)
  amt:  create_listing_templates.amt
  rmrk: create_listing_templates.rmrk
  vid:  create_listing_templates.vid
}
mls_listing_template = {
  tp:   "MLS Listing"
  psf:  create_listing_templates.psf
  loc:  create_listing_templates.loc
  pd:   create_listing_templates.pd.concat(create_listing_templates.pd1, create_listing_templates.pd2)
  amt:  create_listing_templates.amt
  rmrk: create_listing_templates.rmrk
  vid:  create_listing_templates.vid
}

assignment_template = {
  tp:   "Assignment Listing"
  psf:  assignList.concat(create_listing_templates.psf.slice(0, 4),create_listing_templates.psf.slice(-2))#create_listing_templates.psf.slice(0, 4).concat(create_listing_templates.psf.slice(-2))
  pjt:  create_listing_templates.pjt
  loc:  create_listing_templates.loc
  pd:   create_listing_templates.pd.concat(create_listing_templates.pd1opt, create_listing_templates.pd2)
  amt:  create_listing_templates.amt
  rmrk: create_listing_templates.rmrk
  rmrk_zh: create_listing_templates.rmrk_zh
  vid:  create_listing_templates.vid
}
rent_template = {
  tp:   "Rent Listing"
  psf:  create_listing_templates.psf
  loc:  create_listing_templates.loc
  pd:   create_listing_templates.pd.concat(create_listing_templates.pd1, create_listing_templates.pd2)
  rp:   create_listing_templates.rp
  amt:  create_listing_templates.amt.concat create_listing_templates.rent_amt
  rmrk: create_listing_templates.rmrk
}

init_listing_fields = ()->
  fields = ['psnf','psnt','ts','created','src','sid','vturl','ltp','st','st_num','lat','lng','city','cmty','cnty','prov','pstyl'
    'zip','pho','pic','rid', 'pcls','id','sid','rltr', 'phomt', 'rms', 'lpunt', 'flt','front_ft', 'depth', 'lotsz_code','irreg', 'ml_num',
    'area', 'tllck', 'age1','age2', 'sqft1','sqft2',
    'vac', 'bths', 'feat']
    # Rain: check if need age/sqft as well, --no need, will be pushed later
  for k, v of create_listing_templates
    for i in v
      if i.fld
        create_listing_templates.fields.push i.fld
  for i in [1..12]
    create_listing_templates.fields.push "rm#{i}_out"
    create_listing_templates.fields.push "level#{i}"
    create_listing_templates.fields.push "rm#{i}_len"
    create_listing_templates.fields.push "rm#{i}_wth"
    create_listing_templates.fields.push "rm#{i}_dc1_out"
    create_listing_templates.fields.push "rm#{i}_dc2_out"
    create_listing_templates.fields.push "rm#{i}_dc3_out"
  create_listing_templates.fields = create_listing_templates.fields.concat fields
init_listing_fields()

getRMListingTemplete = (tpl)->
  if tpl is 'exlisting'
    return ex_listing_template
  else if tpl is 'mlslisting'
    return mls_listing_template
  else if tpl is 'assignment'
    return assignment_template
  else if tpl is 'rent'
    return rent_template
  else if tpl is 'mlsrent'
    return rent_template
  return {}

propMReplaceList = [
  {reg:/(\W)W\/O(\W)/ig, to:'$1Walk-Out$2'} # case sensitive
  {reg:/(\W)W\/I(\W)/ig, to:'$1walk-in$2'}
  {reg:/(\W)B\/?I(\W)/ig, to:'$1built-in$2'}
  {reg:/(\W)F\/?P(\W)/ig, to:'$1fireplace$2'}
  {reg:/(\W)R\/?I(\W)/ig, to:'$1rough-in$2'}
  {reg:/(\W)hwt(\W)/ig, to:'$1hot water tank$2'}
  {reg:/(\W)sep\.(\W)/ig, to:'$1separate$2'}
  {reg:/(\W)entra?nc?e?(\W)/ig, to:'$1entrance$2'}
  {reg:/(\W)gdr(\W)/ig, to:'$1garage door remote$2'}
  {reg:/(\W)gdos?(\W)/ig, to:'$1garage door opener$2'}
  {reg:/(\W)gdor(\W)/ig, to:'$1garage door opener and remote$2'}
  {reg:/(\W)cva?c(\W)/ig, to:'$1Central Vacuum$2'}
  {reg:/(\W)va?c(\W)/ig, to:'$1Vacuum$2'}
  {reg:/(\W)CA\/?C(\W)/ig, to:'$1Central Air Conditioning$2'}
  {reg:/(\W)A\/?C(\W)/ig, to:'$1Air Conditioning$2'}
  {reg:/(\W)Ea?t?\/I(\W)/ig, to:'$1Eat-In$2'}
  {reg:/(\W)s[\/|\.]?s(\W)/ig, to:'$1stainless steel$2'}
  {reg:/(\W)D\/?W(\W)/g, to:'$1dish washer$2'}
  {reg:/(\W)W\/?D(\W)/g, to:'$1washer and dryer$2'}
  {reg:/(\W)U\/D(\W)/g, to:'$1updated$2'}
  {reg:/(\W)L\/?A(\W)/g, to:'$1listing agent$2'}
  {reg:/(\W)Upgr?a?d(\W)/g, to:'$1Upgraded$2'}
  {reg:/(\W)covs?\.?(\W)/ig, to:'$1covering$2'}
  {reg:/(\W)kit(\W)/ig, to:'$1kitchen$2'}
  {reg:/(\W)flrs?(\W)/ig, to:'$1floor$2'}
  {reg:/(\W)lr?g(\W)/ig, to:'$1large$2'}
  {reg:/(\W)hwys?([^a-z])/ig, to:'$1highway $2'}
  {reg:/(\W)excl(\W)/ig, to:'$1exclude$2'}
  {reg:/(\W)incld?(\W)/ig, to:'$1include$2'}
  {reg:/(\W)elfs?\'?s?(\W)/ig, to:'$1electronic light fixture$2'}
  {reg:/(\W)o\/l(\W)/ig, to:'$1overlook$2'}
  {reg:/(\W)ha?r?d?wd(\W)/ig, to:'$1hardwood$2'}
  {reg:/([^a-zA-Z])pce?(\W)/ig, to:'$1 piece$2'}
  {reg:/(\W)t\.?l\.?c(\W)/ig, to:'$1Need fix up$2'}
  {reg:/(\W)be?d?rm?s?(\W)/ig, to:'$1bedroom$2'}
  {reg:/(\W)ba?th?rm?s?(\W)/ig, to:'$1bathroom$2'}
  {reg:/(\W)appls?(\W)/ig, to:'$1appliances$2'}
  {reg:/(\W)microw?a?v?e?(\W)/ig, to:'$1Microwave oven$2'}
  {reg:/(\W)prof?\.?(\W)/ig, to:'$1professional$2'}
  {reg:/(\W)fin\.?(\W)/ig, to:'$1finished$2'}
  {reg:/(\W)bsmt(\W)/ig, to:'$1basement$2'}
  {reg:/(\W)rmts?(\W)/ig, to:'$1remote$2'}
  {reg:/(\W)cls?t(\W)/ig, to:'$1closet$2'}
  {reg:/(\W)Lndry(\W)/ig, to:'$1laundry$2'}
  {reg:/(\W)Site(\W)/ig, to:'$1the location$2'} # overcome google translate stupidies
  {reg:/(\W)Walkout(\W)/ig, to:'$1Walk-Out$2'} # case sensitive
  {reg:/(\W)Steps?\sto(\W)/ig, to:'$1close to$2'}
  {reg:/(\W)Close\sto(\W)/ig, to:'$1close to$2'} # Close to => close to
  {reg:/(\W)Ensuite?(\W)/ig, to:'$1ensuite$2'}
  {reg:/(\W)In[\-|\s]Law[\s|\-]Suite(\W)/ig, to:'$1in-law room$2'}
  {reg:/(\W)Thu?ro?u?g?h?[\s\-]?Out(\W)/ig, to:'$1whole area$2'}
  {reg:/(\W)Windows(\W)/ig, to:'$1window$2'}
  {reg:/(\W)Homes?(\W)/ig, to:'$1home$2'}
  {reg:/(\W)sq[\s\.]?ft(\W)/ig, to:'$1sqft$2'}
  {reg:/(\W)counter(\W)/ig, to:'$1countertop$2'}
  {reg:/(\W)sprinklers?(\W)/ig, to:'$1Watering system$2'}
  {reg:/(\W)Shutter(\W)/ig, to:'$1window shutter$2'}
  {reg:/(\W)shingles(\W)/ig, to:'$1roof shingles$2'}
  {reg:/(\W)interlocks?i?n?g?(\W)/ig, to:'$1brick driveway$2'}
  {reg:/(\W)rm(\W)/ig, to:'$1room$2'}
  {reg:/(\W)H[\.\/]?S(\W)/ig, to:'$1high school$2'}
  {reg:/(\W)P[\.\/]?S(\W)/ig, to:'$1public school$2'}
  {reg:/(\W)Dr(\W)/g, to:'$1Door$2'}
  {reg:/(\W)D\/R(\W)/g, to:'$1Dinning Room$2'}
  {reg:/(\W)L\/R(\W)/g, to:'$1Living Room$2'}
  {reg:/(\,|\.)(\S)/ig, to:'$1 $2'} # must at last
  {reg:/(\W)w\//ig, to:'$1with '}
]
#NOTE: modify this need to check if field exists in sql, else alter table;
# this is for query only
# TODO: + topMt and parseBack
DEFAULT_PROP_FIELDS = {
  bdrms:1,bthrms:1,gr:1, saletp:1,_id:1, topTs:1,topup_pts:1,\
  ptype2:1,unt:1, lp:1, lpr:1, addr:1,sid:1,city:1,prov:1,pho:1,ddfID:1,\
  phosrc:1,PhotoDlDate:1,phodl:1,mt:1,phomt:1, status:1,ts:1, daddr:1, rmdaddr:1,id:1,\
  ltp:1,ptp:1,pstyl:1,photonumbers:1, pic:1,picUrl:1, sp:1, trbtp:1, lst:1,rltr:1,\
  BrokerReciprocity:1,phoUrls:1,thumbUrl:1,phoP:1,tnLH:1,phoLH:1,src:1,MlsStatus:1,ListingKey:1
}
SITE_MAP_PROP_FIELDS = {id:1,_id: 1, addr: 1, city: 1, prov: 1,cmty:1, zip: 1,\
  ptype2: 1, sid: 1, unt: 1, stp: 1, status:1,mt:1,ts:1,pho:1,photonumbers:1,ListingKey:1,\
  ddfID:1, phosrc:1, picUrl:1, imgs:1, pic:1,thumbUrl:1,phoP:1,tnLH:1,phoLH:1,src:1}

module.exports.SITE_MAP_PROP_FIELDS = SITE_MAP_PROP_FIELDS
module.exports.RM_PROP_FIELDS = RM_PROP_FIELDS
module.exports.ptpValuesList = ptpValuesList
module.exports.DEFAULT_PROP_FIELDS = DEFAULT_PROP_FIELDS
module.exports.rmPropFields = create_listing_templates.fields
module.exports.getRMListingTemplete = getRMListingTemplete
module.exports.propMReplaceList = propMReplaceList



