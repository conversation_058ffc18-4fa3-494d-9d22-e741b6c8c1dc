<template lang="pug">
//- div
div.prop(@click="propClicked()", data-sub="detail", data-act="open", :data-id='prop._id')
  div.img(:style="{ 'height':rcmdHeight+'px'}",
  :class="{blur:prop.login}", :rm-data-bg="this.prop.thumbUrl")
    img(:src="computedBgImg"
      style="background-image: url('/img/noPic.png');background-size: 100% 100%;width:100%;"
      :style="{'height':rcmdHeight+'px'}"
      @error="e => { e.target.src = '/img/noPic.png'}"
      referrerpolicy="same-origin")
    div.on-img-top
      span.top.pull-left(v-if="isTop") {{_('TOP')}}
      span.tp(v-show='prop.type')
        | {{prop.type}}
      span.pull-right.fav.fa(:class="{'fa-heart-o':!prop.fav, 'fa-heart':prop.fav}",
        @click.stop.prevent="toggleFav()", v-show="!prop.login && dispVar.isApp && showFav && !prop.isProj")
  div.price(:class="{'blur':prop.login}")
    span.val(v-if="prop.priceValStrRed") {{prop.priceValStrRed}}
    span.val(v-else-if="prop.askingPriceStr") {{prop.askingPriceStr}}
    span.desc(v-if="prop.lspDifPct && prop.lst && prop.lst == 'Sld'")
      | &nbsp;{{Math.round(prop.lspDifPct * 10000)/100}}%
    span.desc(v-else-if="prop.priceValStrRedDesc",:class="{'through':soldOrLeased}") {{soldOrLeased?prop.askingPriceStr:prop.priceValStrRedDesc}}
    //- 显示tag，不显示字符串
    //- span.padding(v-if="prop.saleOrRent")
    //-   span {{prop.saleOrRent}}
    span.desc(v-if="prop.lstStr && (prop.tagColor != 'red')")
      | &nbsp;({{prop.lstStr}})
    div.displayFlex.maxWidth
      span.stp(v-if="prop.saleTpTag && prop.saleTpTag_en !='Delisted'",:class="prop.tagColor")
        span {{prop.saleTpTag}}
        span(v-if="(prop.tagColor == 'red'|| prop.tagColor == 'green') && (prop.spcts||prop.mt||prop.ts) ") &nbsp;{{(prop.spcts||prop.mt||prop.ts) |dotdate}}
      //- span.askprice(v-show="prop.askingPriceStr")
      //-   span(:class="{'through':isPropUnavailable}") {{prop.askingPriceStr}}
      //-   span.desc(:class="{'through':isPropUnavailable}") {{prop.askingPriceStrDesc}}
        //- div(v-show="isSold")
        //-   div(v-show="!isTrebProp && prop.showSoldPrice && prop.sp", style="font-size: 12px;") {{_('Data from Internet, no guarantee.')}}
        //-   div(v-show="!prop.showSoldPrice", style="font-size: 12px;") {{_('According to regulations, sold price is suppressed here.')}}


      //- span(:class="{'through':soldOrLeased}" v-show="(prop.lp || prop.lpr)") {{(prop.lp || prop.lpr) | currency('$', 0)}}
      //- span(v-show="prop.nm && dispVar.lang == 'en'") &nbsp;{{prop.nm_en}}
      //- span(v-show="prop.nm && dispVar.lang !== 'en'") &nbsp;{{prop.nm}}
      //- span.stp(v-show="prop.saletp && prop.status == 'A'")
      //-   | {{computedSaletp}}
      span.stp.vid(v-show="computedVideoUrl")
        span.fa.fa-youtube-play
        | {{_('Video')}}
      //- span.stp(v-show="prop.saletp && prop.status !== 'A'", :class="{'sold':soldOrLeased, 'inactive':!soldOrLeased}")
      //-   span(v-if="soldOrLeased")
      //-     span(v-show="saletpIsSale") {{_('Sold')}}
      //-     span(v-show="!saletpIsSale") {{_('Leased')}}
      //-   span(v-else)
      //-     span {{_('Inactive')}}
      span.stp.oh(v-if="prop.hasOh") {{_('Open House')}}

  prop-list-element-realtor(v-show="adrltr._id && computedShowRealtor && !prop.hideInfo",:adrltr="adrltr",:dispVar="dispVar")

  div.addr.one-line(v-if="!prop.login")
    span(v-if="prop.addr")
      span(v-if="prop.showAddr || prop.addr") {{prop.origUnt || prop.unt}} {{prop.showAddr || prop.addr}},
      span(v-else) {{prop.origUnt || prop.unt}} {{prop.addr}},
      |  {{prop.origCity ||prop.city}}, {{prop.prov}}
    span(v-else)
      span(v-show="prop.cmty") {{prop.origCmty || prop.cmty}},
      | {{prop.origCity ||prop.city}}, {{prop.prov}}
  div.addr.one-line(v-if="prop.login")
    span(v-if="prop.addr") {{prop.addr}}, {{prop.origCity ||prop.city}}
    span(v-else) {{prop.origCity || prop.city}}, {{prop.prov}}

  div.bdrms(v-if='!prop.login')
    span.rmbed(v-show="prop.rmbdrm != null || prop.bdrms != null || prop.tbdrms != null")
      span.fa.fa-rmbed
      b.num {{rmbdrmStr}}
    span(v-show="prop.rmbthrm != null || prop.tbthrms != null || prop.bthrms != null")
      span.fa.fa-rmbath
      b.num {{rmbthrmStr}}
    span(v-show="prop.rmgr != null || prop.gr != null || prop.tgr != null")
      span.fa.fa-rmcar
      b.num {{rmgrStr}}
    span
    span.sid(v-if="!(adrltr._id && computedShowRealtor)",:class="{'promo':prop.topup_pts}")
      //- span.ad(v-if="(dispVar.isPaytop || dispVar.isVipRealtor) && prop.topup_pts", @click.stop.prevent="showPays()")
      //-   | {{_('Ad','advertisement')}}
      //-   i.fa.fa-caret-down
      | {{propSid}}
  div.bdrms(v-if='prop.login')
    | {{_('Please login to see this listing!')}}
</template>

<script>
import filters from '../filters'
import PropListElementRealtor from './PropListElementRealtor.vue'
import prop_mixins from '../prop_mixins'
export default {
  filters:{
    currency:filters.currency,
    dotdate:filters.dotdate,
  },
  mixins:[prop_mixins],
  components:{PropListElementRealtor},
  props: {
    dispVar:{
      type:Object,
      default:function () {
        return {fav:false};
      }
    },
    hasWechat:{
      type:Boolean,
      default:true,
    },
    showFav:{
      type:Boolean,
      default:true,
    },
    rcmdHeight:{
      type:Number,
      default:170
    },
    prop: {
      type: Object,
      default: function () {
        return {fav:false}
      }
    },
    adrltr: {
      type: Object,
      default: function () {
        return {}
      }
    }
  },
  computed:{
    rmgrStr: function () {
      return this.prop.rmgr || this.prop.tgr || this.prop.gr;
    },
    rmbdrmStr: function () {
      if (this.prop.rmbdrm) {
        return this.prop.rmbdrm;
      }
      let prop = this.prop;
      let tmp = prop.bdrms || prop.tbdrms;
      tmp += prop.br_plus?'+ '+prop.br_plus:''
      return tmp;
    },
    rmbthrmStr: function () {
      return this.prop.rmbthrm || this.prop.tbthrms || this.prop.bthrms;
    },
    soldOrLeased: function () {
      return this.prop.saleTpTag_en == 'Sold'|| ['Sld','Lsd','Pnd','Cld'].includes(this.prop.lst);
    },
    saletpIsSale: function () {
      if (!this.prop.saletp_en) {
        return true;
      }
      if (/sale/.test(this.prop.saletp_en.toString().toLowerCase())) {
        return true;
      }
      return false;
    },
    computedVideoUrl:function(){
      if (this.dispVar.isCip) {
        return this.prop.vurlcn;
      } else {
        if (this.prop.ytvid) {
          return "https://www.youtube.com/watch?v="+this.prop.ytvid;
        }
        return null;
      }
    },
    computedSaletp:function(){
      let saletp = this.prop.saletp;
      if (saletp) {
        if (Array.isArray(saletp)) {
          return saletp.join(' ')
        } else {
          return saletp;
        }
      }
      return this.prop.lpunt;
    },
    computedBgImg: function () {
      if (this.lazyExist && !this.intersected) {
        return "/img/noPic.png";
      }
      if (this.prop && this.prop.thumbUrl) {
        return this.prop.thumbUrl || "/img/noPic.png";
      } else {
        return '';
      }
    },
    propSid:function () {
      if (this.prop.isProj) {
        return '';
      } else if (this.prop.sid) {
        return this.prop.sid;
      } else {
        return this.prop._id?this.prop._id.substr(3):'';
      }
    },
    isTop:function() {
      return (this.prop.status == 'A') && (new Date(this.prop.topTs) >= new Date());
    },
    // avtSrc:function () {
    //   if (this.adrltr.avt) {
    //     return this.adrltr.avt;
    //   }
    //   return '/img/logo.png';
    // },
    computedShowRealtor:function(){
      if (this.dispVar.hasFollowedVipRealtor) {
        return false;
      }
      if (!this.dispVar.isLoggedIn) {
        return false;
      }
      if (this.prop.ltp == 'rent') {
        return true;
      }
      if(!this.hasWechat){
        return false
      }
      return (!this.dispVar.isVisitor);
    },
  },
  data () {
    return {
      lazyExist:true,
      intersected:false,
      intersectionOptions:{},
    };
  },
  mounted () {
    if (!window.bus) {
      console.error('global bus is required!');
      return;
    }
    // alert(JSON.stringify(this.prop));
    // if (/iPhone\s+OS\s+14/ig.test(navigator.userAgent)) {
    //   this.lazyExist = false;
    // } else
    if ("IntersectionObserver" in window) {
      // console.log('lazy load observer exists');
      this.observer = new IntersectionObserver(entries => {
        const image = entries[0];
        if (image.isIntersecting) {
          this.intersected = true;
          // console.log('intersected propListElement: '+this.computedBgImg)
          this.observer.disconnect();
          // this.$emit("intersect");
        }
      }, this.intersectionOptions);
      this.observer.observe(this.$el);
    } else if (window.replaceSrc) {
      setTimeout(function () {
        replaceSrc()
      }, 10);
    } else {
      this.lazyExist = false;
    }
  },
  destroyed() {
    // if (/iPhone\s+OS\s+14/ig.test(navigator.userAgent)) {
    //   return;
    // }
    if ("IntersectionObserver" in window) {
      this.observer.disconnect();
    }
  },
  methods: {
    showPays(){
      window.bus.$emit('show-topup', this.prop);
    },
    propClicked () {
      window.bus.$emit('prop-changed', this.prop);
      // window.bus.$emit('set-loading', true);
    },
    // realtorClicked () {
    //   window.bus.$emit('realtor-clicked', this.adrltr);
    // },
    toggleFav(){
      checkAndSendLogger(null,{sub:'toggle fav',id:this.prop._id});
      window.bus.$emit('prop-fav-add', this.prop);
    }
  }
}
</script>


<!-- Add "scoped" attribute to limit CSS to this component only -->
<style lang='scss' scoped>
@import '../../../style/sass/_base.scss';
@import '../../../style/sass/apps/components/propCard.scss';
</style>