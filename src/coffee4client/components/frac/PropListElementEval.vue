<template lang="pug">
div.prop
  div.detail-wrapper
    div.checkbox(v-if="showCheckbox",@click="selectProp(prop._id)")
      span.fa(:class="[checkedProps && checkedProps.indexOf(prop._id) > -1 ? 'fa-check-square-o':'fa-square-o']")
    div.detail(:style="{width: calculatedPropWidth}",@click="propClicked()")
      div.address {{prop.unt||''}} {{prop.addr}}
        span(style="color:#999; font-size:12px") , {{prop.city}}
      div.price(style="font-size:15px;")
        div(v-if="soldOrLeased")
          span {{(prop.sp) | currency('$', 0)}}
        div
          span(:class="{'through':soldOrLeased}")  {{(prop.lp || prop.lpr) | currency('$', 0)}}
          span.txt {{_('Asking Price')}}
      div.dist(v-if="!fromMls")
        span(v-show="prop.dist<30")
          | {{_('Same Location', 'evaluation')}}
        span(v-show="prop.dist>=30 && prop.sameStreet")
          | {{_('Same Street', 'evaluation')}}
        span(v-show="prop.dist>=30 && prop.sameCmty")
          | {{_('Same Community', 'evaluation')}}
        span(v-show="prop.dist>5")
          span {{_('Distance','evaluation')}}:{{dist}}
      div.bdrms
        span(v-show="prop.rmbdrm != null || prop.bdrms != null || prop.tbdrms != null")
          span.fa.fa-rmbed
          span {{prop.bdrms || prop.rmbdrm || prop.tbdrms}}
          span.equal(v-if="prop.bdrms_diff==0") (&harr;)
          span(v-if="prop.bdrms_diff") &nbsp;(
            span(:class='{red: prop.bdrms_diff<0, green:prop.bdrms_diff>0 }')
              span.nopadding(v-if="prop.bdrms_diff<0") &darr;
              span.nopadding(v-if="prop.bdrms_diff>0") &uarr;
              span {{Math.abs(prop.bdrms_diff)}}
            span )
        span(v-show="prop.rmbthrm != null || prop.tbthrms != null || prop.bthrms != null")
          span.fa.fa-rmbath
          span {{prop.bthrms || prop.rmbthrm || prop.tbthrms}}
          span.equal(v-if="prop.bthrms_diff==0") (&harr;)
          span(v-if="prop.bthrms_diff") &nbsp;(
            span(:class='{red: prop.bthrms_diff<0, green:prop.bthrms_diff>0 }')
              span.nopadding(v-if="prop.bthrms_diff<0") &darr;
              span.nopadding(v-if="prop.bthrms_diff>0") &uarr;
              span {{Math.abs(prop.bthrms_diff)}}
            span )
        span(v-show="prop.rmgr != null || prop.gr != null || prop.tgr != null")
          span.fa.fa-rmcar
          span {{prop.gr || prop.rmgr || prop.tgr}}
          span.equal(v-if="prop.gr_diff==0") (&harr;)
          span(v-if="prop.gr_diff") &nbsp;(
            span(:class='{red: prop.gr_diff<0, green:prop.gr_diff>0 }')
              span.nopadding(v-if="prop.gr_diff<0") &darr;
              span.nopadding(v-if="prop.gr_diff>0") &uarr;
              span {{Math.abs(prop.gr_diff)}}
            span )
      div.sqft(v-show="prop.sqft && !fromMls")
        span {{prop.sqft}}{{_('sqft','prop')}}
        span(v-if="prop.sqft_diff") &nbsp;(
          span(:class='{red: prop.sqft_diff<0, green:prop.sqft_diff>0 }')
            span(v-if="prop.sqft_diff<0") &darr;
            span(v-if="prop.sqft_diff>0") &uarr;
            span {{Math.abs(prop.sqft_diff)}}
          span )

        span(v-if="isNumeric(prop.sqft1_diff)") &nbsp;(
          span(:class='{red: prop.sqft1_diff<0, green:prop.sqft1_diff>0 }')
            span(v-if="prop.sqft1_diff<0") &darr;
            span(v-if="prop.sqft1_diff>0") &uarr;
            span {{Math.abs(prop.sqft1_diff)}}

        span(v-if="isNumeric(prop.sqft2_diff)")
          span ~
          span(:class='{red: prop.sqft2_diff<0, green:prop.sqft2_diff>0 }')
            span(v-if="prop.sqft2_diff<0") &darr;
            span(v-if="prop.sqft2_diff>0") &uarr;
            span {{Math.abs(prop.sqft2_diff)}}
          span )

      div.sqft(v-show="prop.front_ft && !fromMls")
        span {{_('Lot Size')}}: {{prop.front_ft}}
        span(v-if="prop.front_ft_diff") &nbsp;(
          span(:class='{red: prop.front_ft_diff<0, green:prop.front_ft_diff>0 }')
            span(v-if="prop.front_ft_diff<0") &darr;
            span(v-if="prop.front_ft_diff>0") &uarr;
            span {{Math.abs(prop.front_ft_diff)}}
          span )
        span * {{prop.depth}}
        span(v-if="prop.size_diff") &nbsp;(
          span(:class='{red: prop.size_diff<0, green:prop.size_diff>0 }')
            span(v-if="prop.size_diff<0") &darr;
            span(v-if="prop.size_diff>0") &uarr;
            span {{Math.abs(prop.size_diff)}}
            span {{_(prop.lotsz_code)}}
              sup 2
          span )

      div.weight(v-if="dispVar.isDevGroup && prop.weight && prop.weight.wTotal")
        span(v-if="prop.weight.wTotal") Total Weight : {{prop.weight.wTotal}}
        span(@click="wDetailClick($event)")
          span(v-show="!showWDetail") See Detail
          span(v-show="showWDetail") Hide Detail

        div(v-show="dispVar.isDevGroup && showWDetail")
          div
            span(v-if="prop.weight.wSld") sld Weight : {{prop.weight.wSld}}
            span(v-if="prop.weight.wTs") ts Weight : {{prop.weight.wTs}}
          div
            span(v-if="prop.weight.wRange") range Weight : {{prop.weight.wRange}}
            span(v-if="prop.weight.wBdrms") bdrms Weight : {{prop.weight.wBdrms}}
          div
            span(v-if="prop.weight.wBthrms") bthrms Weight : {{prop.weight.wBthrms}}
            span(v-if="prop.weight.wGr") gr Weight : {{prop.weight.wGr}}
          div
            span(v-if="prop.weight.wBr_plus") br_plus Weight : {{prop.weight.wBr_plus}}
            span(v-if="prop.weight.wSqft") sqft Weight : {{prop.weight.wSqft}}
          div
            span(v-if="prop.weight.wSize") size Weight : {{prop.weight.wSize}}
            span(v-if="prop.weight.wPrice") price Weight : {{prop.weight.wPrice}}

  div.img-wrapper(@click="propClicked()")
    div.img
      img(referrer-policy="no-referrer",:src="computedBgImg",  onerror="this.src='/img/noPic.png';return true;")
      span.ts {{formatTs(prop.slddt || prop.mt)}}
      div.stp(:class="prop.tagColor || 'gray'",v-show="prop.saleTpTag")
        | {{prop.saleTpTag}}

    div.action
      span.btn(v-if="showIcon == 'remove'", @click="iconClicked($event, 'remove')")
        | {{_('Exclude','evaluation')}}
      span.btn(v-if="showIcon == 'add'", @click="iconClicked($event,'add')")
        | {{_('Include','evaluation')}}
</template>

<script>
import rmsrv_mixins from '../rmsrv_mixins'
import evaluate_mixins from '../evaluate_mixins'

import filters from '../filters'

export default {
  mixins: [rmsrv_mixins,evaluate_mixins],
  filters:{
    currency:filters.currency,
  },
  props: {
    dispVar:{
      type:Object,
      default:function () {
        return {fav:false};
      }
    },
    fromMls: {
      type: Boolean,
      default: function () {
        return false
      }
    },
    prop: {
      type: Object,
      default: function () {
        return {}
      }
    },
    saleOrRent: {
      type: String,
      default:function() {
        return ''
      }
    },
    showIcon:{
      type: String,
      default: function () {
        return ''
      }
    },
    showCheckbox:{
      type: Boolean,
      default: function () {
        return false
      }
    },
    checkedProps:{
      type: Array,
      default: function () {
        return []
      }
    },
  },
  computed:{
    calculatedPropWidth: function() {
      if (this.showCheckbox) {
        return 'calc(100% - 40px)';
      } else {
        return '100%';
      }
    },
    dist:function() {
      var d = this.prop.dist;
      if(d>=1000) {
        return parseFloat(d/1000).toFixed(1) + 'km';
      } else {
        return d+'m';
      }
    },
    soldOrLeased: function () {
      return this.prop.saleTpTag_en == 'Sold'|| ['Sld','Lsd','Pnd','Cld'].includes(this.prop.lst);
    },
    saletpIsSale: function () {
      if (Array.isArray(this.prop.saletp_en)) {
        return /Sale/.test(this.prop.saletp_en.join(','));
      }
      return /Sale/.test(this.prop.saletp_en.toString());
    },
    computedBgImg: function () {
      if (this.prop && this.prop.thumbUrl) {
        return this.prop.thumbUrl;
      }
      return '/img/noPic.png';
    },
    computedSaletp:function(){
      let saletp = this.prop.saletp;
      if (saletp) {
        if (Array.isArray(saletp)) {
          return saletp.join(' ')
        } else {
          return saletp;
        }
      }
      return this.prop.lpunt;
    },
    computedHeight: function() {
      if (this.fromMls) {
         return 105;
      }
      var height = 105;
      if (this.prop.sqft) {
        height = height +25;
      }
      if (this.prop.depth) {
        height = height + 25;
      }
      if (this.prop.dist) {
        height = height + 25;
      }
      if (this.showIcon && height < 145)
        height = 145;
      return height;
    },
    propSid:function () {
      if (this.prop.sid) {
        return this.prop.sid;
      } else {
        return this.prop._id?this.prop._id.substr(3):'';
      }
    }
  },
  data () {
    return {
      showWDetail: false,
    };
  },
  mounted () {
    if (!window.bus) {
      console.error('global bus is required!');
      return;
    }
  },
  methods: {
    selectProp(id) {
      event.stopPropagation();
      var set = new Set(this.checkedProps);
      if (set.has(id)) {
        set.delete(id);
      } else {
        set.add(id);
      }
      var rental = false;
      if (this.saleOrRent =='rent') {
        rental = true;
      }
      window.bus.$emit('select-prop', {checkedProps:Array.from(set), rental:rental});
    },
    wDetailClick(e) {
      e.stopPropagation();
      e.preventDefault()
      this.showWDetail = !this.showWDetail;
    },
    isNumeric: function (n) {
      return !isNaN(parseFloat(n)) && isFinite(n);
    },
    formatTs(ts) {
      return formatDate(ts);
    },
    propClicked() {
      this.openPropPage(this.prop);
    },
    iconClicked(e, type) {
      e.stopPropagation();
      var rental = false;
      if (this.saleOrRent =='rent') {
        rental = true;
      }
      if(type == 'remove') {
        window.bus.$emit("remove-ref",{id:this.prop._id, rental:rental})
      } else if(type == 'add') {
        window.bus.$emit("add-ref",{id:this.prop._id, rental:rental})
      }
    }
  }
}
</script>


<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped>
.checkbox {
  float: left;
  width: 35px;
  height: 35px;
  padding-left: 10px;
  padding-right: 10px;
}
.checkbox .fa {
  font-size: 20px;
  color: #5cb85c;
}
.weight {
  font-size: 11px;
  color: #777;
}
.weight div{
  display: block;
}
.weight span {
  padding-right: 5px;
}
.img-wrapper .img{
  width: 130px;
  /* display: table-cell; */
  vertical-align: top;
  padding-top: 0px;
  position: relative;
}
.img-wrapper .img img{
  display: inline-block;
  width: 100%;
  height: 83px;
}
.img-wrapper {
  height:100%;
  position:relative;
}
.img-wrapper >div {
  position:relative;
}
.img-wrapper .img >div {
  color: white;
  width: auto;
  text-align: center;
  position: absolute;
  top: 0;
  right: 0;
  padding: 0px 4px 0px;
  /* margin-top: -28px; */
  height: 19px;
  font-size: 12px;
  background: rgba(30,166,27,0.9);
}
.img-wrapper .ts {
  color: #fff;
  text-align: center;
  position: absolute;
  bottom: 10px;
  left: 10px;
  font-size: 10px;
  border-radius: 25px;
  background: black;
  opacity: .7;
  padding: 2px 10px 3px;
  line-height: 12px;
}

.computedHeight {
  height: 145px;
}
.equal{
  font-family: Arial;
  padding-left: 2px;
}
.green {
  color:#42c02e;
}
.red {
  color:#e03131;
}
.address {
  padding-left: 10px;
  white-space: nowrap;
  text-overflow: ellipsis;
  width: 100%;
  overflow: hidden;
  display: block;
  font-weight: 500;
}
.bdrms span{
  padding-right: 3px;
}
.sqft span {
  padding: 0px;
}
.dist span {
  background: #f1f8ec;
  color: #5cb85c;
  font-size: 10px;
  padding: 5px;
  margin-right: 5px;
}
.bdrms, .sqft, .dist{
  font-size: 12px;
  color: #777;
  padding-bottom: 10px;
}
.prop .action {
  color: #42c02e;
  padding-top: 10px;
  border:#5cb85c;
}
.prop .action > span {
  display: flex;
  align-content: center;
  justify-content: center;
}
.prop .action .btn {
  border-radius: 0;
  border: 1px solid #42c02e;
  font-size: 12px;
  color: #42c02e;
  padding: 7px 15px;
  width: 50%;
  float: right;
}
.detail-wrapper {
  width: calc(100% - 130px);
  display: flex;
  align-items: center;
}
.prop .detail {
  display: inline-block;
  float: left;
  font-size: 15px;
}
.prop .detail>div {
  padding: 1px 10px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.prop {
  background: white;
  width: 100%;
  /* height:145px; */
  position: relative;
  padding: 10px 10px 10px 0px;
  border-bottom: 1px solid #f1f1f1;
  display: flex;
  align-items: end;
}

.prop .price{
  padding: 1px 10px;
  color:#666;
  font-size: 13px;
}
.prop .price .through{
  text-decoration: line-through;
}
.price div:first-of-type {
  font-size: 15px!important;
  color: #e03131!important;
}
.price .txt {
  font-size:12px;
  color: #666;
  padding-left: 10px;
}
.prop .stp.sold{
  background: #e03131;
  opacity: 0.9
}
.prop .stp.inactive{
  background: #07aff9;
}
.prop .bdrms span{
  padding-right: 3px;
}
.nopadding{
  padding:0px!important;
}
.prop .addr, .prop .bdrms, .prop .sid{
  padding: 1px 10px;
}
.prop .bdrms{
  font-size: 12px;
  color: #777;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.prop .addr{
  font-size: 15px;
  padding-top: 10px;
}
/* .img .tp, .img .fav{
  color: white;
  display: inline-block;
}
.img .tp{
  background: #e03131;
  padding: 0px 5px;
  border-radius: 7px;
  margin: 4px 0 0 3px;
  font-size: 12px;
}
.img .fav{
  margin: 5px;
  margin-top: 15px;
  padding: 7px 7px 5px 5px;
  font-size: 23px;
  color: #e03131;
}
.img .fav.fa-heart{
  color: rgb(255, 233, 41);
}
.img.blur{
  filter: blur(3px);
  -webkit-filter: blur(3px);
} */
.price.blur{
  filter: blur(2px);
  -webkit-filter: blur(2px);
}
/*.img .fav.fa-heart{
  color:rgb(255, 235, 59);
}*/
.img-wrapper .red{
  background: #e03131 !important;
  color: #fff !important;
}
.img-wrapper .green{
  background: rgba(30, 166, 27, .9) !important;
  color: #fff !important;
}
.img-wrapper .gray{
  background: gray !important;
  color: #fff !important;
}
</style>
