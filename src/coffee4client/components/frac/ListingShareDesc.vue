<template lang="pug">
div
  span#share-title-en
    span(v-if="!isApp")!="RealMaster • "
    span(v-if="prop.tllck && prop.tl") {{prop.tl}}
    span(v-else)
      span(v-if="prop.ltp=='assignment'") Assignment •
      span(v-if="prop.ltp=='exlisting'") Exclusive •
      span(v-if="prop.ltp=='rent' && !prop.cmstn") Landlord Rent •
      span(v-if="prop.ltp=='rent' && prop.cmstn") Rent •
      span
        |  {{prop.priceValStrRed || prop.askingPriceStr}} •
        span(v-if="prop.addr") &nbsp;{{prop.addr}} {{prop.unt||''}},
        | &nbsp;{{prop.city_en || prop.city}} {{prop.prov_en || prop.prop}}
  span#share-desc-en
    | {{isRMProp(prop) ? prop.id +', ' : (prop.sid || prop._id)+', '}}
    | {{ isArray(prop.ptp) ? prop.ptp[0] : (prop.ptype2_en?prop.ptype2_en.join(' '):'') }} {{prop.pstyl_en}}
    | {{propSqft?', '+propSqft+' Sqft, ':''}}
    span(v-if="prop.bcf != 'b'") Bedroom: {{prop.rmbdrm||prop.tbdrms||prop.bdrms}}, Kitchen: {{prop.kch}}, Bathroom: {{prop.rmbthrm||prop.tbthrms||prop.bthrms}}, Parking: {{prop.rmgr||prop.tgr||prop.gr}}.&nbsp;
    | {{getDesc(prop.m)}}
  span#share-title
    span(v-if="!isApp") {{_('RealMaster')}} •
    span(v-if="prop.tllck && prop.tl") {{prop.tl}}
    span(v-else)
      span(v-if="prop.ltp=='assignment'")  {{_('Assignment')}} •
      span(v-if="prop.ltp=='exlisting'")  {{_('Exclusive','realtor sale')}} •
      span(v-if="prop.ltp=='rent' && !prop.cmstn")  {{_('Landlord Rent')}} •
      span(v-if="prop.ltp=='rent' && prop.cmstn")  {{_('Rent','share-title rent')}} •
      span
        |  {{prop.priceValStrRed || prop.askingPriceStr}} •
        span(v-if="prop.addr") &nbsp;{{prop.addr}} {{prop.unt||''}},
        | &nbsp; {{prop.city}} {{prop.prov}}
  span#share-desc
    | {{isRMProp(prop) ? prop.id +', ' : (prop.sid || prop._id)+', '}}
    | {{ isArray(prop.ptp) ? prop.ptp[3] : (prop.ptype2?prop.ptype2.join(' '):'') }}
    | {{propSqft ? ', '+propSqft+' '+_('Sqf','property')+', ':''}}
    span(v-if="prop.bcf != 'b'")
      | {{_('Bedroom')}}: {{prop.rmbdrm||prop.tbdrms||prop.bdrms}}, {{_('Kitchen')}}: {{prop.kch}}, {{_('Bathroom')}}: {{prop.rmbthrm||prop.tbthrms||prop.bthrms}}, {{_('Parking')}}: {{prop.rmgr||prop.tgr||prop.gr}}.&nbsp;
    | {{getDesc(prop.m || prop.m_zh)}}
</template>

<script>
import filters from '../filters'

export default {
  filters:{
    currency:filters.currency
  },
  computed:{
    propSqft:function(){
      let prop = this.prop || {};
      if (!prop.sqft || /-/.test(prop.sqft)) {
        return prop.sqft
      }
      let int = parseInt(prop.sqft);
      if (!isNaN(int)) {
        return int
      } else {
        return prop.sqft;
      }
    }
  },
  props:{
    isApp:{
      type:Boolean,
      default:false
    },
    prop:{
      type:Object
    }
  },
  data () {
    return {
    };
  },
  ready () {
  },
  methods: {
    isRMProp (prop) {
      return /^RM/.test(prop.id);
    },
    isArray (p) {
      return Array.isArray(p);
    },
    getDesc (m) {
      if (!m) {
        return '';
      }
      return m.length > 70 ? m.substr(0,70)+'...' : m;
    }
  },
  events:{}
}
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped>
</style>
