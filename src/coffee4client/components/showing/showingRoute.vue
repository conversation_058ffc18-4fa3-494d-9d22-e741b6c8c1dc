<template lang="pug">
div#map-view.v-cloak(:class="{hidden2:!showRoute}")
  showing-summary(:disp-var="dispVar")
  showing-detail-prop-card.propCard(:disp-var="dispVar",:drvDis='selectedProp.drvDis',:drvMin='selectedProp.drvMin',:prop.sync='selectedProp',:class="{active:showProp}",route=true,@deleteSingle='deleteSingle',@update:prop='updateprop',@update:propStT="updateStartTs",:lbl='showing.lbl')
  header.bar.bar-nav(v-show="showRoute")
    a.icon.fa.fa-back.pull-left(@click='showList()')
    h1.title {{_('Showing Route','showing')}}
    span.pull-right.text(@click='showSummary()') {{_('Summary','showing')}}
  div#sidePane.side-pane(@click="locateMe()")
    span#locateMe.fa.fa-locate
  div#map
  .coverMapLeft
</template>

<script>
import rmsrv_mixins from '../rmsrv_mixins'
import showing_mixin from './showing_mixin'
import showingSummary from './showingSummary'
import showingDetailPropCard from './showingDetailPropCard'
// import mapBox from '/js/map/mapbox'

export default {
  mixins:[rmsrv_mixins,showing_mixin,],
  props: {
    dispVar:{
      type:Object,
      default:function () {
        return {
          isVipRealtor:false,
          allowedEditGrpName:false,
          lang:'zh-cn',
          mapboxKey:''
        }
      }
    }
  },
  components:{
    showingSummary,
    showingDetailPropCard
  },
  data () {
    return {
      grp:null,
      grpName:'',
      showDrop:false,
      mode:'new',
      loading:false,
      inFrame:this.$parent.inFrame,
      showing:{},
      showRoute:false,
      route:true,
      mapbox:null,
      markerSeleted: false,
      selectedProp:{},
      markers:{},
      layers:[],
      showProp:false,
    };
  },
  beforeMount (){
    if (!window.bus) {
      console.error('global bus is required!');
      return;
    }
  },
  mounted () {
    var self = this, bus = window.bus;
    bus.$on('showing-route', function () {
      self.showRoute = true;
      self.showing = self.$parent.showing
      if (!self.mapbox) {
        self.mapbox= new Mapbox();
        self.mapbox.init('map');
        self.mapbox.map.on('load', function() {
          self.createMarker({autoZoom:true});
          self.computeRoute();
        });
      } else {
        self.createMarker({autoZoom:true});
        self.computeRoute();
      }
    });
    bus.$on('close-prop-det', function () {
      self.resetRoute();
    });
  },
  watch:{

  },
  computed:{

  },
  methods: {
    locateMe() {
      this.mapbox.locateMe();
    },
    resetRoute(opt) {
      this.selectedProp = {};
      this.showProp = false;
      if(this.showing.lbl){
        return
      }
      // compute route会reset seq的值，所以要先检查maker
      this.createMarker(); //顺序变了，所有的marker都要重算标签
      this.computeRoute();
    },
    getInnerHtml(prop) {
      let price = currencyFormat(prop.lp, '$', 0);
      let index = prop.index
      let html =  "<div class='showing-marker-wrapper";
      if (!prop.stT) {
        index = '?'
        html = html+ " inactive"
      }
      html = html + "'><div class='red-circle'><div class='index-label'>" +index
      +"</div><div class='time'>"+(prop.stT||'')+"</div></div><div><div class='addr trim'>"+prop.addr
      +"</div><div class='price'>"+price+"</div></div>"
      return html
      // +"</div><div>"+price+"&nbsp;"+(prop.stT||'')+"</div></div>"
      // return "<div class='showing-marker-wrapper'><div class='red-circle'>"
      // +index+"</div><div><div class='addr trim'>"+prop.addr
      // +"</div><div>"+(prop.stT||'')+"</div></div>"
    },
    updateMarker(prop) {
      if (this.markers[prop._id]) {
        // reset innerHtml
        let el = document.getElementById(prop._id)
        if(el) {
          el.innerHTML = this.getInnerHtml(prop);
        }
      }
    },
    createMarker(opt) {
      let self = this;
      let innerHTML = ''
      // console.log(this.showing.props)
      // 删除所有的点，重新画
      for (let marker of Object.values(this.markers)) {
        marker.remove()
      }
      this.markers = {};
      if (self.showing.stPt && self.showing.stPt.lat) {
        innerHTML = "<div class='showing-marker-wrapper start'><div class='red-circle'>S</div><div class='trim addr'>"+self.showing.stPt.addr+"</div></div>"
        let params = {
          position: [self.showing.stPt.lng,self.showing.stPt.lat],
          id:'S',
          className: 'mapboxgl-marker mapboxgl-marker-anchor-center marker-label start',
          innerHTML:innerHTML
        }
        self.markers['start'] = self.mapbox.renderBasicMarker(params,(e)=>{
        });
      }

      for (let prop of this.showing.props) {
        // if (self.markers[prop._id]) {
        //   this.updateMarker(prop);
        // } else {
        let params = {
          position: [prop.lng,prop.lat],
          id:prop._id,
          innerHTML:this.getInnerHtml(prop)
        }
        if (!prop.stT) {
          params.className = 'mapboxgl-marker mapboxgl-marker-anchor-center marker-label inactive';
        }
        // switch到新的prop时先要保存
        self.markers[prop._id] = self.mapbox.renderBasicMarker(params,(e)=>{
          if(self.selectedProp && self.selectedProp._id) {
            window.bus.$emit('close-prop-det');
          }
          self.selectedProp = prop;
          self.showProp = true;
          window.bus.$emit('refresh-height');
          e.stopPropagation();
        });
        // }
      }
      if (opt && opt.autoZoom) {
        self.mapbox.fitMarkers(Object.values(self.markers))
      }
    },
    showList() {
      this.showRoute = false;
      this.resetRoute();
      window.bus.$emit('showing-summary-reset');
      window.bus.$emit('show-list');
    },
    removeRoute () {
      for (let layer of this.layers) {
        if (this.mapbox.map.getLayer(layer)) {
          this.mapbox.map.removeLayer(layer);
          this.mapbox.map.removeSource(layer);
        }
      }
      this.layers = [];
    },
    showRouteOnMap(legs) {
      let legColor = 0;
      let self = this;
      const colorSetp= (255-0)/legs.length

      for (const [index,leg] of legs.entries()) {
        const steps = leg.steps;
        legColor = legColor + colorSetp;

        let source = {
          'type': 'geojson',
          'data': {
            'type': 'FeatureCollection',
            'features': []
          },
        }
        for (let step of steps) {
          source.data.features.push(
            {
              type:'featue',
              geometry:step.geo
            });
        }
        self.layers.push(leg.smry)
        self.mapbox.map.addLayer({
          'id': leg.smry,
          'type': 'line',
          'source': source,
          'layout': {
            'line-join': 'round',
            'line-cap': 'round'
          },
          'paint': {
            'line-color': 'rgb(0,0,'+legColor+')',
            'line-width': 3
            }
        });
        self.mapbox.map.on('click', leg.smry, function(e) {
          let lineWidth = self.mapbox.map.getPaintProperty(leg.smry, 'line-width');
          let weight = lineWidth==3? 8:3
          self.mapbox.map.setPaintProperty(leg.smry, 'line-width', weight);
        });
      }
    },

    mapingRouteAndProp(legs) {
      // ordered id, start, end
      // console.log(legs)
      this.showing.legs = []
      let propIds = this.showing.propSeq.split(';')
      for (const [index,leg] of legs.entries()) {
        leg.f = propIds[index]
        leg.t = propIds[index+1]
        let showingLeg = {f:propIds[index],t:propIds[index+1],smry:leg.summary, dist: (leg.distance/1000).toFixed(1), dur:  Math.ceil(leg.duration/60)}
        if (leg.steps) {
          showingLeg.steps=[]
          for (let step of leg.steps) {
            showingLeg.steps.push({geo:step.geometry})
          }
        }
        this.showing.legs.push(showingLeg);
      }
      // console.log(this.showing.legs);
      // console.log('route')
      this.addDistToProp();
    },

    computeRoute() {
      //f, t
      //if data changed, remove layer and compute again, else return.
      // index change or lat change or prop num change recompute.
      // let url = "https://api.mapbox.com/directions/v5/mapbox/driving-traffic/"
      let url = "https://api.mapbox.com/directions/v5/mapbox/driving/"

      let self = this;
      let seq = this.getPropSequence();
      if (seq.propSeq == this.showing.propSeq) {
        // if first time and showing not changed, load saved routes
        if (this.showing.legs && this.showing.legs.length) {
          if (!this.layers.length) {
            this.showRouteOnMap(this.showing.legs);
          }
          return;
        }
      }
      //clear route on map
      self.removeRoute();
      this.showing.propSeq = seq.propSeq;
      this.showing.ptStr = seq.newPointStr;
      if (seq.propSeq.split(';').length<2) {
        // return 如果少于两个点
        return;
      }
      // 重新显示点的序号
      url=url+encodeURIComponent(seq.newPointStr)
      // TODO:get token from server
      url = url +'.json?steps=true&access_token='+this.dispVar.mapboxKey+'&geometries=geojson&overview=full'
      //different blue
      self.$http.get(url).then(
        function (ret) {
          ret = ret.data;
          if (ret.routes) {
            self.mapingRouteAndProp(ret.routes[0].legs)
            self.showRouteOnMap(this.showing.legs);
          }
        },
        function (ret) {
          ajaxError(ret);
        }
      );
    },
    showSummary(){
      trackEventOnGoogle('showing', 'clickSummary')
      window.bus.$emit('showing-summary');
    },
    // 在当前route页面删除prop，要同时删除地图的点，重画路线
    deleteSingle(prop){
      if(this.showing.lbl){
        return
      }
      this.$parent.deleteSingle(prop);
    },
    updateprop(prop){
      if(this.showing.lbl){
        return
      }
      this.$parent.update(prop);
    },
    updateStartTs(prop) {
      if(this.showing.lbl){
        return
      }
      this.$parent.updateStartTs(prop);
    }
  },
  events: {}
}
</script>
<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped>
.coverMapLeft{
  width: 20px;
  z-index: 9999;
  height: 100%;
  position: absolute;
  left: 0;
  bottom: 0;
}
.modal.active{
  height: auto;
  min-height: auto;
  bottom: 0;
  top: auto;
  }
.propCard{
  position: absolute;
  bottom: 0;
  width: 100%;
  background: #fff;
  z-index: 8;
  display: none;
  margin: 0;
}
.propCard.active {
  display: block;
}
#map-view {
  z-index: 10;
}
#map{
  width: 100%;
  height: calc(100vh - 44px);
  position: absolute;
  top: 44px;
  bottom: 0;
}
div.hidden2{
  visibility: hidden;
  /* z-index: 0; */
  /* position: absolute;
  left: -100%; */
  pointer-events: none;
}
header .text {
  color: #fff;
  padding: 0 10px;
  text-align: center;
  overflow: hidden;
  height: 44px;
  line-height: 44px;
  font-size: 16px !important;
  display: inline-block;
  position: absolute;
  right: 0;
  white-space: nowrap;
}
</style>
