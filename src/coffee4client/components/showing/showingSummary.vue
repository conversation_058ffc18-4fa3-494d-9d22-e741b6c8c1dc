<template lang="pug">
div
  div.backdrop(:class="{'show':showDrop}", @click="reset()")
  div#summary.modal.modal-60pc(:class="{active:showDrop,'fullScreen':showAllSummary}")
    header.bar.bar-nav(@touchstart='onTouchStart($event)',@touchmove='onTouchMove($event)',@touchend='onTouchEnd($event)') {{_('Summary','showing')}}
      span.icon.icon-close.pull-right.time(@click.stop="reset($event)",@touchstart.stop='reset($event)',@touchmove.stop='reset($event)')
      div.blackLine
    div.content
      div.itnrry-infor
          div.infor
            p.time {{_('Total','time')}}
            p.num {{timeToHmin(showing.totlT)}}
          div.infor
            p.time {{_('Driving','time')}}
            p.num {{timeToHmin(showing.allDurn)}}
          div.infor
            p.time {{_('Distance','showing')}}
            p.num {{showing.allDist || 0}}
              span {{_('km','distance')}}
      ul.table-view
        li.table-view-cell(v-for="(prop,index) in summaryLegs")
          div(v-show='prop.drvMin&&prop.drvDis')
            div.grayCircle
              span
              span
            div.time
              span {{prop.drvDis}} {{_('km','distance')}}
              span {{timeToHmin(prop.drvMin)}}
          div
            div.sequence {{index+1}}
            div.num {{prop.addr}}
            span.period(v-show='prop.stT') {{prop.stT}}{{prop.endT ? ' - ' + prop.endT : ''}}
            a.icon.sprite16-18.sprite16-3-9(v-show='prop.addr && !dispVar.isCip',@click="exMap(prop)",@touchstart.stop='exMap(prop)',@touchmove.stop='exMap(prop)')
</template>

<script>
import PageSpinner from '../frac/PageSpinner.vue'
import rmsrv_mixins from '../rmsrv_mixins'
import showing_mixin from './showing_mixin'

export default {
  mixins:[rmsrv_mixins,showing_mixin],
  props: {
    dispVar:{
      type:Object,
      default:function () {
        return {
          isVipRealtor:false,
          allowedEditGrpName:false,
          lang:'zh-cn',
          isCip:false
        }
      }
    }
  },
  components:{},
  data () {
    return {
      grp:null,
      grpName:'',
      showingList:[],
      prop:{},
      showDrop:false,
      mode:'new',
      showing:{},
      summaryLegs:[],
      allDist:0,
      allDurn:0,
      showAllSummary:false,
      moveEndY:0,
      startY:0
    };
  },
  beforeMount (){
    if (!window.bus) {
      console.error('global bus is required!');
      return;
    }
  },
  mounted () {
    var self = this, bus = window.bus;
    bus.$on('showing-summary', function () {
      self.showing = self.$parent.showing;
      self.summaryLegs = [];
      self.showSummary();
    });
    bus.$on('showing-summary-reset', function () {
      self.reset();
    });
    bus.$on('showing-summary-calculate', function () {
      self.calculateSummaryLegs();
    });
  },
  watch:{

  },
  filters:{},
  computed:{},
  methods: {
    hasProp(showing) {
      for(let prop of showing.props) {
        for(let i=0;i<this.prop.length;i++){
         if( prop._id == this.prop[i] )return true
        }
      }
      return false;
    },
    formatTs(ts) {
      return formatDate(ts);
    },
    reset(e){
      if(e) {
        e.stopPropagation();
      }
      this.showDrop = false;
      this.showAllSummary = false;
    },
    showSummary(){
      this.calculateSummaryLegs()
      this.showDrop = !this.showDrop;
      this.showAllSummary = false;
    },
    calculateSummaryLegs(){
      var self=this,sche=[];
      if(self.showing.props && self.showing.legs){
        self.summaryLegs=Object.assign([],self.showing.props);
        self.summaryLegs.forEach(e=>{
          if(e.stT){
            // e.dateT = self.showing.dt +"T"+ e.stT
            sche.push(e)
          }
        })
        // scheduled 排序
        if(sche.length>0){
          sche.sort(function(a, b){
            return a.stT > b.stT ? 1 : -1; // 这里改为大于号
          });
        }
        self.summaryLegs=sche
        //  如果有出发地点，前后为出发地址，没有从第一个prop顺序
        if(self.showing.stPt && self.showing.stPt.addr){
          var startPoint = Object.assign({},self.showing.stPt)
          startPoint.stT = self.calculateStT(self.summaryLegs[0].stT,self.summaryLegs[0].drvMin)
          var endPoint = Object.assign({},self.showing.stPt)
          endPoint.stT = self.calculateEndT(self.summaryLegs[self.summaryLegs.length-1].endT,endPoint.drvMin)
          self.summaryLegs.unshift(startPoint)
          self.summaryLegs.push(endPoint);
        }
      }
    },
    enlarge(e){
      if(e) {
        e.stopPropagation();
      }
      this.showAllSummary =! this.showAllSummary;
      trackEventOnGoogle('showing', 'enlargeSummary')
    },
    // 分别取出开始时间的小时和分钟数，计算驾驶时间的小时和分钟数，分开计算；
    calculateStT(stT,driT){
      var min = driT%60, h = Math.floor(driT/60);
      var startH = Number(stT.slice(0,2)),startMin = Number(stT.slice(3,5));
      var retH,retMin;
      retMin = startMin - min
      // 分钟为负时再减去一小时
      if(retMin < 0){
        retMin += 60;
        retH = startH - h - 1;
      }else{
        retH = startH - h
      }
      return (retH<10 ? "0"+retH : ''+retH) + ':' +  (retMin<10 ? "0"+retMin : ''+retMin );
    },
    calculateEndT(endT,driT){
      var min = driT%60, h = Math.floor(driT/60);
      var endH = Number(endT.slice(0,2)),endMin = Number(endT.slice(3,5));
      var retH,retMin;
      retMin = endMin + min
      // 分钟大于60时再加一小时
      if(retMin > 60){
        retMin -= 60;
        retH = endH + h + 1;
      }else{
        retH = endH + h
      }
      return (retH<10 ? "0"+retH : ''+retH) + ':' +  (retMin<10 ? "0"+retMin : ''+retMin );
    },
    onTouchStart(e) {
      e.preventDefault();
      this.startY = e.changedTouches[0].pageY;
    },
    onTouchMove(e) {
      e.preventDefault();
      this.moveEndY = e.changedTouches[0].pageY;
      // click
      if ( this.moveEndY - this.startY < 0) {
        this.showAllSummary = true
      } else {
        this.showAllSummary = false
      }
      trackEventOnGoogle('showing', 'enlargeSummary')
    },
    onTouchEnd(e) {
      e.preventDefault();
      this.moveEndY = e.changedTouches[0].pageY;
      if ( this.moveEndY - this.startY == 0) {
        this.showAllSummary = !this.showAllSummary;
      }
      trackEventOnGoogle('showing', 'enlargeSummary')
    }
  },
  events: {}
}
</script>
<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped>
.icon-check {
  display: inline-block;
  padding: 3px 0 0 0;
  color: #e03101;
}
#summary{
  z-index: 10;
}
.bar.bar-nav{
  text-align: left;
  font-size: 17px;
  font-weight: 700;
  white-space: nowrap;
  background: #fff;
  color: #333;
  line-height: 44px;
  padding-left: 15px;
  border-bottom: 0.5px solid #F0EEEE;
}
.modal-60pc.active{
  transition: all 0.3s;
}
.backdrop{
  display: none;
}
.backdrop.show{
  display: block;
  z-index: 10;
}
#summary .table-view{
  margin: 0;
}
#summary .table-view-cell{
  padding: 0 15px;
  color: #848484;
  font-size: 16px;
  border: 0;
}
#summary .table-view-cell a.icon{
  font-size: 18px;
  margin-left: 13px;
}
.period{
  color: #777;
  font-size: 12px;
}
.itnrry-infor{
  display: flex;
  padding: 15px;
}
.infor{
  width: 33.3333%;
}
.name{
  font-size: 14px
}
.time{
  color: #777;
}
.num{
  font-size: 20px;
  color: #333;
}
.table-view-cell>div{
  display: flex;
  align-items: center;
  padding: 10px 0;
}
.table-view-cell .num,.table-view-cell .time{
  flex: 1;
  padding-left: 10px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.table-view-cell .time span{
  padding-right: 10px;
}
.grayCircle{
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: center;
  height: 18px;
  width: 18px;
}
.grayCircle span{
  background: #A8A8A8;
  width: 6px;
  height: 6px;
  border-radius: 50%;
}
.sequence{
  background: #e03101;
  width: 18px;
  height: 18px;
  border-radius: 50%;
  color: #fff;
  line-height: 18px;
  text-align: center;
}
.content .num{
  font-size: 16px;
}
.content .time{
  font-size: 14px;
}
.table-view>.table-view-cell:first-child>div:first-child{
  display: none;
}
.blackLine{
  position: absolute;
  width: 25px;
  border-radius: 5px;
  border: 2px solid #d3d3d3;
  left: 50%;
  margin-left: -12.5px;
  top: 6px;
}
.fullScreen{
  height: calc(100% - 44px);
  top: 44px;
}
.table-view .table-view-cell:last-child{
  padding-bottom: 20px !important;
}
.table-view-cell span {
    vertical-align: middle;
}
</style>
