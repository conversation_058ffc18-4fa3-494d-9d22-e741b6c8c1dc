<template lang="pug">
div(:class="{'lbl':lbl}")
  .prop-detail-wrapper(v-show='(prop.id || prop.sid)',@click="goDetails(prop)")
    div.sort(v-show="prop.stT && !route") {{index}}
    div.img-wrapper
      lazy-image.card-la-avt(:src="picUrl",:imgstyle="'float: left;width: 100%;height: 61px;'")
      span.id
        //- span {{_('ID','prop')}}:&nbsp;
        span.sid(v-show="prop.id && isRMProp(prop)")
          | {{ prop.id }}
          span.sid(v-show="prop.ml_num || prop.sid")
            | ({{prop.ml_num || prop.sid}})
        span.sid(v-show="!isRMProp(prop)")
          | {{ prop.sid || prop.id }}
    .prop
      div.addr
        span(v-show='prop.unt') {{prop.unt}} 
        span {{prop.addr}}
      div.price-wrapper
        span.price(v-show="soldOrLeased && prop.sp") {{prop.sp | currency('$', 0)}}
        span.price(:class="{'through':soldOrLeased}" v-show="(prop.lp || prop.lpr)") {{(prop.lp || prop.lpr) | currency('$', 0)}}
        span.price-change(v-if="prop.lstStr && prop.lst == 'Sc'") {{prop.lstStr}}
        span.stp(:class="prop.tagColor || 'gray'",v-show="prop.saleTpTag")
          | {{prop.saleTpTag}}
      div.bdrms
        span(v-show='prop.rmbdrm || prop.tbdrms || prop.bdrms')
          span.fa.fa-rmbed
          span  {{prop.rmbdrm || prop.tbdrms || prop.bdrms}}
        span(v-show='prop.rmbthrm || prop.tbthrms || prop.bthrms')
          span.fa.fa-rmbath
          span  {{prop.rmbthrm || prop.tbthrms || prop.bthrms}}
        span(v-show='prop.rmgr || prop.tgr || prop.gr')
          span.fa.fa-rmcar
          span  {{prop.rmgr || prop.tgr || prop.gr}}
        span | {{prop.ptype2?prop.ptype2.join(','):prop.pstyl}}
    .right-close(v-show="route",@click.stop="close($event)")
      span.fa.fa-check-circle
    .right-nav(v-show="prop.src!='man'&&!route")
        span.icon.icon-right-nav
    .right-nav(v-show="prop.src!='man'&&!route",@click.stop='goToPropMap()')
        span.link {{_("MAP")}}
  div.prop-detail-wrapper(v-show='!(prop.id || prop.sid)')
    div.sort(v-show="prop.stT && !route") {{index}}
    div.faddr
      p {{prop.addr}} , {{prop.city}}
      p {{prop.prov}}
    //- span.icon.icon-edit
  div.toggle-line
    span {{_('Add to Buylist')}}
    span.toggle.ftm.pull-right(:class="{active:prop.inBuylist}", @click="addToBuylist()")
      span.toggle-handle(:class="{active:prop.inBuylist}")
  // div.toggle-line
  //   span {{_('Booking Failed')}}
  //   span.toggle.ftm.pull-right(:class="{active:prop.failed}", @click="onBookingFailed()")
  //     span.toggle-handle(:class="{active:prop.failed}")
  div.memo-wrapper
    .showing-time-wrapper
      label.startTime
        span.sprite16-18.sprite16-1-6.margin-right-3(:class='{"sprite16-2-6":lbl}')
        span(style='flex: 1;',v-show='prop.stT') {{prop.stT}}
        span(style='flex: 1;',v-show='!prop.stT') {{_('Start time','showing')}}
        input.noStT(type="time", v-model.lazy="prop.stT",step='300',@change="checkTime()",:disabled='lbl')
        span.icon.icon-right-nav(v-show='!lbl')
      label.durn
        span.sprite16-18.sprite16-1-7.margin-right-3(:class='{"sprite16-2-7":lbl}')
        span {{_('Stay','showing')}}:
        select.select(v-model="prop.durtn",@change="checkTime()",v-show='!lbl') {{showStayTime}}
          option(v-for='time in itmeList',:value='time.val',:selected="time.val == prop.durtn ? 'selected' : ''") {{time.text}}
        span.input(v-show='lbl') {{showStayTime}}
        span.icon.icon-right-nav(v-show='!lbl')
      div.drv
        span.sprite16-18.sprite16-3-2.margin-right-3(:class='{"sprite16-4-2":lbl}')
        span.dist(v-show='drvDis && drvMin') {{drvDis | fix}}{{_('km','distance')}} {{drvMin || 0}}{{_('min','time')}}
    div.showing-time-wrapper.tag
      span.gd(:class="{'active':checktags(tag)}", @click="onTagClicked(tag)",v-for='tag in leftTags') {{_(tag,'showing')}}
    label.internal-memo
      div.sprite16-18.sprite16-3-3.margin-right-3
      textarea.red(rows="1",:id='"pm"+(route?-1:prop.index)',type="text",v-model="prop.pm", :placeholder="_('Private memo. i.e.:lockbox info ...')",@keyup='calcHeight("pm"+prop.index)',@change='calcHeight("pm"+prop.index);updateProp()',@blur='prop.pm = checkInput(prop.pm)')
      div.red.photo-show {{prop.pm}}
    label.internal-memo
      div.sprite16-18.sprite16-3-5.margin-right-3
      textarea(rows="1",:id='"m"+(route?-1:prop.index)',type="text", v-model="prop.m", :placeholder="_('Shared Memo','showing')",@keyup='calcHeight("m"+prop.index)',@change='calcHeight("m"+prop.index);updateProp()',@blur='prop.m = checkInput(prop.m)')
      div.photo-show {{prop.m}}
  .contact(:class='{"contactSub":route}')
    //- .contact-right.pull-left
    a.btn-negative.save(@click="close()",v-show="route")
      span {{_('Reroute','showing')}}
    .contat-agent(@click="contactAgent()",v-show='(prop.id || prop.sid)')
      span.sprite16-18.sprite16-3-7.margin-right-3
      span {{_('Contact Agent','showing')}}
    .trash(@click="deleteSingle()")
      span.sprite16-18.sprite16-3-8.margin-right-3(:class='{"sprite16-4-8":lbl}')
    .direction(, @click="goMap(prop)")
      span.sprite16-21.sprite16-3-9.margin-right-3
      span {{_('Direction','showing')}}
</template>

<script>
import PageSpinner from '../frac/PageSpinner.vue'
import showing_mixin from './showing_mixin'
import FlashMessage from '../frac/FlashMessage'
import pagedata_mixins from '../pagedata_mixins'
import filters from '../filters'
import LazyImage from '../frac/LazyImage.vue'
import rmsrv_mixins from '../rmsrv_mixins'

export default {
  mixins:[showing_mixin,pagedata_mixins,rmsrv_mixins],
  watch:{
  },
  filters:{
    currency:filters.currency,
    fix:function(num){
      if(!num ){
        return
      }
      num = Number(num)
      if( num < 100){
        return num
      }else{
        return num.toFixed(0)
      }
    }
  },
  components:{
    PageSpinner,
    FlashMessage,
    LazyImage
  },
  props: {
    prop: {
      type: Object,
      default: function () {
        return {
          fav:false,
          picUrls:[],
          tags:[],
          durtn :60,
          inBuylist:false,
          failed:false,
          pm:'',
          m:'',
        }
      }
    },
    dispVar:{
      type:Object,
      default:function () {
        return {fav:false}
      }
    },
    route:{
      type:Boolean,
      default:false
    },
    hasGoogleService:{
      type:Boolean,
      default:false
    },
    lbl:{
      type:Boolean,
      default:false
    },
    index:{
      type:Number,
      default:0
    },
    drvMin:{
      default:0
    },
    drvDis:{
      default:""
    }
  },
  data () {
    return {
      loading:false,
      showBackdrop:false,
      showing:{},
      curUser:'All',
      isnew:false,
      project:this.prop,
      itmeList:[{
        val:30,
        text:'30'+ this._('min','time')
        },{
        val:60,
        text:'60'+ this._('min','time')
        },{
        val:90,
        text:'90'+ this._('min','time')
        },{
        val:120,
        text:'120'+ this._('min','time')
        }],
      staticTags:['Deficient','Not interested','Interested','To put offer'],
      leftTags:[],
      rightTags:[],
      selectPropDurtn:false,
      // duration:15,
      // showTs:'13:00',
      // internalM:'',
      // shareM:''
    };
  },
  mounted () {
    if (!window.bus) {
      console.error('global bus is required!');
      return;
    }
    // this.internalM = this.prop.internalM;
    // this.showTs = this.prop.showTs;
    // this.duration = this.prop.duration;
    // this.shareM = this.prop.shareM;
    var bus = window.bus, self = this;
    if(this.prop.tags ){
      self.leftTags = this.prop.tags
      self.rightTags = this.staticTags.filter(item => !this.prop.tags.some(ele=>ele===item));
      self.leftTags = self.leftTags.concat(self.rightTags)
    }else{
      self.leftTags = this.staticTags
    }
    self.$nextTick(function () {
      self.refreshHeight()
    })
    bus.$on('refresh-height', function (reset) {
      setTimeout(() => {
        self.refreshHeight(reset);
      }, 100);
    });
  },
  computed:{
    picUrl:function() {
      return this.prop.thumbUrl || '/img/noPic.png';
    },
    soldOrLeased: function() {
      return this.prop.saleTpTag_en == 'Sold'|| ['Sld','Lsd','Pnd','Cld'].includes(this.prop.lst);
    },
    computedSaletp: function() {
      let saletp = this.prop.saletp;
      if (saletp) {
        if (Array.isArray(saletp)) {
          return saletp.join(' ');
        } else {
          return saletp;
        }
      }
      return this.prop.lpunt;
    },
    saletpIsSale: function () {
      if (!this.prop.saletp_en) {
        return true;
      }
      if (/sale/.test(this.prop.saletp_en.toString().toLowerCase())) {
        return true;
      }
      return false;
    },
    ifHaveDrvMin:function(){
      if (this.drvMin > 0 || this.drvMin == null) {
        return true;
      }
      return false
    },
    computedBgImg: function () {
      if (this.lazyExist && !this.intersected) {
        return 'url("/img/noPic.png")';
      }
      if (this.prop && this.prop.thumbUrl) {
        return 'url(' + this.prop.thumbUrl + '), url("/img/noPic.png")';
      } else {
        return '';
      }
    },
    showStayTime: function (){
      var text = ''
      this.itmeList.forEach(ele=>{
        if (ele.val == this.prop.durtn){
          text = ele.text
        }
      })
      return text;
    },
  },
  methods: {
    goToPropMap(){
      setLoaderVisibility('block')
      var self = this;
      var lat = this.prop.lat;
      var lng = this.prop.lng;
      var saletp = this.saletpIsSale?'sale':'lease';
      // NOTE: &zoom=13 deprecated, use delta=0.0025
      var url = `/1.5/mapSearch?loc=${lat},${lng}&hMarker=1&mode=map&saletp=${saletp}&propId=${this.prop._id}`;
      if (!this.dispVar.isApp) {
        url = '/adPage/needAPP';
        return this.redirect(url);
      }
      var mode = 'sale',appmode = 'mls'
      if(self.isRMProp()){
        mode = this.prop.ltp
        appmode = 'rm'
      }
      url += `&mapmode=${mode}&appmode=${appmode}&resetmap=1`
      RMSrv.setItemObj({key:'map.feature.prop.current_saved_search',value:null,stringify:false,store:false})
      RMSrv.setItemObj({key:'map.feature.prop.nearby_prop',value:this.prop,stringify:false,store:false},function(){
        window.location = url;
      })
      if (self.isNewerVer(self.dispVar.coreVer,'6.2.7') || (!RMSrv.isIOS() && self.hasGoogleService)) {
        return;
      }
      this.redirect(url);
    },
    refreshHeight(reset){
      var idx = this.prop.index;
      var resetPm = reset,resetM = reset;
      if(!this.prop.pm || this.prop.pm.length == 0){
        resetPm = true;
      }
      if(!this.prop.m || this.prop.m.length == 0){
        resetM = true;
      }
      this.calcHeight('pm'+idx,resetPm)
      this.calcHeight('m'+idx,resetM)
    },
    calcHeight(id,reset){
      if(this.route){
        var idArr=id.split('m');
        id = idArr[0]+'m-1'
      }
      var textarea = document.querySelector('#'+id);
      if(textarea){
        var height = textarea.scrollHeight;
        if(height < 21 || reset){
          height = 21;
        }else if((height > 100) && this.route){
          height = 100;
        }
        textarea.style.height = `${height}px`;
      }
    },
    goMap(prop){
      if(this.route){
        trackEventOnGoogle('showing', 'directionInRoute')
      }else{
        trackEventOnGoogle('showing', 'directionInDetail')
      }
      bus.$emit('wait-open-map', {prop});
    },
    updateProp() {
      if(this.lbl){
        return
      }
      this.$emit('update:prop', this.prop);
    },
    checkTime(info){
      if(this.lbl){
        return
      }
      // booking failed之后再次设置开始时间
      // if(this.prop.failed && !info){
      //   info = 'clear';
      //   window.bus.$emit('flash-message', this.$parent._(this.$parent.strings.failedStartTimeError.key,this.$parent.strings.failedStartTimeError.ctx));
      // }
      if(info=='clear') this.prop.stT=null,this.prop.endT=null
      if(this.prop.stT){
        let min = Number(this.prop.stT.slice(-2))
        let hour = Number(this.prop.stT.slice(0,2))
        // if(min%5 != 0){
        //   min = Math.floor(min/5)*5
        // }
        hour = this.formatDoubleDigit(hour)
        min = this.formatDoubleDigit(min)
        this.prop.stT= hour + ":" + min;
        this.prop.endT=this.calculateEndTime(hour,min,this.prop.durtn)
        // this.prop.dateT = this.$parent.showing.dt +"T"+ this.prop.stT
        // endtime 这样计算在chrome和safari下结果不同。
        //不要用时间和字符串互相转化，写一个函数计算时间，60进制的。得到时间即可。
        // var endTime = new Date(new Date(this.prop.dateT).getTime()+(this.prop.durtn*60*1000));
        // this.prop.endT=endTime.toTimeString().slice(0,5);
      }
      this.$emit('update:propStT', this.prop);
    },
    contactAgent() {
      if(this.isRMProp(this.prop)) {
        let brkg = {}
        if (this.prop.adrltr) {
          brkg =  this.prop.adrltr;
        }
        bus.$emit('show-rmbrkg', {prop:this.prop,picUrls: this.prop.picUrls, brkg:brkg});
      }
      else {
        bus.$emit('show-brkg', {prop:this.prop});
      }
    },
    isRMProp() {
      return /^RM/.test(this.prop.id);
    },
    goDetails(prop){
      var id = /^RM1/.test(this.prop.id)?this.prop.id:this.prop._id;
      let url = '/1.5/prop/detail/inapp?lang=zh-cn&id='+id+'&mode=map&showShowingIcon=1';
      RMSrv.openTBrowser(url);
      trackEventOnGoogle('showing', 'clickPropDetailInApp')
    },
    deleteSingle(){
      this.$emit('deleteSingle', this.prop);
    },
    onTagClicked(fle) {
      if(this.lbl){
        return
      }
      if(!this.prop.tags){
        this.prop.tags = []
      }
      var i= this.prop.tags.indexOf(fle);
      if(i==-1){
        this.prop.tags.push(fle);
      }else {
        this.prop.tags.splice(i,1)
      }
      this.$emit('update:prop', this.prop);
    },
    checktags(tag){
      if(!this.prop.tags){
        this.prop.tags = []
      }
      let i =this.prop.tags.indexOf(tag)
      if(i!=-1) return true
      else  return false
    },
    close(e){
      // console.log(123)
      if(e) {
        e.stopPropagation();
      }
      this.refreshHeight(true);
      window.bus.$emit('close-prop-det');
    },
    chooseDurtnTime(time){
      if(this.lbl){
        return
      }
      this.prop.durtn=time;
      // this.selectPropDurtn=false
    },
    showDurtnTime(){
      if(this.lbl){
        return
      }
      if(this.selectPropDurtn==true)
        this.selectPropDurtn=false
      else
        this.selectPropDurtn=true
    },
    addToBuylist(){
      this.prop.bTs = new Date();
      this.prop.inBuylist = !this.prop.inBuylist;
      this.$emit('update:prop', this.prop);
    },
    onBookingFailed(){
      this.prop.failed = !this.prop.failed;
      this.checkTime('clear')
      this.$emit('update:prop', this.prop);
    },
    checkInput(content){
      if (content && content.length){
        return replaceJSContent(content);
      }
      return content;
    }
  }
}

</script>


<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped lang='scss'>
.toggle-line{
  padding: 10px 16px;
  font-size: 13px;
  .toggle {
    width: 54px;
    height: 24px;
    background: #ddd!important;
    &:before {
      content: "";
    }
    .toggle-handle {
      top: 0px;
      width: 20px;
      height: 20px;
      transform: translate3d(0px, 0px, 0px)!important;
      &.active{
        transform: translate3d(32px, 0px, 0px) !important;
      }
    }
    &.active {
      background-color: #3080B0!important;
      border: 2px solid #3080B0;

      &:before {
        content: "";
      }
      .toggle-handle {
        left: -1px;
        border-color: #3080B0;
        -webkit-transform: translate3d(32px, 0, 0);
        -ms-transform: translate3d(32px, 0, 0);
        transform: translate3d(32px, 0, 0);
      }
    }
  }
}
input,textarea{
  color: #333;
}
.icon-right-nav {
  font-size:14px;
  padding-left:4px;
  color:#777;
}
.showing-time-wrapper {
  display: flex;
  font-size: 13px;
  align-items: center;
  color: #333;
}
.showing-time-wrapper input {
  font-size: 13px;
  border: none;
  height: auto;
  background: #fff;
}
.showing-time-wrapper .fa-clock-o {
  font-size: 20px;
}
.showing-time-wrapper .icon, .showing-time-wrapper .fa{
  padding-right: 6px;
}
.showing-time-wrapper .fa-hourglass-half{
  font-size: 16px;
}
.showing-time-wrapper .fa-car{
  font-size: 15px;
}
.showing-time-wrapper, .internal-memo, .share-memo {
  padding: 5px;
  display: flex;
  overflow: auto;
  margin: 0;
  flex-shrink: 1;
  align-items: center;
}
.showing-time-wrapper .gd {
  margin: 2px 10px 2px 0;
  padding: 2px 10px;
  font-size: 15px;
  background: white;
  border: 0.5px solid #ddd;
  box-shadow:0px 1px 3px #ddd;
  border-radius: 15px;
  color: #999;
  white-space: nowrap;
}
.showing-time-wrapper .gd.active {
  background-color: #e03131;
  color: #fff;
}
/* .showing-time-wrapper .gd:not(:first-child) {
  margin-left: 10px;
} */
.showing-time-wrapper div,.showing-time-wrapper label  {
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-weight: inherit;
  margin: 0;
  position: relative;
  padding: 5px 0;
}
.startTime{
  width: 31%;
}
.durn{
  width: 35%;
}
.drv{
  width: 34%;
}
.showing-time-wrapper span{
  white-space: nowrap;
}

.showing-time-wrapper .icon-right-nav {
  padding-right: 1px;
  color: #333;
}

.price-wrapper .fa-car{
  font-size: 14px;
}
.price-wrapper .price-change{
  color: #666;
  font-size: 12px;
}
.memo-wrapper {
  background: white;
  padding:5px 10px;
}
.internal-memo textarea, .share-memo  textarea {
  flex: 1;
  font-size: 13px;
}
.treb-link{
  padding-left: 10px;
  color: #428bca;
}
.prop-detail-wrapper {
  width: 100%;
  display: flex;
  position: relative;
  align-items: center;
  background: #fff;
  border-bottom: 0.5px solid rgb(245,245,245);
}
.prop-detail-wrapper input{
  outline: 0;
  flex: 1;
  border: 0;
  background: #fff;
  margin-left: 10px;
}
.prop-detail-wrapper .prop{
  width: calc(100% - 166px);
  padding: 0;
  flex: 1;
}

.prop .bdrms .num {
  font-size: 14px;
  /* margin-right: 10px; */
}
.prop .bdrms span.sid{
  float: right;
  padding-right: 0;
}
/*.prop .bdrms span.sid.promo{
  color: #e03131;
}*/
.prop .bdrms span.sid .ad{
  color: #e03131;
  font-size: 13px;
  border: 0.5px solid #e03131;
  margin-right: 7px;
  padding: 1px 5px;
  border-radius: 4px;
}
.prop .bdrms span.sid .ad .fa{
  padding-right: 3px;
  vertical-align: top;
}
.prop .addr, .prop .bdrms{
  padding: 1px 0 1px 10px;
}
.prop .bdrms{
  font-size: 13px;
  color: #777;
  overflow: hidden;
  text-overflow: ellipsis;
}
.prop .addr{
  font-size: 15px;
  color: #333;
  width: 100%;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.img .tp, .img .fav{
  color: white;
  display: inline-block;
}
.prop-detail-wrapper .right-nav {
  padding: 0;
}
.content {
  overflow-y: scroll;
  height: 100%;
}
.summary span {
  padding-right: 5px;
}
.content{
  background: #f1f1f1;
}
.content .card{
  border: none;
  margin: 0;
  border-radius: 0px;
}
.card .card-header{
  padding: 15px 10px;
  font-weight: bold;
  font-size: 16px;
  border-bottom: 0.5px solid #f1f1f1;
}
.card .card-header .text {
  color: white;
  font-weight: bold;
}
.card .card-header .pull-right{
  font-size: 14px;
  color: #428bca;
  font-weight: normal;
}
.card .card-header .pull-right .icon{
  font-size: 13px;
}
.content > div{
  background: white;
}
.content > div.card{
  margin-top: 15px;
}
.content .btn{
  border-radius: 0;
  height: 30px;
  font-size: 14px;
  width: 50px;
  padding-top: 6px;
}
.memo-wrapper input,.memo-wrapper textarea{
  margin-bottom: 0;
  display: inline-block;
  height: 30px;
  border: none;
  background: white;
  margin-left: 3px;
  font-size: 13px;
  padding: 0 5px;
  font-weight: 400;
}
.memo-wrapper textarea{
  resize: none;
  word-wrap: break-word;
  padding: 0 5px 0 10px;
}
.memo-wrapper.hide{
  height: 0;
  overflow: hidden;
}
.row .opt{
  color: #428bca;
  font-size: 13px;
  padding-left: 8px;
}
.card .val{
  color: #e03131;
}
.card .padding{
  height: 60px;
}
.contact-right,.contact {
  display: flex;
  flex:1;
  color:#2F80B0;

  font-size: 13px;
}
.contact .icon {
  font-size: 18px;
  padding-right: 5px;
}
.contact-right div span{
  vertical-align: middle;
}
.contact {
  padding:10px 10px 10px 15px;
  justify-content: space-between;
  align-items: center;
  background: #fff;
  border-top: 0.5px solid #f1f1f1;
}
.contactSub{
  padding: 0 10px 0 0;
  border-top: 0.5px solid #f1f1f1;
}
/* .contact .direction ,.contact .contat-agent {
  padding-right: 15px;
} */
.contact .direction span,.contact .contat-agent span{
  vertical-align: middle;
}
.contactSub .direction ,.contactSub .contat-agent, .contactSub .trash {
  line-height: 50px;
}
.select{
font-size: 13px;
  margin-bottom: 0;
  background: white;
  padding: 0 0 0 3px;
  box-shadow: none;
  border: 0;
  width: 44%;
}
.save{
  width: 30%;
  height: 50px;
  background: #e03131;
  text-align: center;
  line-height: 50px;
  font-size: 18px;
}
.showing-time-wrapper.tag{
  margin-right: 5px;
}
.showing-time-wrapper .options{
  display: block;
  position: absolute;
  left: 70%;
  background: rgba(255,255,255,.9);
  width: 50px;
  border-radius: 3px;
  border: 0.5px solid #c0c0c0;
  box-shadow: 0px 3px 6px #c0c0c0;
  text-align: center;
}
.option{
  margin: 0;
}
.option:not(:last-child){
  border-bottom: 0.5px solid #c0c0c0;
}
.faddr{
  width: calc(100% - 40px);
  margin-right: 16px;
}
.faddr p{
  font-size: 15px;
  color: #333;
  width: 100%;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
/* input[type="time"]::-webkit-clear-button {
    display: none;
} */
label span.icon-close{
  position: absolute;
  right: 4px;
  top: 50%;
  margin-top: -10px;
  font-size: 20px;
}
label span, label input ,label select{
  vertical-align: middle;
}
.showing-time-wrapper label input{
  height: 20px;
  padding: 0;
  margin: 0;
}
.dist{
  flex: 1;
}
.lbl .input,.lbl .dist{
  color: #aaa;
  flex: 1;
  padding: 0 5px;
}
.lbl .trash{
  color: #aaa
}
.lbl .showing-time-wrapper .gd.active{
  background: #aaa;
}
.lbl .showing-time-wrapper{
  color: #aaa
}
.noStT{
  width: 1px;
  text-indent: -1000px;
}
::-webkit-input-placeholder {
  color: #a0a0a0;
}
:-moz-placeholder {/* Firefox 18- */
  color: #a0a0a0;
}
::-moz-placeholder{/* Firefox 19+ */
 color: #a0a0a0;
}
:-ms-input-placeholder {
  color: #a0a0a0;
}
.link{
  color: #2F80B0 !important;
}

.photo-show{
  display: none;
  padding: 0 5px 0 10px;
  color: #333;
  margin-bottom: 0;
  background: #fff;
  margin-left: 3px;
  font-size: 13px;
  font-weight: 400;
}
.download{
  .showing-time-wrapper .gd{
    box-shadow:initial;
  }
  .toggle-line {
    display: none;
  }
  .contact{
    display: none;
  }
  textarea{
    display: none;
  }
  .photo-show{
    display: block;
  }
}
.price-wrapper .red{
  background: #e03131 !important;
  color: #fff !important;
}
.price-wrapper .green{
  background: rgba(30, 166, 27, .9) !important;
  color: #fff !important;
}
.price-wrapper .gray{
  background: gray !important;
  color: #fff !important;
}
</style>
