<template lang="pug">
div
  prop-fav-actions(:loading.sync="loading", :disp-var="dispVar")
  page-spinner(:loading.sync="loading")
  div.WSBridge(style="display:none")
    listing-share-desc(:prop.sync="prop", :is-app="dispVar.isApp")
    div#wx-title {{_('RealMaster')}} {{computedCnt}} {{_('Results')}}
    div#wx-desc
      span(v-if="user && agentCard")
        | {{_('Shared by')}} {{user.fnm?user.fnm:user.nm}},{{user.mbl}},{{user.eml}}
      span(v-else)
        | {{_('RealMaster Multiple Properties Sharing')}}
    div#wx-image
      span(v-if="user && agentCard")
        | {{user.avt || '/img/logo.png'}}
      span(v-else)
        | /img/logo.png
    div#wx-url {{fullUrl}}
    div#share-image {{shareImage}}
    div#share-data {{shareData}}

  header.bar.bar-nav
    a.icon.fa.fa-back.pull-left(href='javascript:;', @click="goBackIndex()", v-if="dispVar.isApp")
    h1.title
      span(v-if="!barTitle")
        | {{computedCnt}} {{_('results')}}
      span(v-if="barTitle") {{barTitle}}
    span.changeLang(v-show='fromShare')
      a.icon.pull-right(href='javascript:void 0',v-for='lang in dispVar.languageAbbrObj',@click="createLangTemplate(locale)", v-show="isActive(lang.k)") {{lang.v}}
  //- div#searchWrapper.bar.bar-standard.bar-header-secondary (v-if='fromShare')
  //-   input#searchInput(type="text",
  //-     :placeholder="computed_placeholder",
  //-     required,
  //-     v-model='searchStr',
  //-     onclick="this.setSelectionRange(0, this.value.length)",
  //-     @focus='focused=true',
  //-     @blur='lostFocus()')
  //-   button.btn.btn-positive.pull-right.icon.fa.fa-rmsearch(@click.stop.prevent='searchAutocomplete()')

  brkg-phone-list(:disp-var="dispVar",v-if="dispVar.isApp")
  rm-brkg-phone-list(v-if="dispVar.isApp", :cur-brkg="curBrkg")
  div#propDetailModal.modal
    prop-detail(:w-id="wId", :owner.sync="dispVar.ownerData", :from-share="fromShare", :user-form="userForm", :dis="true", :show-close='true', :loading="loading", :signup-title="signupTitle")
  prop-detail-map-menu(:prop="prop", :disp-var="dispVar")

  div#SignupModal.modal.modal-fade(v-if="(prop.rmcontact && !dispVar.isRealtor)")
    header.bar.bar-nav
      h1.title
    div.content
      sign-up-form(:feedurl="feedurl",:owner="dispVar.ownerData", :user-form="userForm", :title="signupTitle", @click.prevent.stop="noop(e)")
      .close(@click="toggleModal('SignupModal')")
        img(src='/img/staging/close.png')

  div#schoolDetailModal.modal
    school-detail(:disp-var="dispVar")

  prop-need-login(:message.sync="message")
  //- prop3p-promote-modal
  //- prop-create-wepage-modal(:prop.sync="prop")
  share-dialog(:w-dl.sync="wDl", :w-sign.sync="wSign", :disp-var="dispVar", :prop.sync="prop")
  flash-message
  div#detail-map-holder
  //- div.content#autocompleteWrapper(v-show="focused")
  //-   autocomplete-list-wrapper(:show-autocompletes="showAutocompletes", :hist="hist", :glist="gautocompletes", :alist="autocompletes")
  div.content(:class="{oldVerBrowser:oldVerBrowser}", v-show="!focused")
    div()
      div#errMsg(style="padding:14px 0 0 15px;", v-show="dispVar.isApp && (err || ERR_NO_PROP_FOUND) && !loading")
        div.err-wrapper(v-show='err') {{err}}
        div#notFoundTip(v-show='ERR_NO_PROP_FOUND') {{_('Not found any properties.')}}
      div#foundAddrTl(v-show="addrSearchResults.length > 0 && !err")
        span {{_('Matched Addresses')}}
        //- span.pull-right 
      div#foundAddrWrapper(v-show="dispVar.isApp")
        div.matched-address-list(v-for="r in addrSearchResults")
          div.matched-address-addr {{r.addr}}{{r.city?', '+r.city:''}}{{r.prov?', '+r.prov:''}}
          div.matched-address-action
            div.href-wrapper(@click="openInMapSearch(r.loc[1], r.loc[0])")
              a(href='javascript:;')
                i.fa.fa-map-marker(style="padding-right: 5px;")
                | {{_('Find Properties')}}
            div.href-wrapper(@click="findSchools(r)")
              | {{_('Find Schools')}}
      div#ulWrapper(v-show='propItems.length > 0 && !focused')
        //- div.ul-title {{_('Matched Properties')}}
        prop-list(:list.sync='propItems', :disp-var="dispVar")
      button.btn.btn-positive.btn-block.btn-mar-top.btn-long(v-show='dispVar.isApp && dispVar.isLoggedIn && hasMore', @click.stop.prevent='showMore();')
        | {{_('Show More')}}
      a.btn.btn-positive.btn-block.btn-mar-top.btn-long(href="/1.5/user/login", v-show="dispVar.isApp && !dispVar.isLoggedIn && !loading")
        | {{_('Login For More Results')}}
    //- div(v-show='fromShare')
    //-   disclaimer(:no-bot-bar="true")
    //- div(v-if="agentCard && user")
    //-   wechat-qr-modal(:owner.sync="dispVar.ownerData", :qrcd.sync="qrcd")

  //- div(v-if="fromShare && wDl")
  //-   get-app-bar(:dtl-in-app="true", :w-dl.sync="wDl", :owner.sync="dispVar.ownerData", :params="params")

  //- user-brief-info(:owner.sync="dispVar.ownerData", :qrcd.sync="qrcd")
</template>

<script>
import PageSpinner from './frac/PageSpinner.vue'
import ListingShareDesc from './frac/ListingShareDesc.vue'
import PropDetail from './frac/PropDetail.vue'
import PropDetailMapMenu from './frac/PropDetailMapMenu.vue'
import SchoolDetail from './frac/SchoolDetail.vue'
import PropNeedLogin from './frac/PropNeedLogin.vue'
// import Prop3pPromoteModal from './frac/Prop3pPromoteModal.vue'
// import PropCreateWepageModal from './frac/PropCreateWepageModal.vue'
import PropList  from './frac/PropList.vue'
// import Disclaimer  from './frac/Disclaimer.vue'
import BrkgPhoneList  from './frac/BrkgPhoneList.vue'
import RmBrkgPhoneList  from './frac/RmBrkgPhoneList.vue'

import ShareDialog from './frac/ShareDialog2.vue'
// import WechatQrModal from './frac/WechatQrModal.vue'
// import GetAppBar from './frac/GetAppBar.vue'
// import UserBriefInfo from './frac/UserBriefInfo.vue'
import FlashMessage from './frac/FlashMessage.vue'
import pagedata_mixins from './pagedata_mixins'
import PropFavActions from './frac/PropFavActions.vue'
import rmsrv_mixins from './rmsrv_mixins'
import prop_mixins from './prop_mixins'
import SignUpForm from './frac/SignUpForm.vue'
// import autocomplete_mixins from '../components/autocomplete_mixins'
// import AutocompleteListWrapper  from '../components/frac/AutocompleteListWrapper.vue'
export default {
  mixins: [pagedata_mixins,rmsrv_mixins,prop_mixins],//autocomplete_mixins
  watch:{
  },
  computed:{
    curBrkg: function () {
      if (this.prop.ltp == 'rent' && !this.prop.cmstn) {
        return this.prop.adrltr;
      }
      var userFollowedRltr = this.dispVar.userFollowedRltr;
      if (userFollowedRltr && userFollowedRltr._id) {
        return userFollowedRltr;
      }
      if (this.prop.adrltr) {
        return this.prop.adrltr;
      }
      // if (this.prop.uid) {
      //   return this.userDict[this.prop.uid] || {};
      // }
      return {}
    },
    params: function () {
      return this.searchStr?'m_ids=' + this.searchStr:'';
    },
    ERR_NO_PROP_FOUND: function () {
      return this.propItems.length < 1 && !this.err;
    },
    shareImage: function () {
      return this.prop.thumbUrl || '/img/create_exlisting.png';
    },
    computed_placeholder:function(){
      return this._('Input Address/Listing ID')
    },
    computedCnt:function () {
      // if (this.cnt > 9) {
      //   return 9+'+';
      // }
      return this.cnt;
    },
    shareData: function () {
      var prop = this.prop, lang = this.dispVar.lang;
      var data_text = `id=${prop._id || prop.sid}&tp=listing&lang=${lang}`
      if (this.dispVar.isLoggedIn && this.dispVar.allowedShareSignProp) {
        if (this.wDl) {
          data_text += '&wDl=1';
        }
        if (this.wSign) {
          data_text += '&aid=' + this.dispVar.shareUID;
        }
      } else {
        data_text += "&wDl=1";
        if (this.user) {
          data_text += "&uid=" + this.dispVar.shareUID;
        }
      }
      return data_text;
    }
  },
  data () {
    return {
      propItems:[],
      feedurl: '/1.5/form/forminput',
      pgNum:0,
      searchAfter:'',
      hasMore:true,
      offSet:0,
      cnt:0,
      barTitle:'',
      prop:{},
      jumping:false,
      fullUrl: document.URL,
      message:'',
      err:'',
      pSize: 20,
      focused:false,
      wId: (vars.wId?true:false),
      wDl: (vars.wDl?true:false),
      wSign: true,
      qrcd:false,
      oldVerBrowser: window.oldVerBrowser || false,
      agentCard: false,
      fromShare: false,
      addrSearchResults: [],
      loading: true,
      signupTitle:this._('Book a Tour'),
      user:{fnm:'',eml:'',mbl:''},
      userForm:{
        ueml:'<EMAIL>',
        sid:'',
        nm:'',
        eml:'',
        mbl:'',
        projShareUID:'',
        formid:'system',
        id:''
      },
      dispVar: {
        defaultEmail:'',
        isApp:true,
        isCip:false,
        isLoggedIn:true,
        lang:'en',
        ownerData:{vip:1},
        allowedShareSignProp: false,
        allowedPromoteProp: false,
        hasFollowedRealtor: false,
        shareUID:'',
        allowedEditGrpName:false,
        shareLinks:{l:[],v:[]},
        sessionUser: {},
        projShareUID:'',
        languageAbbrObj:[],
        isRealtor:false
      },
      searchStr:'',
      datas:[
        'defaultEmail',
        'isPaytop',
        'isApp',
        'isCip',
        'isVipUser',
        'isVipRealtor',
        'isRealtor',
        'isLoggedIn',
        'lang',
        'allowedShareSignProp',
        'allowedPromoteProp',
        'hasFollowedRealtor',
        'shareUID',
        // 'gmapGeocode',
        'allowedEditGrpName',
        // 'jsGmapUrl',
        'shareLinks',
        'sessionUser',
        'projShareUID',
        // 'autocomplete',
        // 'placeApi',
        'coreVer',
        'languageAbbrObj',
      ],
      datasObj:{page:'listSearch',src:'list'},
      // hist: [],//indexHistCtrl?indexHistCtrl.getHist()
      fromList:1,
      locale:'en'
    };
  },
  components: {
    PageSpinner,
    ListingShareDesc,
    PropDetail,
    PropDetailMapMenu,
    SchoolDetail,
    PropNeedLogin,
    // Prop3pPromoteModal,
    // PropCreateWepageModal,
    PropList,
    // Disclaimer,
    BrkgPhoneList,
    RmBrkgPhoneList,
    ShareDialog,
    // WechatQrModal,
    // GetAppBar,
    // UserBriefInfo,
    FlashMessage,
    PropFavActions,
    SignUpForm,
    // AutocompleteListWrapper
  },
  beforeMount () {
    if (!window.bus) {
      console.error('global bus is required!');
      return;
    }
    if (!window.gMapsCallback) {
      window.gMapsCallback = this.initGmap;
    }
    if (vars.aid || vars.uid) {
      this.agentCard = true;
      this.datas.push('ownerData');
      this.wId = true;
      this.datasObj.ownerId = vars.aid || vars.uid;
    }
    if (vars.id) {
      this.searchStr = decodeURIComponent(vars.id);
    }
    var self = this, bus = window.bus;
    //after getPageData
    bus.$on('pagedata-retrieved', function (d) {
      self.dispVar = Object.assign(self.dispVar, d);
      if (d.sessionUser) {
        for (let i of ['nm','eml','mbl']) {
          self.userForm[i] = d.sessionUser[i];
        }
      }
      if(d.ownerData){
        // self.user = Object.assign(self.user, d.ownerData);
        self.user = d.ownerData
      }
      if (d.projShareUID) {
        self.userForm.projShareUID = d.projShareUID;
      }
      self.initData();
    });
    bus.$on('prop-retrived', function(prop){
      self.userForm.sid = prop._id;
      self.userForm.id = prop._id;
      if (prop.e) {
        bus.$emit('set-loading', false);
        if (prop.ec == 1) {
          bus.$emit('prop-need-login', prop.e);
        }
      } else {
        if (!prop.thumbUrl) {
          prop.thumbUrl = self.picUrl(prop);
        }
        self.prop = prop;
        toggleModal('propDetailModal');
        setTimeout(function () {
          bus.$emit('set-loading', false);
        }, 0);
      }
    });
    bus.$on('set-loading', function (loading) {
      self.loading = loading;
    });
    bus.$on('school-retrieved', function (sch) {
      if (sch.e) {
        return window.bus.$emit('prop-need-login', sch.e);
      }
      toggleModal('schoolDetailModal','open');
    });
    bus.$on('toggle-drop', function (cmd) {
      self.toggleDrop(cmd);
    })
    bus.$on('school-prop', function (d) {
      var url = '/1.5/mapSearch', self = this;
      toggleModal('schoolDetailModal','close');
      toggleModal('propDetailModal','close');
      if (d.sch && d.sch.loc) {
        url += '?loc=' + d.sch.loc[0] + ',' + d.sch.loc[1];
        url += '&zoom=15';
        url += '&saletp='+d.type;
      }
      self.loading = true;
      return window.location = url;
    });
    this.getPageData(this.datas, this.datasObj);
    if (this.dispVar.defaultEmail){
      this.userForm.ueml = this.dispVar.defaultEmail;
    }
  },
  mounted () {
    setTimeout(() => {
      this.showMore();
      // this.getAutoCompletes();  
    }, 500);
    this.locale = vars.lang
    this.createLangTemplate = createLangTemplate;
    window.setLang = this.setLang;
  },
  events: {},
  methods: {
    setLang(lang){
      var url = window.location.href;
      var url = url.replace(/=(zh-cn|en|zh|kr)/,'='+lang);
      window.location = url;
    },
    isActive (lang) {
      return this.locale === lang ? 'active' : '';
    },
    noop(e){
      if (e) {
        e.preventDefault();
        e.stopPropagation();
        return 0;
      }
    },
    findSchools(r){
      var self = this;
      return self.openInMapSearch(r.loc[1], r.loc[0], 'ss')
      //maybe
      var city = null;
      for (let a of r.address_components) {
        if (a.types.indexOf('locality')>-1) {
          city = a.long_name;
          break;
        }
      }
      if (city) {
        var url = '/1.5/school/schoolList?city='+city;
        window.location = url;
      } else {
        self.openInMapSearch(r.geometry.location.lat, r.geometry.location.lng, 'ss')
      }
    },
    initGmap () {
      console.log('initmap');
      // var map = new google.maps.Map(document.getElementById('detail-map-holder'), {
      //     center: {lat: -34.397, lng: 150.644},
      //     zoom: 8
      // });
    },
    goBackIndex(){
      var url = '/1.5/index';
      // this.clearCache();
      // if (vars.d) {
      //   url = vars.d;
      //   return window.location = url;
      // }
      // use back may cause crash on iphone6
      RMSrv.closeAndRedirectRoot(url);
      // window.location = url;
      // window.history.back();
    },
    initData(){
      // #agentCard if shared by realtor, show realtor name, else show 'shared by RM'
      var self = this;
      if (!self.dispVar.isLoggedIn && !self.dispVar.isApp) {
        self.fromShare = true;
      }
      if (self.dispVar.isRealtor) {
        self.wSign = true;
      } else {
        self.wSign = false;
      }
      // #TODO: if has owner change shareUID
    },
    initPropListImg (data={}) {
      // var prop;
      // for (var i = 0; i < this.propItems.length; i++) {
      //   prop = this.propItems[i];
      //   if (!prop.img) {
      //     console.log(prop.sid);
      //     prop.img = this.picUrl(prop);
      //   }
      // }
      var recursive = this.propItems;
      if (data.target) {
        recursive = this[data.target];
      }
      for (var prop of recursive) {
        if (!prop.thumbUrl) {
          prop.thumbUrl = this.picUrl(prop);
        }
      }
    },
    isRMProp (prop) {
      return /^RM/.test(prop.id);
      // if (!id) {
      //   return false;
      // }
      // return id.substr(0, 3) === 'RM1';
    },
    viewListing (prop) {
      window.bus.$emit('prop-changed',prop);
      // var url = '/html/propDetailPage.html?inframe=1&id='+prop._id;
      // url = this.appendDomain(url);
      // this.openTBrowser(url);
    },
    toggleModal (a,b,c) {
      toggleModal(a, b);
      if (c) {
        toggleDrop()
      }
    },
    picUrl (r) {
      var ret = this.setupThisPicUrls(r);
      return ret[0] || (window.location.origin + "/img/noPic.png");
      // if (self.isRMProp(r)) {
      //   r.pic.ml_num = r.sid || r.ml_num;
      //   ret = self.$parent.convert_rm_imgs(self, r.pic, 'reset');
      //   return ret[0] || (window.location.origin + "/img/noPic.png");
      // } else {
      //   return listingPicUrls(r, self.dispVar.isCip)[0] || '/img/noPic.png';
      // }
    },
    openTBrowser (url, cfg) {
      if (this.dispVar.isApp) {
        this.tbrowser(url, cfg);
        // RMSrv.closeAndRedirect(url)
      } else {
        window.location = url;
      }
    },
    lostFocus () {
      // if (!this.jumping) {
      //   this.focus = false;
      // }
    },
    openInMapSearch (lat, lng, opt='') {
      var addr = '/1.5/mapSearch?cMarker=1&loc=' + lat + ',' + lng;
      if (opt=='ss') {
        addr += "&zoom=15";
      }
      addr += '&d='+encodeURIComponent(window.location.pathname+window.location.search);
      window.location = addr;
    },
    toggleDrop (cmd) {
      var style = document.getElementById('backdrop').style.display;
      if (cmd == 'hide') {
        style = 'block';
      } else if (cmd == 'show') {
        style = 'none';
      }
      return document.getElementById('backdrop').style.display = style === 'block' ? 'none' : 'block';
    },
    searchAddrG (e) {
      return;
      var self = this;
      if (!self.searchStr || /^@/.test(self.searchStr)) {
        return;
      }
      var address = self.searchStr || 'Toronto, ON, Canada';
      // if (vars.recoAddr && vars.recoAddr !== '') {
      //   address = vars.recoAddr;
      // }
      if ((typeof address === 'string') && (address.toLowerCase().indexOf('Canada') < 0)) {
        address += '&components=country:ca&language=en';
      }
      var firstChar = self.dispVar.gmapGeocode.indexOf('?')>0?'&':'?';
      self.$http.get(self.dispVar.gmapGeocode + firstChar+'address=' + address).then(
        function (ret) {
          ret = ret.data;
          if (ret.status == 'OK') {
            if (ret.results.length > 3) {
              ret.results = ret.results.slice(0,3);
            }
            self.addrSearchResults = ret.results;
          } else {
            self.addrSearchResults = [];
          }
        },
        function (ret) {
          console.error( "server-error" );
        }
      );
    },
    // removeHist (key) {
    //   var self = this;
    //   indexHistCtrl.removeHist(key);
    //   indexHistCtrl.setHist();
    //   self.hist = indexHistCtrl.getHist();
    //   self.jumping = true;
    //   document.querySelector('#searchInput').focus();
    //   setTimeout(function () {
    //     self.jumping = false;
    //   }, 200);
    // },
    search (q) {
      return;
      if (q) {
        this.searchStr = q;
      }
      if (this.searchStr) {
        //this.searchStr.split(/\s+/);
        this.suggestions = null;
        this.pgNum = 0;
        this.propItems = [];
        indexHistCtrl.pushHist(this.searchStr);
        indexHistCtrl.setHist();
        this.hist = indexHistCtrl.getHist();
        this.showMore();
      }
    },
    // request stdFunction:addrSuggest to get geo rets
    // TODO: dependency of [standardFunction]
    getGeoAddrResults(){
      if(!this.isAddressInput(this.searchStr)){
        return;
      }
      if(!this.dispVar.isApp){
        return
      }
      if(this.propItems.length){
        return;
      }
      // call std fns
      getStandardFunctionCaller()('addrSuggest',{s:this.searchStr},(err,ret)=>{
        if (err) {
          console.error(err)
          return;
        }
        if(ret && ret.suggest){
          this.addrSearchResults = ret.suggest;
        }
      })
    },
    // NOTE: 后端会根据用户role filter merged 房源，后端会查找merged房源，see： https://bitbucket.org/fredxiang/realmaster-appweb/commits/668cb5d58289dc072dc2de52b1c457232d663180
    // 导致pSize 无法使用
    calcHasMoreProps(ret){
      if(ret.resultList.length <= 0){
        return false
      }
      if(ret.cnt <= 0){
        return false
      }
      // if(ret.cntRemain<=0){
      //   return false
      // }
      // if(this.propItems.length >= ret.cnt){
      //   return false
      // }
      return true;
    },
    showMore () {
      var self = this,
      data = {cache:true,
        id:this.searchStr, 
        p:this.pgNum,
        share:(vars.share || vars.ec||!this.dispVar.isApp)?true:false,
        lang:vars.lang
      };
      if(data.p > 0){
        data.searchAfter = this.searchAfter
      }
      self.loading = true;
      self.$http.post('/1.5/search/prop/list', data).then(
        function(ret) {
          ret = ret.data;
          self.loading = false;
          if (ret.e) {
            console.error(ret.e);
            self.err = ret.e;
            return;
          }
          self.err = '';
          // self.searchAddrG();
          if (ret.resultList) {
            self.propItems = self.propItems.concat(ret.resultList);
            self.hasMore = self.calcHasMoreProps(ret)
          } else {
            self.hasMore = false;
          }
          self.initPropListImg();
          self.offSet = ret.offSet;
          self.cnt = ret.cnt || self.propItems.length;
          self.pSize = ret.pSize || 20;
          self.searchAfter = ret.searchAfter || '';
          if (self.propItems.length == 1) {
            self.viewListing(self.propItems[0]);
          }
          if (ret.barTitle) {
            self.barTitle = ret.barTitle;
            document.querySelector("#wx-title").innerHTML = ret.barTitle;
          }
          if (ret.desc) {
            document.querySelector("#wx-desc").innerHTML = ret.desc;
          }
          setTimeout(function () {
            if (window.RMSrv && RMSrv.wxOnShare) {
              RMSrv.wxOnShare();
            }
            self.getGeoAddrResults()
          }, 0);
        }, function(ret) {
          ajaxError(ret);
        });
      self.pgNum += 1;
    }
  }
}

</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped>
#SignupModal .close{
    width: 100%;
    text-align: center;
    padding-top: 30px;
}
#SignupModal .close img {
    width: 40px;
    height: 40px;
}
#autocompleteWrapper{
  padding-top: 80px;
}
.content{
  background: #eee;
}
#searchWrapper .btn{
  outline: 0;
  height: 35px;
  width: 44px;
  margin-top: -43px;
  padding: 5px 5px 5px 5px;
  background-color: transparent;
  border: 1px none;
  color: #afafaf;
  font-size: 18px;
}
#searchWrapper{
  padding: 0;
  background-color: #eee;
  height: 35px;
  /*margin-bottom: 10px;*/
}
/* #searchWrapper #id{
  margin-bottom: 0;
} */
#errMsg #notFoundTip{
  padding-top: 0px;
  font-size: 13px;
  padding-left: 10px;
}
#errMsg .err-wrapper{
  padding-left: 10px;
  color: #B95050;
  padding-top: 0px;
  margin-top: -10px;
}
#foundAddrWrapper{
  margin-bottom: 10px;
}
#foundAddrTl{
  /*float: left;*/
  /*position: absolute;*/
  /*background-color: #5cb85c;*/
  /*width: auto;*/
  padding-left: 10px;
  padding-top: 10px;
  padding-bottom: 5px;
  font-weight: bold;
  font-size: 16px;
  /*display: inline-block;*/
  /*padding: 2px 5px 2px 5px;*/
  /*color: white;  */
}

#foundAddrWrapper > .matched-address-list{
  height: auto;
  padding: 10px ;
  border-bottom: 1px solid #eee;
  background-color: white;
}

#foundAddrWrapper .matched-address-list .matched-address-addr{
  font-size: 16px;
  /*display: inline-block;*/
  /*width: 75%;*/
  /*padding: 5px 15px 5px 15px;*/
}
 #foundAddrWrapper .matched-address-list .matched-address-action{
  /*display: inline-block;*/
  /*width: 25%;*/
  padding-top:10px;
  vertical-align: top;
  text-align: left;
  font-size: 20px;
}
#foundAddrWrapper .matched-address-list .matched-address-action .href-wrapper{
  display: inline-block;
  width: auto;
  font-size: 14px;
  color: #007aff;
}
#foundAddrWrapper .matched-address-list .matched-address-action .href-wrapper:not(:first-child){
  margin-left: 13px;
}
#foundAddrWrapper .matched-address-list .matched-address-action .href-wrapper a{
  font-size: 15px;
  color:#E03131;
}
#foundAddrWrapper .matched-address-list .matched-address-action a:nth-of-type(1){
  margin-right: 20px;
}
#ulWrapper .ul-title{
  font-size: 16px;
  font-weight: bold;
  background-color: #eee;
  padding: 10px;
}
#searchWrapper input {
  margin-bottom: 0;
  border: none;
}
#propDetailModal.active, #brkgPhoneList.active{
  -webkit-transform: translate3d(0, 0, 0px);
}
#SignupModal{
  background: rgba(0,0,0,.88);
  z-index: 22;
}
#SignupModal .content{
  background-color: transparent;
}
#SignupModal .bar.bar-nav{
  background-color: transparent;
  border-bottom: none;
}
.changeLang a{
  border-radius: 4px;
  height: auto;
  padding: 3px !important;
  border: 1px solid white;
  font-size: 15px;
  margin-top: 11px;
  margin-right: 10px;
}
</style>
