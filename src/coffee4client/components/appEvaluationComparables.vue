<template lang="pug">
div(style="background:#f1f1f1; height: 100%;")
  div.overlay.loader-wrapper#busy-icon(v-show="loading")
    div.loader
  div.mask(v-if="loading || showmask",@click="showmask=false")
  div.calc(v-show="loading")
    span(v-show="step=='searching'") {{_('Searching', 'evaluation')}}
  header#header-bar.bar.bar-nav(v-if="!inframe")
    a.icon.fa.fa-back.pull-left(@click="goback()")
    h1.title.ng-cloak!= "{{_('Evaluation Conditions','evaluation')}}"

  div.content
    div#evaluation.full-height.content-list
      //- div#owner-of-property(v-show="estimated_price && showOwner && !share")
      //-   div
      //-     span.icon.icon-close(@click="showOwner = false")
      //-     span {{_('Owner of this property?','evaluation')}}
      //-     span.btn(@click="addOwner()") {{_('Add to my property', 'evaluation')}}
      div#prop.block
        div.header
          span {{_('Step 2','evaluation')}}
          span.pull-right(@click="goback()")
            span {{_('Previous','evaluation')}}
            span.icon.icon-right-nav
        div.prop
          div.detail(:style="{width: addrWidth}")
            div.address.trim {{address}}
              span.city , {{_(prop.city)}}
            div.bdrms
              span(v-show="prop.bdrms")
                span.fa.fa-rmbed
                span  {{prop.bdrms}}
              span(v-show="prop.bthrms")
                span.fa.fa-rmbath
                span  {{prop.bthrms}}
              span(v-show="prop.gr")
                span.fa.fa-rmcar
                span  {{prop.gr}}
              span(v-show="prop.br_plus")
                span  {{prop.br_plus}}
                span {{_('den')}}
              span(v-show="prop.tp")
                span  {{_(prop.tp)}}
            div.bdrms
              span.text(v-show="prop.tax")
                | ${{prop.tax}} {{_('Tax')}}
              span.text(v-show="prop.sqft")
                | {{prop.sqft}} {{_('sqft','prop')}}
            div
              span.btn-light-green
                span.text {{_('Condition','evaluation')}} {{getReno(prop.reno||3)}}
              span.btn-light-green(v-if="prop.front_ft")
                span {{prop.front_ft}} * {{prop.depth}} {{_(prop.lotsz_code)}}
                //- span.adjust(v-if="!share && !fromHist",@click="adjust()") {{_('Adjust','evaluation')}}
              //- div(v-if="prop.mlsid && !fromMls")
          div.img-wrapper(v-if="hasImage")
            div.img
              img(:src="computedImg" referrerpolicy="same-origin")
      div.block.refer
        div.header
          | {{_('Select Comparables','evaluation')}}
        //-   span.pull-right(style="padding-left:10px;",v-show="showRef('rental')", @click="selectSaleOrRent('rent')", :class="{active: saleOrRent=='rent'}") {{_('For Rent')}}
        //-   span.pull-right(v-show="showRef('sale')", @click="selectSaleOrRent('sale')", :class="{active: saleOrRent=='sale'}") {{_('For Sale ')}}
        div(style="background:white;")
          div.report-wrapper
            div
              span.fa.fa-exclamation-circle
            div.txt
              | {{_('The evaluation result is based on comparable listings selected. Please adjust distance and time frame range values then select  suitable listings to make the result more accurate.')}}
          div.wrapper
            div.label
              div.val {{dist_range}}
                span(style="font-size:10px") m
              div {{_('Distance','evaluation')}}
            div.range_wrapper
              range.range(@on-change="onChangeVal('dist_range',$event)", :step="50", :value.sync="dist_range", :min="50", :disabled="disabled", :max.sync="max_dist_range",label-down=true)
          div.wrapper
            div.label
              div.val {{last}}
                span(style="font-size:10px;padding-left:5px;") {{_('months')}}
              div {{_('Past','evaluation')}}
            div.range_wrapper
              range.range(@on-change="onChangeVal('last',$event)", :value.sync="last",:step="1", :min="3", :disabled="disabled", :max="12", label-down=true)

          div(style="padding:10px;", v-show="step=='changeRange' && !share")
            a.download-button( @click="searching()")
              |{{_('Search Comparable Listings','evaluation')}}
          div.no-comparable(v-show="(step=='searched' || step=='re-searched') && !rentSearchResults.length && !saleSearchResults.length")
             | {{_('No Comparable listing','evaluation')}}

          div(v-show="!loading && step!='changeRange' && (rentSearchResults.length || saleSearchResults.length)")
            div.tab(v-if="rentSearchResults.length && saleSearchResults.length")
              span.btn(@click="selectSaleOrRent('sale')", :class="{active: saleOrRent=='sale'}") {{_('For Sale','evaluation')}}
                span.num {{incl.length}}
              span.btn(@click="selectSaleOrRent('rent')", :class="{active: saleOrRent=='rent'}") {{_('For Rent','evaluation')}}
                span.num {{rentalIncl.length}}
            div.tab(v-else)
              div.btn-long
                span(v-show="saleSearchResults.length") {{_('For Sale','evaluation')}}
                  span.num {{incl.length}}
                span(v-show="rentSearchResults.length") {{_('For Rent','evaluation')}}
                  span.num {{rentalIncl.length}}
            div(v-show="saleOrRent=='sale'")
              prop-list-element-eval(v-for="prop in saleSearchResults", :checked-props="incl", :sale-or-rent="saleOrRent", :prop="prop", :disp-var="dispVar", :show-icon="iconType", show-checkbox=true, :key="prop._id")
            div(v-show="saleOrRent=='rent'")
              prop-list-element-eval(v-for="prop in rentSearchResults", :checked-props="rentalIncl", :sale-or-rent="saleOrRent", :prop="prop", :disp-var="dispVar", :show-icon="iconType", show-checkbox=true, :key="prop._id")

  div.bar.bar-standard.bar-footer.row(v-show="step!='changeRange'")
    span.pull-left.update(@click="goback()", style="border-top: 1px solid #ddd;")
      span {{_('Previous', 'evaluation')}}
    span.pull-right.update.active(@click="evaluate()", :class="{disabled: !rentSearchResults.length && !saleSearchResults.length}")
      span {{_('Evaluate', 'evaluation')}}

  div(style="display:none")
    span(v-for="(v,k) of strings") {{_(v.key, v.ctx)}}
</template>

<script>
import Range from './frac/Range'
// import filters from './filters'
import PropListElementEval from './frac/PropListElementEval.vue';
import file_mixins from './file_mixins'
import evaluate_mixins from './evaluate_mixins'
import rmsrv_mixins from './rmsrv_mixins'
import pagedata_mixins from './pagedata_mixins'

export default {
  mixins: [file_mixins,evaluate_mixins,rmsrv_mixins,pagedata_mixins],
  // filters:{
  //   propPrice:filters.propPrice,
  // },
  props:{
    fromMls: {
      type:Boolean,
      default:function() {
        return false;
      }
    },
  },
  components:{
    Range,
    PropListElementEval,
  },
  data () {
    return {
      dispVar:{
        type:Object,
        default:function () {
          return {fav:false};
        }
      },
      strings:{
        notEnoughSaleConfirm:{key:"To evaluate rental price only, please click confirm. To evaluate sale price also, please click cancel and select at least 3 sale comparable listings. ", ctx:'evaluation'},
        notEnoughRentConfirm:{key:"To evaluate sales price only, please click confirm. To evaluate rental price also, please click cancel and select at least 3 rental comparable listings. ", ctx:'evaluation'},
        notEnoughConfirm:{key:"Please select at least 3 comparable lists to perform the evaluation. Sales and leasing are carried out separately. Please select comparable listings in each category to complete the evaluation.", ctx:'evaluation'},
        notEnoughSaleAlert:{key:"You need to select at least 3 comparable sale listings to get evaluation result.", ctx:'evaluation'},
        notEnoughRentAlert:{key:"You need to select at least 3 comparable rental listings to get evaluation result.", ctx:'evaluation'},
        message:{key:"Message", ctx:'evaluation'},
        cancel:{key:"Cancel", ctx:''},
        confirm:{key:"Confirm", ctx:''}
      },
      datas: [
        'isApp',
        'isCip',
        'isLoggedIn',
        'lang',
        'sessionUser',
        'reqHost',
        'isVipUser',
        'streetView',
        'streetViewMeta',
        'isAdmin',
        'isDevGroup'
      ],
      tax:null,
      sqft:null,
      reno:3,
      showChange:false,
      prop:{
        bdrms:1,
        reno:3,
        sqft:0,
        tax:0,
      },
      step:'searching', // searching, searched, calculating, calculated, re-searched, changeRange,tryagain
      noBack: false,
      last:0,
      range_from: 0,
      range_to: 0,
      // estimated_price: 0,
      // estimated_price1: 0,
      // estimated_rent_price:0,
      // excl:[],
      incl:[],
      // inclIds:[],
      // exclIds:[],
      dists:[],
      // rentalExcl:[],
      rentalIncl:[],
      // rentalInclIds:[],
      // rentalExclIds:[],
      rentSearchResults:[],
      saleSearchResults:[],
      dist_range: 50,
      max_dist_range: 3000,
      accuracy: 0,
      // showRentalRef: false,
      rental_dist_range:0,
      rental_last:0,
      histId:'',
      uaddr:'',
      showHelp: false,
      address:'',
      // shareAddress:'',
      currentList:[],
      // currentExcl:[],
      currentTab:null,
      showOwner: true,
      saleOrRent: '',
      evaluationPaddingTop: 44,
      wDl: false,
      wSign: true,
      wComment: true,
      wId: false,
      prop:{},
      disabled: false,
      currentExclCnt:0,
      currentUsedCnt:0,
      share:false,
      fromHist:false,
      noHist:false,
      histcnt:0,
      iconType:'',
      originalLast:0,
      originalRange:0,
      showmask:false,
      inframe:vars.inframe,
      computedImg:'',
      hasImage:false
    };
  },
  computed:{
    showFooter:function() {
      if (this.share)
        return false;
      if(!this.incl.length && !this.rentalIncl.length)
        return false
      return ['searched','calculated','re-searched'].indexOf(this.step)>=0
    },
    addrWidth:function() {
      if(this.hasImage) {
        return 'calc(100% - 130px)';
      } else {
        return '100%';
      }
    },
    loading:function () {
      return ['searching','calcuating'].indexOf(this.step)>=0
    },
    accuracyLeftWidth: function() {
      if (this.accuracy=='low') {
        return 30;
      } else if (this.accuracy=='high') {
        return 80;
      } else {
        return 50;
      }
    },
  },
  watch:{
    last (val, oldVal) {
      if(val!=this.originalLast && oldVal!=this.originalLast) {
        this.changeRange();
      }
    },
    dist_range (val, oldVal) {
      if(oldVal!=this.originalRange && val!=this.originalRange) {
        this.changeRange();
      }
    },
  },
  mounted () {
    if (!window.bus) {
      console.error('global bus is required!');
      return;
    }
    var bus = window.bus, self = this;
    if (this.inframe) {
      document.getElementById('evaluation').style.paddingTop = '0px';
    }
    this.getPageData(this.datas,{},true);
    bus.$on('pagedata-retrieved', function (d) {
      self.dispVar = Object.assign(self.dispVar, d);
      self.getImg();
    });
    this.prop.lat = vars.lat;
    this.prop.lng = vars.lng;
    var self = this;
    for (let key of this.fields) {
      if (vars[key]){
        if (key == 'img') {
          this.prop[key] = decodeURIComponent(vars[key])
        } else if(this.numFields.indexOf(key) >=0 ) {
          this.prop[key] = Number(vars[key]);
        } else {
          this.prop[key] = vars[key];
        }
      }
    }
    self.step = 'searching';
    var addr = self.prop.addr ? self.prop.addr : self.prop.st_num? self.prop.st_num + ' ' + self.prop.st : self.prop.st
    self.address = this.getFormatAddr(addr, self.prop);
    self.last = 3;
    self.getMaxDist();
    self.searching(true);
    if(vars.ids)
      self.incl = vars.ids.split(',');
    if (vars.rentalids)
      self.rentalIncl = vars.rentalids.split(',');
    window.bus.$on('select-prop', function(data) {
      if (data.rental) {
        self.rentalIncl = data.checkedProps;
      } else {
        self.incl = data.checkedProps;
      }
    });
  },
  methods: {
    onChangeVal(fld,val){
      if (val == null) {
        return;
      }
      this[fld] = val;
    },
    selectSaleOrRent(type) {
      var self = this;
      this.saleOrRent = type;
      this.currentList = [];
      var ids = [];
      var loaded = false;
      if (type =='sale') {
        this.currentList = this.saleSearchResults;
        this.selectList = this.incl;
      } else if (type =='rent') {
        this.currentList = this.rentSearchResults;
        this.selectList = this.rentalIncl;
      }
    },
    getImg:function() {
      var self = this;
      if (this.prop.thumbUrl) {
        self.hasImage = true;
        this.computedImg = this.prop.thumbUrl;
      } else if (!this.isCip && this.prop.lat && this.prop.lng) {
        this.getGoolgeStreeViewImg(this.dispVar,this.prop, function(url){
          self.computedImg = url;
          if (url) {
            self.hasImage = true;
          }
        });
      } else {
        return null;
        self.hasImage = false;
      }
    },
    goback:function() {
      var url = '/1.5/evaluation/evaluatePage.html?nobar=1&inframe=1&'+ this.buildUrlFromProp();
      if (vars.fromMls) {
        url +='&fromMls=1';
      }
      if (vars.reEval) {
        url +="&reEval=1";
        // url = RMSrv.appendDomain(url);
        // return document.location.href= url;
      }
      url = RMSrv.appendDomain(url);
      document.location.href = url;
      // if (RMSrv.closeAndRedirect)
      //   RMSrv.closeAndRedirect(url);
    },
    reEvaluate: function() {
      this.prop = Object.assign({}, this.prop, { sqft: parseInt(this.sqft), reno: parseInt(this.reno),tax: parseInt(this.tax)});
      // this.evaluate();
      this.showmask =false;
      this.showChange = false;
    },
    cancelChange: function() {
      this.showmask =false;
      this.showChange= false;
    },
    numberFormat: function(event) {
      var input = event.target.id;
      if(event.target.value){
        if(input == 'tax')
          this.tax = parseInt(this.tax);
        if(input == 'sqft')
          this.sqft = parseInt(this.sqft);
      }
    },
    adjust:function() {
      this.tax = this.prop.tax;
      this.reno = Number(this.prop.reno);
      this.sqft = this.prop.sqft;
      this.showmask = true;
      this.showChange= true;
    },
    // showRef:function(type) {
    //   if (type=='sale') {
    //     return this.incl.length || this.inclIds.length || this.excl.length || this.exclIds.length;
    //   } else if (type=='rental') {
    //     return  this.rentalIncl.length||this.rentalExcl.length || this.rentalInclIds.length ||this.rentalExclIds.length;
    //   } else {
    //     return this.incl.length || this.rentalIncl.length|| this.excl.length || this.rentalExcl.length || this.inclIds.length || this.exclIds.length || this.rentalInclIds.length ||this.rentalExclIds.length;
    //   }
    // },
    changeRange() {
      this.incl = [];
      this.rentalIncl = [];
      this.excl = [];
      this.rentalExcl = [];
      this.step="changeRange";
      this.rentalIncl = [];
    },
    tryagain() {
      this.step='tryagain';
      this.fromHist = false;
      this.disabled =false;
      if (this.incl.length) {
        this.selectSaleOrRent('sale')
      } else if (this.rentalIncl.length) {
        this.selectSaleOrRent('rent');
      }
    },

    addOwner() {
      var self = this;
      self.$http.post('/1.5/evaluation/addOwner', {uaddr: self.uaddr, uid: self.dispVar.sessionUser._id}).then(function(ret){
        if(ret.data.ok) {
          self.showOwner = false;
        }
      },function(ret){
        ajaxError(ret);
      });
    },

    showSMB(){
      RMSrv.share('show');
    },
    rmShare(tp, doc){
      RMSrv.share(tp, doc);
    },
    getMaxDist() {
      var self = this;
      self.$http.get('/1.5/evaluation/maxDistRange?type='+self.prop.tp).then(function(ret){
        if(ret) {
          ret = ret.data;
          self.max_dist_range = ret.max_dist_range;
        }
      },function(ret){
        ajaxError(ret);
      });
    },
    clearResults(){
      var self = this;
      self.last = null;
      self.dist_range = null;
      self.range_from = null;
      self.range_to = null;
      self.incl = [];
      self.inclIds = [];
      self.rentalIncl = [];
    },
    searching(first){
      var self = this;
      self.step = 'searching';
      if (!(self.prop.lat && self.prop.lng)) {
        return RMSrv.dialogAlert("error")
      }
      var success = (ret)=>{
        ret = ret.data;
        if (!ret.ok) {
          console.log(ret.e);
          if (ret.e=='Access Denied') {
            if(RMSrv.closeAndRedirectRoot) {
              RMSrv.closeAndRedirectRoot('/1.5/user/login#index');
              return;
            } else {
              document.location.href='/1.5/user/login#index';
            }
          }
          self.step = first? 'searched' :'re-searched';
          return;
        }
        self.step = first? 'searched' :'re-searched';
        self.uaddr = ret.uaddr;
        self.dist_range = self.originalRange = ret.range;
        self.last = self.originalLast = ret.last;
        if(ret.rent) {
          self.rental_dist_range = ret.rent.range;
          self.rental_last = ret.rent.last;
        }
        self.max_dist_range = ret.max_dist_range
        self.changeSelectProp = false;
        self.saleSearchResults = ret.incl||[];
        self.initPropListImg(self.saleSearchResults);
        self.rentSearchResults = ret.rentalIncl||[]
        self.initPropListImg(self.rentSearchResults);
        for (let prop of self.saleSearchResults) {
          prop = this.compareDifference(prop);
        }
        for (let prop of self.rentSearchResults) {
          prop = this.compareDifference(prop);
        }
        if (self.saleSearchResults.length) {
          self.selectSaleOrRent('sale')
        } else if (self.rentSearchResults.length) {
          self.selectSaleOrRent('rent');
        }
      }
      var failed = (ret)=>{
        ajaxError(ret);
        //show error to user will not help. just log. Next try may work.
      }
      var params = Object.assign({nocalc: true},self.prop);
      params.excl = [];
      params.rentalExcl=[];
      if(!first) {
        params.range = self.dist_range;
        params.last = self.last;
        params.range_r = self.rental_dist_range || self.max_dist_range;
        params.last_r = self.rental_last || 12;
      }
      self.$http.post('/1.5/evaluation', params).then(function(ret){
        setTimeout(success(ret),5000);
      },failed);
    },
    evaluate (){
      if (!this.rentSearchResults.length && !this.saleSearchResults.length) {
        return;
      }
      let self = this;
      let _do = function(idx) {
        if (idx+'' != '2') {
          return;
        }
        let ids = self.incl.join(",")||'null';
        let rentalids = self.rentalIncl.join(",")||null
        let url = "/1.5/evaluation/result.html?ids="+ids+"&last="+self.last+"&range="+self.dist_range+"&last_r="+self.rental_last+"&range_r="+self.rental_dist_range+"&rentalids="+rentalids + '&'+self.buildUrlFromProp();
        if (vars.fromMls) {
          url = url + '&fromMls=1&inframe=1';
          url = RMSrv.appendDomain(url);
          var cfg = {hide:false, title: self._('RealMaster') +' ' +  self._('Estimate Report','evaluation')}
          RMSrv.getPageContent(url, '#callBackString', cfg, function(val) {
            // window.rmCall(':ctx:close');
          });
        } else if (RMSrv.closeAndRedirectRoot) {
          RMSrv.closeAndRedirectRoot(RMSrv.appendDomain(url));
          return;
        } else {
          document.location.href = url;
        }
      };
      if (self.saleSearchResults.length>0 && self.rentSearchResults.length>0) {
        let msg = ''
        if (self.incl.length>=3 && self.rentalIncl.length>=3) {
          return _do(2);
        } else {
          if ( self.incl.length<3 && self.rentalIncl.length <3) {
            return RMSrv.dialogAlert(self._(self.strings['notEnoughConfirm'].key,'evaluation'))
          } else if (self.incl.length<3) {
            msg = self._(self.strings['notEnoughSaleConfirm'].key,'evaluation');
          } else if (self.rentalIncl.length <3) {
            msg = self._(self.strings['notEnoughRentConfirm'].key,'evaluation');
          }
          return RMSrv.dialogConfirm(msg,
            _do,
            self._(this.strings.message.key,self.strings.message.ctx),
            [self._(this.strings.cancel.key,self.strings.cancel.ctx), self._(self.strings.confirm.key,self.strings.confirm.ctx)]
          );
        }
      } else if (self.saleSearchResults.length>0 && self.incl.length<3) {
        return RMSrv.dialogAlert(self._(self.strings['notEnoughSaleAlert'].key,'evaluation'));
      } else if (self.rentSearchResults.length>0 && self.rentalIncl.length<3) {
        return RMSrv.dialogAlert(self._(self.strings['notEnoughRentAlert'].key,'evaluation'));
      } else  {
        _do(2);
      }
    }
  }
}

</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped>
.no-comparable {
  text-align: center;
  height: 200px;
  padding-top: 20px;
}
#prop .prop .detail {
  display: inline-block;
  float: left;
  font-size: 15px;
}
#prop .prop .detail div {
  padding: 1px 10px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
#prop .prop {
  background: white;
  width: 100%;
  position: relative;
  padding: 10px 10px 10px 0px;
  border-bottom: 1px solid #f1f1f1;
  display: flex;
  align-items: end;
}

.img-wrapper .img{
  width: 130px;
  /* display: table-cell; */
  vertical-align: top;
  padding-top: 0px;
  position: relative;
}
.img-wrapper .img img{
  display: inline-block;
  width: 100%;
  height: 83px;
}
.img-wrapper {
  height:100%;
  position:relative;
}
.bar-footer {
  border-top: none;
}
.content{
  padding-bottom: 44px;
}
.conditions .header {
  border-bottom: none!important;
}
.report-wrapper {
  display: flex;
  background: #f9f9f9;
  padding: 10px;
  font-size: 14px;
  margin:10px;
}
.report-wrapper>div:first-of-type {
    width: 30px;
    padding: 12px;
    color: #e03131;
    float: left;
    margin-top: -10px;
}
.report-wrapper .txt{
  color: #666;
  padding-left: 10px;
}
.btn-light-green {
  background: #f1f8ec;
  color: #5cb85c;
  font-size: 10px;
  margin-right: 5px;
  padding: 5px;
}
.wrapper{
  min-height: 74px;
  display: flex;
  align-items: center;
  padding:0px 15px;
  background-color: white;
}
.wrapper:not(:last-of-type) {
  border-bottom: 1px solid #f1f1f1;
}
.label {
  display: inline-block;
  width: 30%;
  font-weight: normal;
  font-size: 15px;
  weight:500;
  color:#999;
}
.label > div{
}
.val{
  color: black;
  font-size: 17px;
  padding-bottom: 5px;
}
.range_wrapper {
  width: 65%;
  display: inline-block;
  margin-top: -25px;
}
.valign {
  position: absolute;
  top: 50%;
  transform: translate(0, -50%);
}
.bdrms .text {
  padding-right: 5px;
}
.share-text{
  padding: 20px 10px;
}
.share-text .btn {
  border-radius: 10px;
  border: 3px solid #fff;
  box-shadow: 0px 2px 2px 2px #e5e5e5;
  height: 30px;
  padding: 6px 0px;
  color:#007aff;
}
.share-text .btn:nth-of-type(2) ::before {
  content: "";
  float: left;
  display: inline-block;
  height: 20px;
  border-left: 2px solid #cfcfcf;
  margin-left: -14%;
  margin-top: -4px;
}

.expand-transition {
  transition: all .3s ease;
  height: 320px;
  padding: 10px;
  background-color: #eee;
  overflow: hidden;
}
/* .expand-enter defines the starting state for entering */
/* .expand-leave defines the ending state for leaving */
.expand-enter, .expand-leave {
  height: 0;
  padding: 0 10px;
  opacity: 0;
}
.share-wrapper .share-text {
  height: 30px;
  width: 100%;
  display: flex;
}
/* .share-wrapper .share-text .try-again {
  float: right;
  font-size: 12px;
  margin-top: -5px;
} */
.mask {
  background: rgba(0,0,0,0.8);
  opacity: 0.3;
  z-index: 9;
}
.loader-wrapper{
  padding: 10px;
  background-color: rgba(0,0,0,.68);
  border-radius: 7px;
  height: 70px;
  width: 70px;
  z-index: 20;
  position: fixed;
  margin-left: -35px;
  margin-top: -35px;
  top: 50%;
  left: 50%;
  display: block;
  stroke: #fff;
  fill: #444;
}
.loader {
  /*margin: 60px auto;*/
  font-size: 10px;
  position: relative;
  text-indent: -9999em;
  border-top: 0.8em solid rgba(255, 255, 255, 0.2);
  border-right: 0.8em solid rgba(255, 255, 255, 0.2);
  border-bottom: 0.8em solid rgba(255, 255, 255, 0.2);
  border-left:0.81em solid #ffffff;
  -webkit-transform: translateZ(0);
  -ms-transform: translateZ(0);
  transform: translateZ(0);
  -webkit-animation: load8 1.1s infinite linear;
  animation: load8 1.1s infinite linear;
}
.loader,
.loader:after {
  border-radius: 50%;
  width: 50px;
  height: 50px;
}
@-webkit-keyframes load8 {
  0% {
    -webkit-transform: rotate(0deg);
    transform: rotate(0deg);
  }
  100% {
    -webkit-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}
@keyframes load8 {
  0% {
    -webkit-transform: rotate(0deg);
    transform: rotate(0deg);
  }
  100% {
    -webkit-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}
.calc {
  color: #fff;
  margin-top: -10px;
  text-align: center;
  margin-left: -30px;
  width: 60px;
  z-index: 30;
  position: fixed;
  left: 50%;
  top: 50%;
  font-size: 9px;
}
.notop {
  top:0px!important;
}
.fullLength {
  width:100%!important;
}
#accuracy .line {
  background-color: #d2d2d2;
  display: block;
  height: 1px;
  position: relative;
  width: 100%;
}
#accuracy .line img {
  position: absolute;
}
#hist{
  background: #e5e5e5 !important;
  height: 70px;
  padding-left: 0px!important;
  padding-right: 0px!important;
}
#hist div{
  min-height: 55px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  padding: 0 15px;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  background: white;
  justify-content: space-between;
  border-bottom: 1px solid #c5c5c5;
  border-top: 1px solid #c5c5c5;
}
.download-button {
  padding: 10px 0;
  text-align: center;
  font-size: 15px;
  color: #fff!important;
  text-decoration: none;
  background-color: #5cb85c;
  display: block;
  width: 100%;
  font-size: 15px;
  font-weight: 700;
  padding: 13px 8px 14px;
  line-height: 1;
}
.tab {
  padding: 15px 50px;
}
.tab .btn {
  width: 50%;
  padding: 10px;
  border: 1px solid #aaa;
  color: #666;
  font-size: 14px;
}
.tab .btn-long {
  width: 100%;
  text-align: center;
  padding: 10px;
  border: 1px solid #aaa;
  border-radius: 5px;
  color: #666;
  font-size: 14px;
  margin-left:0px;
  line-height: 14px;
}
.tab .btn:first-of-type {
  border-radius: 5px 0px 0px 5px;
  border-right: none;
}
.tab .btn:last-of-type {
  border-radius: 0px 5px 5px 0px;
  border-left: none;
}
.tab .btn.active {
  background-color: #aaa;
  color: white !important;
}
.tab .btn .num, .tab .btn-long .num{
  color: #e03131;
  padding-left: 5px;
}
.image {
  padding: 0px 5px;
  float: left;
}
.image img {
  width: 77px;
  height: 77px;
}
#prop .city {
  font-size: 12px;
  color:#999;
  padding-left: 5px;
}
#prop .fa-angle-right {
  float: right;
  position: absolute;
  right: 0px;
  top: 30px;
  padding-top: 20px;
  font-size: 22px;
}
.result-value {
  display: flex;
  align-items: center;
  justify-content: center;
}
.bar-footer .icon {
  color: #e03131;
  top: 3px;
  width: 24px;
  height: 24px;
  padding-top: 0;
  padding-bottom: 0;
  display: block;
  margin: auto;
  font-size: 21px;
}
#owner-of-property {
  height: 55px;
  background:#f0f0f0!important;
  width: 100%;
  border-bottom: 1px solid #cbcbcb;
  padding-left: 0px!important;
  padding-right: 0px!important;
}
#owner-of-property div {
  height: 47px;
  background: #5e5e5e!important;
  width: 100%;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  padding: 0;
  border-bottom: 1px solid #cbcbcb;
}
#owner-of-property .icon {
  font-size: 21px;
  padding: 10px;
}
#owner-of-property span {
  color: white;
  font-size: 13px;
  padding-right: 10px;
}
#owner-of-property .btn {
  background: white;
  color: #a1a1a1;
  height: 24px;
  width: 130px;
  border: none;
  float: right;
  position: absolute;
  right: 10px;
}
#help {
  z-index: 10000;
  background: white;
  width: 100%;
  height: 100%;
  position: absolute;
  top:44px;
}
.fa-question-circle {
  width: 13px;
  font-size: 15px;
  margin-top: 0px;
  padding-left: 3px;
  color: #e13232;
  vertical-align: top;
  padding-top: 3px;
  display: inline-block;
}
.refer label {
  padding-left: 10px;
}
.refer .header .active {
  color: #42c02e!important;
}
.share-wrapper {
  padding: 10px 5px 10px 5px;
  background: #e5e5e5 !important;
}
.share{
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100px;
}
.share > div{
  display: inline-block;
  width: 20%;
  overflow: hidden;
  vertical-align: top;
  font-size: 12px;
  text-align: center;
  line-height: 11px;
}
.share img{
  width: 100%;
  padding: 7px 11px 7px 11px;
  height: auto;
  max-width: 100px;
}
.address{
  color: #3e3e3e;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  padding: 10px 0px 10px 10px;
  font-weight: 500;
}
.changeCondition {
  z-index: 10;
  position: absolute;
  background: white;
  width: 100%;
  bottom: 0;
}
.bdrms span{
  padding-right: 3px;
}
.bdrms {
  font-size: 12px;
  color: #777;
  padding-left: 10px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
h4 {
  margin-bottom: 0px;
  padding: 10px;
}

h4 span:not:first-child {
  padding:0px 5px;
}
h4 .fa{
  padding-left: 10px;
}

#result .small {
  font-size: 12px;
}

#eva-btn-wrapper{
  padding: 10px;
}
#result .center img, #result .center span{
  vertical-align: middle;
}
#result .center span{
 padding-left: 10px;
}
#result .center .fa{
 padding-top: 5px;
}
.center {
  text-align: center;
}
#eva-btn-wrapper .btn{
  height: 40px;
  padding: 10px;
  margin: 0;
}
#result{
  width: 100%;
}
#result > div {
  padding: 10px;
}
#result .ret > div{
  display: inline-block;
}
#result .tl{
  width: 45%;
  font-size: 17px;
  vertical-align: top;
}
#result .val-wrapper{
  width: 55%;
  text-align: right;
  /* font-size: 13px; */
}
#result .val{
  font-size: 12px;
}
#result .price{
  font-size: 20px;
  font-weight: bold;
  padding:5px;
}
.val-wrapper .na{
  font-size: 18px;
}
.val-wrapper .range{
  padding-top: 10px;
}
.report{
  height: 43px;
  padding: 10px 10px;
  font-size: 13px;
  text-align: center;
  border-bottom: 1px solid #eaeaea;
  text-decoration: underline;
  display: block;
}
.bar-footer {
  vertical-align: middle;
  color: white;
  background-color: #efefef;
  padding-left: 0;
  padding-right: 0px;
  z-index: 1;
  height: 50px;
}
.result-value .outer-div{
  background-color:white;
  border-radius: 50%;

}
.bar-footer .tab-label {
  display: block;
  font-size: 10px;
  color: #666;
}
.vux-range-input-box {
  margin: 0px!important;
  padding: 0px!important;
}
.bar-footer .update {
  width: 50%;
  height: 100%;
  text-align: center;
  padding-left: 0;
  display: flex;
  justify-content:center;
  align-items: center;
  /* border-top: 1px solid #f1f1f1; */
  border: none;
  color: black;
  background: white;
}
.bar-footer .update.active {
  background-color: #42c02e;
  color: white;
}
.bar-footer .update.active.disabled {
  background-color: #dddd;
}
.bar-footer a.pull-right {
  padding: 4px 7px 0 7px;
}
#help .bar-footer {
  background-color: #E03131!important;
  text-align: center;
  vertical-align: middle;
  line-height: 44px;
}
</style>
