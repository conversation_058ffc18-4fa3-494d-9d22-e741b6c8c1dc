<template lang="pug">
div.prop-part
  div.prop-table
    div(v-for='prop in list', :key='prop._id')
      .prop-info(@click='selectProp(prop._id)', :class='{selected:prop.selected}')
        span.stp(:class='prop.tagColor')
          span {{prop.saleTpTag || prop.lstStr}}
        span.sort-number(v-if="selectedProp.indexOf(prop._id) >-1",v-show='from == "showing"')
          | {{selectedProp.indexOf(prop._id)+1}}
        span.table-image
          img(:src='getPropThumbnail(prop)', onerror="this.onerror=null;this.src='/img/noPic.png';", style="background-image: url('/img/noPic.png');", :style="{'height':propImgHeight+'px'}", referrerpolicy="same-origin")
          span.dom(v-if='prop.dom && !prop.login') {{prop.dom}} {{_('days')}}
          span.date.stp(:class='prop.tagColor',v-if="(prop.tagColor == 'red') && (prop.spcts||prop.mt||prop.ts) ") &nbsp;{{(prop.spcts||prop.mt||prop.ts) |dotdate}}
        .addr.one-line(v-if='!prop.login')
          p(v-show='prop.addr && prop.addr') {{prop.unt}} {{prop.addr}}
          p(v-show='(!prop.addr) && prop.cmty')  {{prop.cmty}}
          p {{prop.city}}, {{prop.prov}}
        .addr.one-line(v-if='prop.login')
          p(v-show='prop.addr') {{prop.addr}}, {{prop.city}}
          p(v-show='!prop.addr') {{prop.city}}, {{prop.prov}}
        p.bdrms(v-if='!prop.login')
          span(v-if='prop.bdrms')
            span.fa.fa-rmbed
            |  {{prop.bdrms}}{{prop.br_plus?'+'+prop.br_plus:''}}
          span(v-if='prop.rmbthrm || prop.tbthrms || prop.bthrms')
            span.fa.fa-rmbath
            |  {{prop.rmbthrm || prop.tbthrms || prop.bthrms}}
          span(v-if='prop.rmgr||prop.tgr||prop.gr')
            span.fa.fa-rmcar
            |  {{prop.rmgr||prop.tgr||prop.gr}}
        p.price(v-if='!prop.login')
          span {{formatPrice(prop.sp || prop.lp || prop.lpr)}}
          span.price-change.sold(v-if='prop.sp') {{formatPrice(prop.lp || prop.lpr)}}
        p(v-if='!prop.login && (prop.sqft || (prop.sqft1&&prop.sqft2) || prop.rmSqft)')
          span(v-if='prop.sqft') {{parseSqft(prop.sqft)}}
          span(v-else-if='prop.sqft1&&prop.sqft2') {{prop.sqft1}}-{{prop.sqft2}}
          span(v-if='prop.rmSqft && prop.login')
            span(v-if='prop.sqft && /\-/.test(prop.sqft) && (prop.sqft!=prop.rmSqft)') ({{parseSqft(prop.rmSqft)}})
            span(v-if='!prop.sqft') {{parseSqft(prop.rmSqft)}} ({{_('Estimated')}})
          span {{_('ft&sup2;')}}
        p(v-if='!prop.login && prop.front_ft') {{prop.front_ft}} * {{prop.depth}} {{prop.lotsz_code}} {{prop.irreg}}
    div(v-if='from == "showing"')
      .prop-info.add-new-prop(@click='addProp()')
        span.plus-icon(:style="{'height':propImgHeight+'px','line-height':propImgHeight+'px'}")
          span.fa.fa-plus
        p.addr {{_("Add prop by ID")}}
  .btn-cell.bar.bar-tab
    a.btn.btn-tab.btn-half.btn-sharp.btn-fill.btn-negative(@click='commitSelected()')
      | {{_(action)}}
      span.badge(style='color:white') {{selectedProp.length}}
    a.btn.btn-tab.btn-half.btn-sharp.btn-fill.length(@click='reset()') {{_('Cancel')}}

</template>

<script>
import filters from '../filters'

export default {
  filters:{
    dotdate:filters.dotdate,
    // currency:filters.currency,
  },
  components:{

  },
  props:{
    dispVar:{
      type:Object,
      default:function () {
        return {};
      }
    },
    action:{
      type:String,
    },
    from:{
      type:String,
    },
    list:{
      type:Array,
      default:function() {
        return []
      }
    }
  },
  data () {
    return {
      propImgHeight:0,
      selectedProp:[],
    };
  },
  mounted () {
    var self = this;
    this.propImgHeight = parseInt((window.innerWidth-48)/1.6/2);
    bus.$on('selected-ids',function (ids) {
      self.selectedProp = ids;
    });
  },
  methods: {
    addProp(){
      window.bus.$emit('add-prop', this.selectedProp);
    },
    formatPrice(price) {
      if (typeof price == "number") {
        return '$' + price.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
      } else {
        return price;
      }
    },
    parseSqft(sqft){
      if (/\-/.test(sqft)) {
        return sqft;
      }
      if ('number' == typeof sqft) {
        sqft = ''+sqft;
      }
      if (/\./.test(sqft)) {
        return sqft.split('.')[0];
      }
      return sqft;
    },
    reset(){
      window.bus.$emit('reset', {});
    },
    selectProp(id){
      var self = this;
      var idx = self.selectedProp.indexOf(id);
      var prop = self.list.find(p => p._id == id)
      if(idx >= 0){
        self.selectedProp.splice(idx,1);
        prop.selected = false;
      }else{
        self.selectedProp.push(id);
        prop.selected = true;
      }
      self.$forceUpdate();
    },
    getPropThumbnail(prop) {
      return prop.thumbUrl || prop.image || '/img/noPic.png';
    },
    commitSelected(){
      var self = this;
      if(self.selectedProp.length <1 ){
        return window.bus.$emit('flash-message',this.$parent._('No Selection'));
      }
      window.bus.$emit('selected-prop', this.selectedProp);
      self.showPropTable = false;
      self.selectedProp = [];
      self.list.forEach(p => {
        p.selected = false;
      })
      self.$forceUpdate();
    },
  }
}
</script>


<!-- Add "scoped" attribute to limit CSS to this component only -->
<style lang="scss" scoped>
@import '../../../style/sass/apps/components/propShareModal.scss';
.btn-cell {
  padding: 0;
  border-top: none;
  display: table;
  width: 100%;
  height: 44px;

  &>a.btn-half {
    padding: 13px 0 0 0;
    height: 50px;
  }

  .length {
    color: black;
  }
}
</style>
