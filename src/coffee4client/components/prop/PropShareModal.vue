<template lang="pug">
//- not used
div.prop-part#share-showing-wrapper
  div.prop-list-container
    div.prop-table
      div(v-for="prop in list" @click="check(prop)")
        div.prop-info(:class="{selected:checkedList[prop._id]}")
          img(v-if="!prop.isProj" :src="prop.thumbUrl" onerror="this.onerror=null;this.src='/img/noPic.png';" referrerpolicy="same-origin")
          p(v-show="prop.priceValStrRed") {{prop.priceValStrRed}}
          div.addr.one-line(v-if="!prop.login")
            p(v-show="(prop.addr )&& prop.addr") {{prop.unt}} {{prop.addr}}
            p(v-show="(!prop.addr) && prop.cmty") {{prop.cmty}}
            p {{prop.city}}, {{prop.prov}}
          div.addr.one-line(v-if="prop.login")
            span(v-if="prop.addr") {{prop.addr}}, {{prop.city}}
            span(v-else) {{prop.city}}, {{prop.prov}}
</template>

<script>
import rmsrv_mixins from '../rmsrv_mixins'
export default {
  mixins: [rmsrv_mixins],
  props: {
    dispVar:{
      type:Object,
      default:function () {
        return {};
      }
    },
    checkedList:{
      type:Object,
      default:function () {
        return {}
      }
    },
    list: {
      type: Array,
      default: function () {
        return []
      }
    }
  },
  data () {
    return {
      strings:{
        maximumListing:{key:'Maximum %d listings in one showing',ctx:'showing'},
        maximumPropsStr:{key:'Maximum 40 properties!'},
      }
    };
  },
  methods:{
    check(p){
      var tmp = {}, val,self = this;
      val = this.checkedList[p._id]?false:true;
      if (val) {
        // 如果正在选择showing部分房源，直接传入_id
        if(this.dispVar.listShowingMode){
          if(Object.keys(this.checkedList).length>(vars.propsLimit-1)){
            return window.bus.$emit('flash-message', self.sprintf(self._(self.strings.maximumListing.key,self.strings.maximumListing.ctx),vars.propsLimit));
          }
          val = p._id
        }else{
          // val = /^RM1/.test(p.id)?p.id:p._id;
          // 查询列表已支持object
          val = p._id;
        }
      }
      tmp[p._id] = val;
      let checkedList = Object.assign({}, this.checkedList, tmp);
      if (val == false) {
        delete checkedList[p._id];
      }
      // .filter((i)=>{return checkedList[i]!==false})
      var length=Object.keys(checkedList).length;
      if (length > 40) {
        for (var key in checkedList) {
          delete checkedList[key];
          break;
        }
        window.bus.$emit('flash-message', self._(self.strings.maximumPropsStr.key));
      }
      this.$emit('update:checkedList', checkedList)
      // console.log(Object.values(checkedList))
    }
  }
}
</script>
