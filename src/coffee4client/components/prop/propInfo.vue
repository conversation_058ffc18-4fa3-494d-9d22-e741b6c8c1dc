<template lang="pug">
div(style="margin:8px 0 20px;")
  div.none
    | {{_('Sale')}} {{_('Rent')}} {{_('Sold')}} {{_('Rented')}} {{_('Unavailable')}}
  div.img
    img(:src="img || '/img/noPic.png'", referrerpolicy="same-origin")
    div.sid {{prop.sid}}
  div.detail
    div.addr {{prop.addr}}
    div
      span.lp
        span(:class="{line:price[1]}") {{price[0] | currency('$', 0)}}
      span.state {{status}}
    div.spec
      i.fa.fa-rmbed
      //- (v-if="prop.bdrms||prop.tbdrms")
      span {{prop.bdrms || prop.tbdrms}}{{prop.br_plus?'+'+prop.br_plus:''}}
      i.fa.fa-rmbath
      //- (v-if="prop.bthrms")
      span {{prop.rmbthrm || prop.tbthrms || prop.bthrms || 0}}
      i.fa.fa-rmcar(v-if="prop.tgr!=0||prop.gr!=0||prop.rmgr!=0")
      span(v-if="prop.tgr!=0||prop.gr!=0||prop.rmgr!=0") {{prop.rmgr || prop.tgr || prop.gr}}
      span(v-if="prop.ptype2") | {{prop.ptype2.join(' ')}}
    div.date
      span {{computedLstd | dotdate(false,'-')}}
      |  • {{prop.city}}
</template>

<script>
import filters from '../filters'
export default {
  mixins: [],
  filters:{
    dotdate:filters.dotdate,
    currency:filters.currency,
  },
  props:{
  },
  data () {
    return {
      prop:{ptype2:[]},
      img:'',
    };
  },
  computed:{
    computedLstd(){
      return this.prop.onD
    },
    status(){
      if (this.prop.status=='A') {
        if (this.isSale) {
          return this._('Sale')
        }
        return this._('Rent')
      } else {
        if (/Lsd|Sld/.test(this.prop.lst)) {
          if (this.isSale) {
            return this._('Sold')
          }
          return this._('Rented')
        }
        return this._('Unavailable')
      }
    },
    isSale(){
      return /Sale/i.test(this.prop.saletp)
    },
    price(){
      if (this.prop.status !== 'A' && this.prop.sp) {
        return [this.prop.sp, true]
      }
      return [this.prop.lp||this.prop.lpr,false]
    },
    showSearchIcon() {
      return this.post.src=='property' && vars.src!=='property'
    }
  },
  methods: {
    showMembers() {
      window.bus.$emit('showReaders');
    },
  },
  mounted () {
    // if link from property, open exist or create a new one.
    var self = this;
    if (vars && vars.post && vars.post.prop) {
      self.prop = Object.assign({}, vars.post.prop);
    }
    if (vars && vars.post) {
      self.img = vars.post.thumb;
    }
    if (self.prop.thumbUrl) {
      self.img = self.prop.thumbUrl;
    }
    if (!window.bus) {
      console.error('global bus is required!');
      return;
    }
    window.bus.$on('addComment',function(d) {
    });
  },
  components: {
  }
}
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped>
.detail .addr{
  font-size: 15px;
  padding: 0 0 1px 0;
}
.detail .spec{
  font-size: 13px;
  padding: 3px 0 0px 0;
}
.detail .spec > span {
  padding-left: 3px;
  margin-right: 5px;
  color: #777;
}
.detail .date{
  color: #999;
  font-size: 11px;
}
.detail .state{
  padding: 1px 4px;
  color: white;
  background: #e03131;
  border-radius: 1px;
  font-size: 11px;
  vertical-align: bottom;
  margin-left: 8px;
}
.detail .lp{
  color: #e03131;
  font-size: 16px;
  font-weight: bold;
}
.none{
  display:none;
}
.img img{
  width:100%;
  padding: 0 10px 0 0;
  height: 83px;
}
.img .sid{
  height: 17px;
  font-size: 12px;
  color: white;
  background: rgba(0, 0, 0, 0.5);
  text-align: center;
  margin-right: 10px;
  margin-top: -22px;
  position: relative;
}
.img{
  margin-right:1px;
  padding-top: 3px;
  vertical-align: top;
  width: 130px;
  display: inline-block;
}
.detail{
  width: calc(100% - 152px);
  display: inline-block;
}
</style>
