<template lang="pug">
#evaluation-hist(v-cloak)
  header#header-bar.bar.bar-nav(v-if="!nobar")
    h1.title.ng-cloak!= "{{_('RealMaster')}} {{_('Evaluation Hist','evaluation')}}"
  //- div#address
  //-   span.fa.fa-map-marker(style="vertical-align:middle")
  //-   span.valign {{address}}
  div.hist
    div(v-for="(hist,$index) in hists", @click="openHist(hist)")
      evaluation-hist(:disp-var="dispVar", :hist="hist",:uaddr="uaddr", :users="users", :index="$index", :max-sale-price='maxSalePrice', :max-rent-price='maxRentPrice')
</template>

<script>
import EvaluationHist from './frac/EvaluationHist'
import pagedata_mixins from './pagedata_mixins'
// import file_mixins from './file_mixins'

export default {
  mixins: [pagedata_mixins],
  props:{
  },
  components:{
    EvaluationHist,
  },
  data () {
    return {
      hists:[],
      address:'',
      addr:{},
      prop:{},
      dispVar: {
        isApp:true,
        isCip:false,
        isAdmin: false,
        isLoggedIn:true,
        lang:'en',
        sessionUser: {},
        reqHost:'',
        isVipRealtor: false
      },
      datas: [
        'isApp',
        'isCip',
        'isAdmin',
        'isLoggedIn',
        'lang',
        'sessionUser',
        'reqHost',
        'isVipRealtor'
      ],
      nobar: false,
      users:{},
    };
  },
  mounted () {
    if (!window.bus) {
      console.error('global bus is required!');
      return;
    }
    var bus = window.bus, self = this;
    this.getPageData(this.datas,{},true);
    bus.$on('pagedata-retrieved', function (d) {
      self.dispVar = Object.assign(self.dispVar, d);
      self.init();
    });
    // if open from url, can init from url params.
    self.uaddr = vars.uaddr;
    if (vars.nobar) {
      this.nobar = true;
      // document.getElementById("address").style.marginTop='0px';
    }
    window.bus.$on('add-hist', function(hist){
      // self.prop.uaddr = hist.uaddr;
      hist.uid = self.dispVar.sessionUser._id;
      self.users[self.dispVar.sessionUser._id] = {avt:self.dispVar.sessionUser.avt,nm:self.dispVar.sessionUser.nm};
      hist = Object.assign(hist, self.prop);
      self.hists.unshift(hist);
    });

  },
  computed:{
    maxSalePrice:  function() {
      var max = 0;
      for (let hist of this.hists) {
        if (hist.result && hist.result.p > max)
          max = hist.result.p;
      }
      return max;
    },
    maxRentPrice:  function() {
      var max = 0;
      for (let hist of this.hists) {
        if (hist.result && hist.result.rent && hist.result.rent.p > max)
          max = hist.result.rent.p;
      }
      return max;
    },
  },
  methods: {
    toggleModal(a){
      toggleModal(a);
    },
    init() {
      var self = this;
      this.$http.post('/1.5/evaluation/hist',{uaddr:vars.uaddr}).then(
        function(ret) {
          ret = ret.data
          if (ret.ok == 1) {
            if (ret.evaluate) {
              self.users = ret.evaluate.users;
              self.hists = ret.evaluate.hist.splice(0,100);
              self.address =  ret.evaluate.addr + ',' + ret.evaluate.city + ',' + ret.evaluate.prov;
            }
            self.hists.sort(function(a, b){
              return new Date(b.ts) - new Date(a.ts)
            });
          } else {
            console.log(ret.e)
            self.msg = "error";
          }
        },function(ret){
          ajaxError(ret);
        });
    },
    openHist(hist) {
      var url = 'http://' + this.dispVar.reqHost + '/1.5/evaluation/result.html?inframe=1&hist=1&uaddr=' + encodeURIComponent(this.uaddr) + '&id=' + hist._id;
      if (vars.fromMls) {
        url=url+"&fromMls=1";
      }
      if(this.dispVar.isApp) {
        RMSrv.openTBrowser(url, {nojump:true, title:this._('Estimate Report','evaluation')});
      } else {
        window.document.location.href = url;
      }
    }
  }
}

</script>


<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped>
.props{
  position: absolute;
  top: 0;
  overflow: auto;
  /* padding: 5px; */
  height: 100%;
  width: 100%;
  z-index: 10;
}
.valign {
  position: absolute;
  top: 50%;
  transform: translate(0, -50%);
}
h4 .fa {
  padding: 0px 10px 10px 10px;
  font-size: 20px;
}
#details{
  padding-bottom: 20px;
}
#evaluation {
  padding-bottom: 44px;
  overflow-y: auto;
  background: #f1f1f1;
  color: #626262;
}
#evaluation h4 {
  padding-left: 5px;
  line-height: 2;
}
[v-cloak] {
  display: none;
}

#input-box {
  margin: 0 10px;
}
#address .fa {
  font-size: 20px;
  padding-right: 20px;
}
#address {
  width: 100%;
  font-size: 12px;
  white-space: nowrap;
  text-overflow: ellipsis;
  width: 100%;
  margin-bottom: 10px;
  height: 66px;
  background: white;
  border-bottom: 1px solid #ddd;
  padding:20px;
  margin-top: 44px;
  position: relative;
}
.bar-footer {
  text-align: center;
  vertical-align: middle;
  color: white;
  background-color: #E03131;
  z-index: 1;
}
#evaluation-hist {
  height: 100%;
  padding-bottom: 20px;
}
.hist {
  overflow: auto;
  height: 100%;
  height: calc(100% - 124px);
  padding-top:10px;
}
</style>
