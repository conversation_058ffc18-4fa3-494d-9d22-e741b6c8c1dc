<template lang="pug">
#evaluation-main(style='height:100%;', v-cloak='v-cloak')
  header#header-bar.bar.bar-nav(v-if='!inframe')
    a.icon.fa.fa-back.pull-left(v-if='!share && !noBack', @click='goBack()')
    h1.title.ng-cloak {{_('Estimate Report','evaluation')}}
  share-dialog(:w-dl.sync='wDl', :w-sign.sync='wSign', :disp-var='dispVar', :height='160', :prop='prop', :no-lang='true')
  get-app-bar(:w-dl.sync='wDl', :owner.sync='dispVar.ownerData', v-show='share')
  .WSBridge(style='display:none;')
    span#share-title-en {{shareTitleEn}}
    span#share-desc-en {{shareDescEn}}
    span#share-title {{shareTitle}}
    span#share-desc {{shareDesc}}
    span#share-image {{shareImage}}
    #share-url {{shareUrl}}
  .err(v-if='errMsg') {{errMsg}}
  #busy-icon.overlay.loader-wrapper(v-show='loading')
    .loader
  .mask(v-if='loading || showmask', @click='showmask=false')
  .calc(v-show='loading')
    span {{_('Calcuating', 'evaluation')}}
  #help(v-show='showHelp', :class='{notop: inframe}')
    div
      div(style='padding: 90px 20px 0px 20px;')
        | {{_('This evaluation does NOT consider many factors of the subject property and the transaction of sale.','evaluation')}}
        | {{_('Ravine, view, T junction, lot size, building material, orientation, renovation, interior design, layout, floor level,building style, sellers and/or buyers’ human factors,','evaluation')}}
        | {{_('negotiation procedure and consideration, and many more.','evaluation')}}
        | {{_('This evaluation is not suitable as a referring point to make a sale or buying decision.','evaluation')}}
    .bar.bar-standard.bar-footer.row(@click='closeHelpDiv()')
      span {{_('OK')}}
  rm-brkg-phone-list(v-if="dispVar.isApp", :cur-brkg="brkg")
  sign-up-form-contact-realtor(:prop="prop",:brkg='brkg',:owner='owner', :feedurl='feedurl', :user-form='userForm', :title='signupTitle', @click.prevent.stop='noop(e)',:disp-var='dispVar')
  .content(v-show='!loading && !errMsg', :class='{padding44: !inframe}')
    #evaluation.content-list(:class='{notop: inframe}')
      div
        .result-mask(:style='{background:computedBgImg}')
          .result-wrapper(:style='{background:computedPropBg()}')
            #prop(@click='openPropDetail()')
              div(style='float:left; width:calc(100% - 90px);')
                .address.trim {{address}}
                .bdrms
                  span(v-show='prop.bdrms')
                    span.fa.fa-rmbed
                    span  {{prop.bdrms}}
                  span(v-show='prop.bthrms')
                    span.fa.fa-rmbath
                    span  {{prop.bthrms}}
                  span(v-show='prop.gr')
                    span.fa.fa-rmcar
                    span  {{prop.gr}}
                  span(v-show='prop.tp')
                    span  {{_(prop.tp)}}
                  span(v-show='prop.br_plus')
                    span  {{prop.br_plus}}
                    span {{_('den')}}
                  span.text(v-show='prop.sqft') {{prop.sqft}}{{_('sqft','prop')}}
              //- .tracking
              //-   span.btn(v-show='prop.tracked', @click='addTrack($event,false)') - {{_('Saved','evaluation')}}
              //-   span.btn(v-show='!prop.tracked', @click='addTrack($event,true)') + {{_('Save','evaluation')}}
            #result
              div(v-if='estimated_price')
                .price {{estimated_price | currency('$',0)}}
                .small {{_('Sale Price','evaluation')}}
              div(v-if='estimated_rent_price')
                .price  {{estimated_rent_price | currency('$',0)}}
                .small {{_('Rent Price')}}
              div(v-if='!estimated_price && !estimated_rent_price && !loading', style='padding-left: 15px;padding-bottom:30px;')
                span {{_('No Result', 'evaluation')}}
            .estimated_price1(v-if='dispVar.isLoggedIn && !share && estimated_price1')
              .small.pull-left {{_('30 Days Forecast','evaluation')}}
              .price(v-if='dispVar.isVipUser') {{estimated_price1 | currency('$',0)}}
              .vip(v-if='dispVar.isVipUser') {{_('VIP Only','evaluation')}}
              .vip-only(v-else='v-else')
                span {{_('VIP Only','evaluation')}}
                span.icon.icon-right-nav(@click='openVipPage()')
        #accuracy
          .accuracy-wrapper
            span(style='font-size: 15px; font-weight: bold;') {{_('Accuracy','evaluation')}}
            span.accuracy {{_(accuracy,'evaluation')}}
            span.pull-right(v-if='prop.ts', style='font-size:12px;color:#999;')
              | {{formatTs(prop.ts)}}
              span(style='padding-left:5px;') {{_('Evaluated','evaluation')}}
          .result-range(v-if='range_from')
            .min
              div {{_('MIN')}}
              .txt
                span.dollar $
                span {{roundPriceToK(range_from)}}
                span.k K
            .mid
              .line-wrapper
                .line
              span.txt(:style='{left:accuracyLeftWidth}')
                span $
                span {{roundPriceToK(estimated_price)}}K
            .max
              div {{_('MAX')}}
              .txt
                span.dollar $
                span {{roundPriceToK(range_to)}}
                span.k K
          .report-outter-wrapper
            .btn(v-if='!fromMls && !fromHist && !share', @click='goToEvaluate(true)') {{_('Adjust conditions and re-evaluate','evaluation')}}
            .report-wrapper(@click='showHelpDiv()')
              div
                span.fa.fa-exclamation-circle
              .txt
                span
                  | {{_('The evaluation result is NOT suitable as reference for decision making.','evaluation')}}
                span(v-if='!share')
                  | {{_('Find a problem with this evaluation?','evaluation')}}
                  a.report(@click='reportError($event)') {{_('Report Error')}}
        div(v-if='!share')
          evaluation-hist-cnt-card(:uaddr='uaddr', :histcnt='histcnt', :disp-var='dispVar', :no-hist='noHist', :address='address', :prop='prop', :propcnt='propcnt')
      .block
        .header {{_('Evaluation Conditions','evaluation')}}
        .condition
          div
            .items(v-if='prop.sqft')
              .small.trim {{_('Square Footage')}}
              .val {{prop.sqft}}
            .items(v-if='prop.tax')
              .small.trim {{_('Property Tax')}}
              .val ${{prop.tax}}
            .items(v-if='prop.reno')
              .small {{_('Condition')}}
              .val {{getReno(prop.reno)}}
            .items(v-if='dist_range')
              .small.trim {{_('Distance Range')}}
              .val
                | {{dist_range}}
                span.suffix m
            .items(v-if='last')
              .small.trim {{_('Time Range')}}
              .val
                | {{last}}
                span.suffix {{_('months')}}
          .re-evaluate(v-if='!fromMls && !fromHist && !share')
            .pull-right.btn(@click='goToEvaluate(true)') {{_('Adjust conditions and re-evaluate','evaluation')}}
        .comparables
          .header
            span {{_('Comparable Listings','evaluation')}}
            span.pull-right(style='padding-left: 20px;', v-show="showRef('rental')", @click="selectSaleOrRent('rent')", :class="{active: saleOrRent=='rent'}") {{_('For Rent')}}
            span.pull-right(v-show="showRef('sale')", @click="selectSaleOrRent('sale')", :class="{active: saleOrRent=='sale'}") {{_('For Sale ')}}
          div
            prop-list-element-eval(v-for='prop in computedDispList', :sale-or-rent='saleOrRent', :prop='prop', :disp-var='dispVar', :key='prop._id')
          .header.center.see-all(v-show='currentList.length>2', @click='showmore = !showmore')
            a(v-show='!showmore') {{_('See All')}} ({{currentList.length}})
            a(v-show='showmore') {{_('See Less')}}
      .block.center.start-evaluation(v-if='!dispVar.isApp')
        div(style='font-size:17px; font-weight:500;') {{_('RealMaster Evaluation','evaluation')}}
        div(style='font-size:12px; color:#999; padding-top: 7px;') {{_('Estimate your home value','evaluation')}}
        a.btn(href='/adPage/needAPP') {{_('Start New Evaluation','evaluation')}}
  .bar.bar-standard.bar-footer(v-if='!errMsg && !share')
    span.pull-left
      a.btn.btn-positive.btn-segment.no-border.brkg(v-if="!dispVar.isRealtor",@click="getContactRealtor({prop,hasWechat,page:'evaluation'})")
        div.brkg-wrapper
          img.agent-avt(:src="computedAgentSrc", v-if="dispVar.rltrTopAd", referrerpolicy="same-origin")
          span {{_('Request Appraisal')}}
    a.pull-right(@click='showSMB()')
      span.icon.sprite16-21.sprite16-1-1
      span.tab-label {{_('Share')}}
    a.pull-right.fav(@click='toggleTrack()', v-if="dispVar.isLoggedIn")
      span.icon.sprite16-21(:class="{'sprite16-2-3':prop.tracked,'sprite16-1-3':!prop.tracked}")
      span.tab-label {{_('Save','Save')}}
  flash-message
</template>

<script>
import PropListElementEval from './frac/PropListElementEval.vue';
import ShareDialog from './frac/ShareDialog2'
import GetAppBar from './frac/GetAppBar'
import SignUpFormContactRealtor from './frac/SignUpFormContactRealtor.vue'
import FlashMessage from './frac/FlashMessage'

// import filters from './filters'
import file_mixins from './file_mixins'
import evaluate_mixins from './evaluate_mixins'
import rmsrv_mixins from './rmsrv_mixins'
import pagedata_mixins from './pagedata_mixins'
import contactRealtor_mixins from './contactRealtor_mixins'
import EvaluationHistCntCard from './frac/EvaluationHistCntCard.vue';
import RmBrkgPhoneList  from './frac/RmBrkgPhoneList.vue'
import filters from './filters'

export default {
  mixins: [file_mixins,evaluate_mixins,rmsrv_mixins,pagedata_mixins,contactRealtor_mixins],
  filters:{
    currency:filters.currency,
  },
  components:{
    PropListElementEval,
    ShareDialog,
    GetAppBar,
    SignUpFormContactRealtor,
    EvaluationHistCntCard,
    FlashMessage,
    RmBrkgPhoneList
  },
  props:{
  },
  data () {
    return {
      brkg:{},
      hist:{},
      prop:{
        bdrms:1,
        reno:3,
        sqft:0,
        tax:0,
        tracked:false,
        addr:'',
        img:'',
        city:'',
        prov:''
      },
      last:0,
      range_from: 0,
      range_to: 0,
      estimated_price: 0,
      estimated_price1: 0,
      estimated_rent_price:0,
      excl:[],
      incl:[],
      inclIds:[],
      exclIds:[],
      dists:[],
      rentalExcl:[],
      rentalIncl:[],
      rentalInclIds:[],
      rentalExclIds:[],
      dist_range: 50,
      max_dist_range: 3200,
      accuracy: 0,
      showRentalRef: false,
      rental_dist_range:0,
      rental_last:0,
      histId:'',
      uaddr:'',
      showHelp: false,
      address:'',
      shareAddress:'',
      currentList:[],
      showmore:false,
      currentExcl:[],
      currentTab:null,
      showOwner: true,
      saleOrRent: '',
      evaluationPaddingTop: 44,
      wDl: false,
      wSign: true,
      wComment: true,
      wId: false,
      // disabled: false,
      currentExclCnt:0,
      currentUsedCnt:0,
      share:false,
      fromHist:false,
      noHist:false,
      histcnt:0,
      totalhist:0,
      propcnt:0,
      iconType:'',
      changeSelectProp: false,
      showmask:false,
      fromMls: vars.fromMls,
      inframe : vars.inframe,
      loading:true,
      userForm:{
        ueml:'<EMAIL>',
        nm:'',
        fn:'',
        ln:'',
        eml:'',
        formid:'system',
        url: document.URL,
        tp:'evaluationReport',
        mbl:'',
      },
      signupTitle: this._('Report Error'),
      owner:  {vip:1},
      feedurl: '/1.5/form/forminput',
      computedBgImg:'transparent',
      hasImage:false,
      dispVar: {
        defaultEmail:'',
        isApp:true,
        isCip:false,
        isLoggedIn:true,
        lang:'en',
        sessionUser: {},
        reqHost:'',
        isVipUser:false,
        streetView:'',
        streetViewMeta:'',
        isAdmin:'',
        isVipRealtor:'',
        isRealtor:''
      },
      hasWechat:true,
      datas: [
        'defaultEmail',
        'isApp',
        'isCip',
        'isLoggedIn',
        'lang',
        'sessionUser',
        'reqHost',
        'isVipUser',
        'streetView',
        'streetViewMeta',
        'isAdmin',
        'isRealtor',
        'isVipRealtor'
      ],
      errMsg:'',
      noBack: false,
    };
  },
  computed:{
    shareImage: function() {
      return 'http://' + this.dispVar.reqHost + '/img/share_evaluation.png';
    },
    shareUrl:  function() {
      var url = '';
      url = 'http://' + this.dispVar.reqHost + '/1.5/evaluation/result.html?share=1&uaddr=' + encodeURIComponent(this.uaddr) + '&id=' + this.histId +'&lang='+this.dispVar.lang;
      return url;
    },
    shareTitle:function() {
      return this._('My evaluation on RealMaster-', 'evaluation') + '-' + this.shareAddress;
    },
    shareTitleEn:function() {
      return 'My evaluation on RealMaster-' +  this.shareAddress
    },
    shareDesc:function() {
      var price = currencyFormat(this.estimated_price, '$', 0);
      return this._('Evaluation Price','evaluation') +':' + price + '.\n' + this._('Evaluation Date','evaluation') + ':'
      +this.formatTs(this.prop.ts || new Date()) + '.\n' + this._('This evaluation is NOT suitable as reference for decision making.','evaluation')
    },
    shareDescEn:function () {
      var price = currencyFormat( this.estimated_price, '$', 0);
      return 'Evaluation Price:' + price + '.\nEvaluation Date :' +this.formatTs(this.prop.ts || new Date()) + '.\n' + 'This evaluation is NOT suitable as reference for decision making.'
    },
    computedDispList:function() {
      var dispList = []
      if (this.currentList.length>0 && this.showmore) {
        dispList = this.currentList;
      } else {
        dispList = this.currentList.slice(0,2);
      }
      return dispList
    },
    accuracyLeftWidth: function() {
      var left = (this.estimated_price-this.range_from)*100/(this.range_to-this.range_from)
      left = 'calc('+left+'% - 20px)';
      return left;
    }
  },
  mounted () {
    if (!window.bus) {
      console.error('global bus is required!');
      return;
    }
    var bus = window.bus, self = this;
    if (vars.share) {
      this.share = true;
    }
    if (this.nobar && !this.share) {
      document.getElementById('evaluation').style.paddingTop = '0px';
      document.getElementById('help').style.paddingTop = '0px';
    }

    if (vars.hist) {
      this.fromHist = 1;
      this.noHist = true
    }
    this.getPageData(this.datas,{},true);
    window.bus.$on('pagedata-retrieved', function (d) {
      self.dispVar = Object.assign(self.dispVar, d);
      if (self.dispVar.defaultEmail){
        self.userForm.ueml = self.dispVar.defaultEmail;
      }
      if (vars.uaddr) {  //for share
        self.uaddr = vars.uaddr;
        self.userForm.id = vars.uaddr;
        self.getHistResult();
      } else if(vars.ids) { //from mls listing
        self.calc();
      }
      if (self.dispVar.sessionUser && self.dispVar.sessionUser.eml) {
        for (let i of ['nm','eml','mbl','fnm','fn','ln']) {
          self.userForm[i] = self.dispVar.sessionUser[i];
        }
        if(!self.userForm.nm || self.userForm.fnm){
          self.userForm.nm = self.userForm.fnm || d.sessionUser.nm_en || d.sessionUser.nm_zh
        }
      }
      if (RMSrv.hasWechat){
        RMSrv.hasWechat((has)=>{
          if (has!=null) {
            self.hasWechat = has
          }
        })
      }
    });
  },
  methods: {
    handleContactRealtorAction() {
      var ret = this.contactRealtorInfo;
      var self = this;
      if (ret.action.UIType.indexOf('form') > -1) {
        self.userForm.tp = 'eval2';
        self.userForm.m = ret.formInfo.formPlaceHolder;
        self.userForm.page = 'evaluation';
        self.userForm.city = self.prop.city;
        self.userForm.prov = self.prop.prov;
        self.userForm.uaddr = self.uaddr;
        // self.userForm.addr = self.prop.addr;
        var prop = self.prop;
        self.userForm.addr = `${prop.unt?(prop.unt+' '):''}${prop.addr}`;
        self.userForm.histId = self.histId;
        self.signupTitle = ret.action.title_tr || ret.action.title;
        self.toggleModal('SignupModal','open');
        if (ret.action.UIType.indexOf('agentCard') > -1) {
          self.brkg = ret.agentInfo;
          if (ret.mailBody) {
            self.brkg.mailBody = ret.mailBody;
            self.brkg.mailSubject = ret.mailSubject;
          }
        }
        self.setSignupModalHeight();
      }
      else if (ret.action.UIType.indexOf('agentCard') > -1) {
        var rmbrkg = {brkg:ret.agentInfo};
        if (ret.mailBody) {
          rmbrkg.mailBody = ret.mailBody;
          rmbrkg.mailSubject = ret.mailSubject;
        }
        bus.$emit('show-rmbrkg', rmbrkg);
      // } else if (ret.action.UIType.includes('showRealtorYellowPage')) {
      //   self.findARealtor();
      }
    },
    // signUpNoWechat(title){
    //   this.userForm.tp = 'eval2'
    //   this.signupTitle = this._(title)
    //   this.toggleModal('SignupModal','open')
    // },
    // findARealtor (opt={}){
    //   // if(!this.hasWechat){
    //   //   return this.signUpNoWechat()
    //   // }
    //   // if(this.prop.bndCity && (this.prop.bndCity.nm != this.prop.city_en)){
    //   //   var city = this.prop.bndCity.nm
    //   // }else{
    //   //   var city = this.prop.city_en
    //   //   var cityName = this.prop.city
    //   // }
    //   var url = "/1.5/user/follow?city=" + this.prop.city + "&prov=" + this.prop.prov;
    //   // if (cityName){
    //   //   url += "&cityName=" + cityName
    //   // }
    //   // if (opt.showla) {
    //   //   url+='&showla=1'
    //   // }
    //   // if (this.prop) {
    //   //   url+='&_id='+this.prop._id;
    //   // } // url+='&propid='+this.prop._id
    //   // if (this.prop.bndSubCity && this.prop.bndSubCity.nm){
    //   //   url+= '&subCity='+this.prop.bndSubCity.nm
    //   // }
    //   // if (this.$parent.closeAndRedirect) {
    //   //   this.$parent.closeAndRedirect(url);
    //   //   return;
    //   // }
    //   // return window.location = url;
    //   if (this.dispVar.isApp) {
    //     // url = this.appendDomain(url);
    //     url += '&inframe=1';
    //     RMSrv.openTBrowser(url,{title:this._('RealMaster')});
    //     return;
    //   }
    //   this.redirect(url);
    // },
    showHelpDiv() {
      this.showHelp=true;
      this.noBack = true;
    },
    closeHelpDiv() {
      this.showHelp=false;
      this.noBack = false;
    },
    goBack() {
      // window.history.back();
       document.location.href = vars.d || '/1.5/evaluation';
    },
    selectSaleOrRent(type) {
      var self = this;
      this.saleOrRent = type;
      this.currentList = [];
      var ids = [];
      var loaded = false;
      if (type =='sale') {
        ids = self.inclIds.concat(self.exclIds);
        loaded = self.incl.length || self.excl.length;
        self.currentUsedCnt = self.incl.length;
        self.currentExclCnt = self.excl.length;
      } else if (type =='rent') {
        ids = self.rentalInclIds.concat(self.rentalExclIds);
        loaded = self.rentalIncl.length || self.rentalExcl.length;
        self.currentUsedCnt = self.rentalIncl.length;
        self.currentExclCnt = self.rentalExcl.length;
      }
      this.selectTab('used');
      if (!loaded && ids.length) {
        self.$http.post('/1.5/evaluation/props', {ids: ids, share:this.share}).then(function(ret) {
          ret = ret.data
          if(ret.ok && ret.props) {
             for(let prop of ret.props) {
               if (self.dists) {
                 var d = self.dists.find(function(p) {
                   return p.id == prop._id;
                 });
                 if (d)
                    prop.dist = d.d;
               }
               if (self.weights) {
                 var d = self.weights.find(function(p) {
                   return p.id == prop._id;
                 });
                 if (d) {
                   prop.weight = d.weight;
                 }
               }
               prop = this.compareDifference(prop);
             }
             if(self.weights) {
               ret.props.sort(function(a, b) {
                  return a.weight.wTotal - b.weight.wTotal;
               });
             }
             for (let prop of ret.props) {
               if (self.inclIds.indexOf(prop._id) >=0) {
                  self.incl.push(prop);
                } else if(self.exclIds.indexOf(prop._id) >=0) {
                  self.excl.push(prop);
                } else if(self.rentalInclIds.indexOf(prop._id) >=0) {
                   self.rentalIncl.push(prop);
                } else if(self.rentalExclIds.indexOf(prop._id) >=0) {
                  self.rentalExcl.push(prop);
                }
             }
             if (type =='sale') {
               self.initPropListImg(self.incl);
               self.initPropListImg(self.excl);
               self.currentList = self.incl;
               self.currentExcl = self.excl;
               self.currentUsedCnt = self.incl.length;
               self.currentExclCnt = self.excl.length;
             } else if (type =='rent') {
               self.initPropListImg(self.rentalExcl);
               self.initPropListImg(self.rentalIncl);
               self.currentList = self.rentalIncl;
               self.currentExcl = self.rentalExcl;
               self.currentUsedCnt = self.rentalIncl.length;
               self.currentExclCnt = self.rentalExcl.length;
             }
          } else if(ret.e) {
            console.log(ret.e);
          }
        },function (ret) {
          ajaxError(ret);
        });
      } else {
        if (type =='sale') {
          for (let prop of self.incl) {
            prop = this.compareDifference(prop);
          }
          for (let prop of self.excl) {
            prop = this.compareDifference(prop);
          }
        }
        else if (type =='rent') {
          for (let prop of self.rentalIncl) {
            prop = this.compareDifference(prop);
          }
          for (let prop of self.rentalExcl) {
            prop = this.compareDifference(prop);
          }
        }
      }
    },
    reportError(e) {
      e.preventDefault();
      e.stopPropagation();
      this.userForm.page = '';
      this.userForm.tp = 'evaluationReport';
      this.userForm.m = '';
      delete this.userForm.city;
      delete this.userForm.prov;
      this.signupTitle = this._('Report Error');
      this.brkg = {};
      this.toggleModal('SignupModal','open')
      this.setSignupModalHeight();
    },
    computedPropBg:function() {
      return this.prop.thumbUrl || this.hasImage ? 'rgba(0, 0, 0, 0.3)' : '#5cb85c';
    },
    getImg: function() {
      var self = this;
      if (this.prop.thumbUrl) {
        this.computedBgImg = 'url(' + this.prop.thumbUrl + ')';
        self.hasImage = true;
      } else {
        if (this.isCip || !this.prop.lat || !this.prop.lng) {
          return 'transparent';
        }
        this.getGoolgeStreeViewImg(this.dispVar, this.prop, function(ret) {
          if (ret) {
            self.hasImage = true;
            self.computedBgImg = 'url(' + ret + ')';
          }else{
            self.computedBgImg = 'transparent';
            self.hasImage = false;
          }
        });
      }
    },
    toggleModal (a,b,c) {
      toggleModal(a, b);
      if (c) {
        toggleDrop()
      }
    },
    noop(e){
      if (e) {
        e.preventDefault();
        e.stopPropagation();
        return 0;
      }
    },
    roundPriceToK: function(p) {
      return currencyFormat(p, '', 0, 3);
    },
    toggleTrack:function() {
      // e.preventDefault();
      // e.stopPropagation();
      var self = this;
      var tracked = !self.prop.tracked;
      this.$http.post('/1.5/evaluation/addTrack',{uaddr:this.uaddr, tracked: tracked}).then(
        function(ret) {
          ret = ret.data;
          if (ret.ok == 1) {
            self.prop = Object.assign({}, self.prop);
            self.prop.tracked = tracked;
            return window.bus.$emit('flash-message', ret.msg);
          } else {
            console.log(ret.e)
          }
        },function(ret){
          ajaxError(ret);
        });
    },
    getPropCount:function(addr) {
      var self = this;
      self.getPropCnt(addr, function(ret) {
        ret = ret.data;
        if (ret.ok == 1) {
          self.propcnt = ret.propcnt;
        } else {
          console.log(ret.e)
        }
      });
    },
    openVipPage() {
      url = 'https://www.realmaster.ca/membership'
      // '/event?tpl=getvip&inframe=1&lang='+this.dispVar.lang
      RMSrv.openTBrowser(url);
    },
    getHistResult:function () {
      var self = this;
      self.loading = true;
      this.$http.get('/1.5/evaluation/result?uaddr='+vars.uaddr+'&id='+vars.id).then(
        function(ret) {
          self.loading = false;
          ret = ret.data
          if (!ret.ok || !ret.hist) {
            if(ret.err) {
              console.log(ret.err);
            }
            self.errMsg = this._('The report is not found, may be deleted by the owner','evaluation');
            if(ret.url) {
              document.location.href = this.appendDomain(ret.url)
            }
            return;
          }
          self.hist = ret.hist;
          self.prop = {};
          self.prop = this.getPropFromVars(ret);
          self.prop.uaddr = ret._id;
          self.prop._id = ret._id;
          self.prop.ts = ret.hist.ts;
          if (ret.tracked)
            self.prop.tracked = ret.tracked;
          self.getPropCount(self.prop);

          self.address =  this.getFormatAddr(ret.addr, self.prop) + ', ' + this._(ret.city) ;
          self.shareAddress = this.getFormatAddr(ret.addr, self.prop) + ', ' + this._(ret.city) + ',' + this._(ret.prov);

          var data = {owner:ret.owner,address:self.address, uaddr:vars.uaddr, hist: ret.hist, histcnt: ret.histcnt};
          self.histId = data.hist._id;
          self.histcnt =  data.histcnt;
          self.dists = data.hist.dists;
          self.weights = data.hist.weights;

          for (let p of this.fields) {
            if (data.hist[p])
              self.prop[p] = data.hist[p];
          }
          self.prop.reno = self.prop.reno || 3;
          var ret = data.hist.result
          if(ret) {
            self.dist_range = ret.range;
            self.last = ret.last;
            self.accuracy = ret.a;
            self.estimated_price =  ret.p;
            self.range_from = ret.f;
            self.range_to = ret.t;
            self.estimated_price1 = ret.p1;
            if (ret.rent) {
              self.estimated_rent_price =  ret.rent.p;
              self.rental_dist_range = ret.rent.range;
              self.rental_last = ret.rent.last;
              if (!self.accuracy && ret.rent.a)
                self.accuracy = ret.rent.a;
            }
          }
          data.owner = data.owner ||[]
          if(data.owner.indexOf(self.dispVar.sessionUser._id) >=0) {
            self.showOwner = false;
          }

          self.inclIds = data.hist.incl || [];
          self.exclIds = data.hist.excl || [];
          self.rentalInclIds = data.hist.incl_r || [];
          self.rentalExclIds = data.hist.excl_r || [];
          self.usedlistingNum = self.inclIds.length;
          self.loading = false;
          if (self.estimated_price) {
            self.selectSaleOrRent('sale')
          } else if (self.estimated_rent_price) {
            self.selectSaleOrRent('rent');
          }
          self.getImg();
          self.getMaxDist();
        },function(ret){
          ajaxError(ret);
        });
    },
    calc:function () {
      this.prop.lat = vars.lat;
      this.prop.lng = vars.lng;
      var self = this;
      this.prop = this.getPropFromVars(vars);
      var addr = self.prop.addr ? self.prop.addr : self.prop.st_num? self.prop.st_num + ' ' + self.prop.st : self.prop.st||''
      addr = this.getFormatAddr(addr, self.prop);
      this.prop.addr = addr;
      this.address = addr + ',' + this._(self.prop.city) ;
      this.shareAddress = addr + ',' + this._(self.prop.city) + ',' + this._(self.prop.prov);
      this.getMaxDist();
      this.getImg()
      var params = {};
      if(vars.ids) {
        this.inclIds = params.ids = vars.ids.split(',');
      }
      if (vars.rentalids) {
        this.rentalids = params.rentalids = vars.rentalids.split(',');
      }
      this.getPropCount(this.prop);
      this.evaluate(params)
    },
    evaluate (data){
      var self = this;
      this.loading = true;
      if (!(self.prop.lat && self.prop.lng)) {
        return RMSrv.dialogAlert("error")
      }
      var success = (ret)=>{
        ret = ret.data;
        if (!ret.ok) {
          console.log(ret.e);
          return;
        }
        var hist = {};
        // this.clearResults();
        self.histcnt = ret.histcnt;
        ret.owner = ret.owner ||[]
        if(ret.owner.indexOf(self.dispVar.sessionUser._id) >=0) {
          self.showOwner = false;
        }
        if (ret.tracked) {
          self.prop.tracked = ret.tracked;
          self.prop = Object.assign({},self.prop);
        }
        self.loading = false;
        self.uaddr = hist.uaddr = ret.uaddr;
        self.userForm.id = ret.uaddr;
        self.histId = ret.histId;
        self.ver = ret.ver;
        self.range_from = ret.f;
        self.range_to = ret.t;
        self.estimated_price = ret.p;
        self.estimated_price1 = ret.p1;
        self.accuracy = ret.a;
        if(ret.rent) {
          // hist.result.rent = ret.rent;
          self.estimated_rent_price = ret.rent.p;
          self.rental_dist_range = ret.rent.range;
          self.rental_last = ret.rent.last;
          self.accuracy = self.accuracy || ret.rent.a;
        }
        self.dist_range = ret.range;
        self.last = ret.last;
        self.changeSelectProp = false;
        self.incl = ret.incl||[];
        self.initPropListImg(self.incl);
        self.excl = ret.excl||[];
        self.initPropListImg(self.excl);
        self.rentalIncl  = ret.rentalIncl;
        self.initPropListImg(self.rentalIncl);
        self.rentalExcl =  ret.rentalExcl;
        self.initPropListImg(self.rentalExcl);

        self.max_dist_range = ret.max_dist_range
        var addrObj = {
          lat: self.prop.lat,
          lng: self.prop.lng,
          cnty: self.prop.cnty,
          prov: self.prop.prov,
          city: self.prop.city,
          cmty: self.prop.cmty,
          st: self.prop.st,
          st_num: self.prop.st_num || '',
          addr: self.prop.addr || ((self.prop.st_num || '') + " " + self.prop.st)
        };

        // window.bus.$emit('add-addr-hist',addrObj);
        // window.bus.$emit('add-hist', hist);
        if (self.estimated_price) {
          self.selectSaleOrRent('sale')
        } else if (self.estimated_rent_price) {
          self.selectSaleOrRent('rent');
        }
        document.getElementById('prop').scrollIntoView();
      }
      var failed = (ret)=>{
        self.loading = false;
        ajaxError(ret);
        //show error to user will not help. just log. Next try may work.
      }

      var params = Object.assign({}, this.prop);
      if (vars.ids)
        params.ids = vars.ids.split(',');
      if (vars.rentalids)
        params.rentalids = vars.rentalids.split(',');
      self.$http.post('/1.5/evaluation', params).then(function(ret){
        setTimeout(success(ret),5000);
      },failed);
    },
    showRef:function(type) {
      if (type=='sale') {
        return this.incl.length || this.inclIds.length || this.excl.length || this.exclIds.length;
      } else if (type=='rental') {
        return  this.rentalIncl.length||this.rentalExcl.length || this.rentalInclIds.length ||this.rentalExclIds.length;
      } else {
        return this.incl.length || this.rentalIncl.length|| this.excl.length || this.rentalExcl.length || this.inclIds.length || this.exclIds.length || this.rentalInclIds.length ||this.rentalExclIds.length;
      }
    },

    addOwner() {
      var self = this;
      self.$http.post('/1.5/evaluation/addOwner', {uaddr: self.uaddr, uid: self.dispVar.sessionUser._id}).then(function(ret){
        if(ret.data.ok) {
          self.showOwner = false;
        }
      },function(ret){
        ajaxError(ret);
      });
    },

    showSMB(){
      RMSrv.share('show');
      this.showmask = false;
    },
    rmShare(tp, doc){
      RMSrv.share(tp, doc);
    },

    currentSelect(type) {
      if (this.saleOrRent=='sale' && type == 'used' ) {
        return 'saleused';
      } else if (this.saleOrRent=='sale' && type == 'unused') {
        return 'saleunused';
      } else if (this.saleOrRent=='rent' && type == 'used') {
        return 'rentused'
      } else if (this.saleOrRent=='rent' && type == 'unused') {
        return 'rentunused'
      }
    },
    selectTab(type) {
      this.currentTab = type;
      if (type=='used' && !this.fromHist && !this.share)
        this.iconType = 'remove';
      if (type=='unused' && !this.fromHist && !this.share)
        this.iconType = 'add'
      var self = this;
      if (this.currentSelect(type) =='saleused') {
        self.currentList = self.incl;
      } else if (this.currentSelect(type) =='saleunused') {
        self.currentList = self.excl;
      } else  if (this.currentSelect(type) =='rentused'){
        self.currentList = self.rentalIncl;
      } else if (this.currentSelect(type) =='rentunused') {
        self.currentList = self.rentalExcl;
      }
    },
    openPropDetail() {
      if (!this.prop.mlsid || vars.fromMls)
        return;
      if(!this.prop._id) {
        this.prop._id = this.prop.mlsid
      }
      this.openPropPage(this.prop);
    },

  }
}

</script>


<!-- Add "scoped" attribute to limit CSS to this component only -->
<style lang="scss" scoped>
@import "/css/sprite.min.css";
.err {
  position: absolute;
  left: 0;
  right: 0;
  font-size: 15px;
  text-align: center;
  overflow: hidden;
  top: 40%;
  height: 200px;
  padding-top: 100px;
  margin-left: -32px;
}
.result-mask {
  background-size: 100% 100%!important;
}
.condition .suffix{
  font-size:10px;
  font-weight:400;
  padding-left:3px;
}
#evaluation .mask {
  opacity: 0.3;
  z-index: 0;
}
.result-range {
  display: flex;
  padding: 10px 0px 20px 0px;
  align-items: center;
  justify-content: space-between;
}
.result-range .min, .result-range .max {
  font-size: 12px;
  color: #666;
}
.result-range .min .k, .result-range .max .k {
  font-size: 10px;
  font-weight: 400;
}
.result-range .min {
  text-align: right;
}
.result-range .max {
  text-align: left;
}
.result-range .mid {
  width: 100%;
  height: 22px;
  position: relative;
}

.result-range .line-wrapper {
  padding: 0px 10px;
}
#accuracy .line {
  background-color: #ccc;
  display: block;
  height: 1px;
  position: relative;
  width: 100%;
}
#accuracy .line span {
  position: absolute;
  background: red;
  color: white;
  padding: 3px 10px;
}
.result-range .txt {
  font-size: 17px;
  font-weight: 500;
  color:black;
}
.result-range .txt .dollar {
  font-size: 10px;
  vertical-align: top;
}
.result-range .mid .txt {
  font-size: 12px;
  left:calc(50% - 20px);
  background: url('/img/mapmarkers/price-old.png');
  color: white;
  padding: 1px 5px 3px 5px;
  position: absolute;
  top: -25px;
  font-weight: 400;
  background-size: 100% 100%;
}

.start-evaluation {
  padding: 30px 10px;
}
.start-evaluation > div{
  /* padding: 10px; */
}
.start-evaluation .btn {
  padding: 15px 15px;
  background: #5cb85c;
  color: #fff;
  font-size: 14px;
  border: none;
  margin-top: 20px;
  margin-bottom: 10px;
}
.comparables .header .active{
  color: #5cb85c!important;
}
.comparables .header{
  border-bottom: none !important;
}
.comparables .header span:not(:first-child) {
  font-size: 14px;
}

.content {
  height: 100%;
  background: #f1f1f1;
  padding-bottom: 50px;
}
.padding44 {
  padding-top: 44px;
}
.see-all  {
  font-weight: 400;
  line-height: 15px;
  padding: 15px 10px 20px !important;
}
.re-evaluate {
  padding-top: 10px;
  font-size: 14px;
}

.re-evaluate .btn, .report-outter-wrapper .btn {
  background: #5cb85c;
  color: #fff;
  font-size: 15px;
  padding: 13px 8px 14px;
  border: none;
  border-radius: 0;
  width: 100%;
  font-weight: 700;
}
.report-outter-wrapper {
  background: #f9f9f9;
  padding: 10px 10px;
}
.report-wrapper {
  display: flex;
  margin-top:10px;
  font-size: 14px;
}
.report-wrapper >div:first-of-type{
  width:30px;
  padding:10px;
  color: #e03131;
  float: left;
  margin-top: -10px;
  padding-left: 0px;
}
.report-wrapper .txt {
  color: #666;
  font-size: 14px;
}
.report-wrapper .report{
  text-decoration: underline;
  padding-left: 10px;
}
.block .condition {
  margin: 10px;
}
.block .condition >div:first-of-type{
  display:inline-block;
  color: #666;
  width: 100%;
  background:#f9f9f9;
}
.block .condition .items {
  padding: 10px;
  width: 33.3%;
  float: left;
  color: #999;
}
.block .condition .items .val {
  color:black;
  font-size: 17px;
  font-weight: 500;
  padding-top: 2px;
}

.block .condition .re-evaluate {
  display: flex;
  align-items: center;
}
.block .condition .accuracy {
  text-transform: capitalize;
}
.bdrms .text {
  padding-right: 5px;
}
.share-text{
  padding: 20px 10px;
}
.share-text .btn {
  border-radius: 10px;
  border: 3px solid #fff;
  box-shadow: 0px 2px 2px 2px #e5e5e5;
  height: 30px;
  padding: 6px 0px;
  color:#007aff;
}
.share-text .btn:nth-of-type(2) ::before {
  content: "";
  float: left;
  display: inline-block;
  height: 20px;
  border-left: 2px solid #cfcfcf;
  margin-left: -14%;
  margin-top: -4px;
}
/* always present */
.wrapper{
  min-height: 68px;
  display: flex;
  padding: 0px 5px;
  align-items: center;
}
.wrapper input{
  margin: 0;
  width: 65%;
  height: 29px;
  border: none;
  text-align: right;
}

.mask {
  background: rgba(0,0,0,0.8);
  opacity: 0.3;
  z-index: 9;
}
.loader-wrapper{
  padding: 10px;
  background-color: rgba(0,0,0,.68);
  border-radius: 7px;
  height: 70px;
  width: 70px;
  z-index: 20;
  position: fixed;
  margin-left: -35px;
  margin-top: -35px;
  top: 50%;
  left: 50%;
  display: block;
  stroke: #fff;
  fill: #444;
}
.loader {
  /*margin: 60px auto;*/
  font-size: 10px;
  position: relative;
  text-indent: -9999em;
  border-top: 0.8em solid rgba(255, 255, 255, 0.2);
  border-right: 0.8em solid rgba(255, 255, 255, 0.2);
  border-bottom: 0.8em solid rgba(255, 255, 255, 0.2);
  border-left:0.81em solid #ffffff;
  -webkit-transform: translateZ(0);
  -ms-transform: translateZ(0);
  transform: translateZ(0);
  -webkit-animation: load8 1.1s infinite linear;
  animation: load8 1.1s infinite linear;
}
.loader,
.loader:after {
  border-radius: 50%;
  width: 50px;
  height: 50px;
}
@-webkit-keyframes load8 {
  0% {
    -webkit-transform: rotate(0deg);
    transform: rotate(0deg);
  }
  100% {
    -webkit-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}
@keyframes load8 {
  0% {
    -webkit-transform: rotate(0deg);
    transform: rotate(0deg);
  }
  100% {
    -webkit-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}
.calc {
  color: #fff;
  margin-top: -10px;
  text-align: center;
  margin-left: -30px;
  width: 60px;
  z-index: 30;
  position: fixed;
  left: 50%;
  top: 50%;
  font-size: 9px;
}
.notop {
  top:0px!important;
}
.fullLength {
  width:100%!important;
}
#accuracy .accuracy-wrapper {
  padding: 20px 0px;
}
#accuracy .accuracy{
  color:red;
  font-weight: bold;
  text-transform: uppercase;
  padding-left: 10px;
}

#accuracy {
  background: white;
  padding:10px;
}

.download-button {
  margin: 30px 20px;
  padding: 10px 0;
  text-align: center;
  font-size: 16px;
  color: #fff!important;
  text-decoration: none;
  border-radius: 4px;
  background-color: #42c02e;
  height: 40px;
  display: block;
}
.estimated_price1 {
  /* background: #7FC580; */
  display: flex;
  align-items: center;
  padding:10px;
}
.estimated_price1 .vip-only {
  font-size:14px;
  float: right;
  margin-left: auto;
}
.estimated_price1 .vip-only .icon {
  font-size: 17px;
}
.estimated_price1 .small {
  width:50%;
  font-size: 15px;
  line-height: 17px;
}
.estimated_price1 .price {
  font-size: 17px;
  display: flex;
  align-items: center;
}
.estimated_price1 .vip{
  background: #e03131;
  color: #fff;
  font-style: normal;
  padding: 1px 3px;
  border-radius: 2px;
  font-size: 9px;
  text-align: center;
  /* padding: 0px 3px; */
  margin-left: 10px;
  display: inline;
  line-height: 16px;
}
/* .tab .num, .estimated_price1 .num, .adjust{
  background: #e03131;
  border-radius: 10px;
  font-size: 12px;
  padding: 2px 10px!important;
  vertical-align: bottom;
  color: white;
}
.tab .num, .estimated_price1 .num{
  margin-left: 10px;
} */
#shareDialog #share-content{
  bottom: 0px !important;
}
#accuracy {
  position: relative;
}
#accuracy hr {
  border-top: 1px solid #cecece;
  margin: 0;
}
/* #accuracy > div {
  position: absolute;top: 2px;left: 50%;
} */
.image {
  padding-right: 5px;
  float: left;
}
.image img {
  width: 60px;
  height: 60px;
}
#prop {
  position: relative;
  padding: 10px 15px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  padding-top: 10px;
}
#prop .tracking {
  margin-left: auto;
}
#prop .tracking .btn{
  background: transparent;
  color: white;
  border: 1px solid white;
  font-size: 14px;
  padding: 9px 8px 10px;
}

.result-value {
  display: flex;
  align-items: center;
  justify-content: center;
}
.result-wrapper {
  color: white;
  padding:10px;
}

.label {
  display: inline-block;
  width: 30%;
  font-weight: normal;
  font-size: 16px;
}
.label > div{
}
.label .val{
  color:#666;
  font-size: 12px;
}
.range{
  display: inline-block;
  width: 65%;
  vertical-align: top;
  margin-top: 13px;
  padding-right: 20px;
  padding-top: 8px;
}
.wrapper{
  min-height: 68px;
  display: flex;
  padding: 0px 5px;
  align-items: center;
}
.wrapper:first-of-type {
  border-bottom: 1px solid #eaeaea;
}
#owner-of-property {
  /* height: 55px; */
  background:#f0f0f0!important;
  width: 100%;
  border-bottom: 1px solid #cbcbcb;
  padding-left: 0px!important;
  padding-right: 0px!important;
}
#owner-of-property div {
  height: 47px;
  background: #5e5e5e!important;
  width: 100%;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  padding: 0;
  border-bottom: 1px solid #cbcbcb;
}
#owner-of-property .icon {
  font-size: 21px;
  padding: 10px;
}
#owner-of-property span {
  color: white;
  font-size: 13px;
  padding-right: 10px;
}
#owner-of-property .btn {
  background: white;
  color: #a1a1a1;
  height: 24px;
  width: 130px;
  border: none;
  float: right;
  position: absolute;
  right: 10px;
}
#help {
  z-index: 10000;
  background: white;
  width: 100%;
  height: calc(100% - 44px);
  position: absolute;
  top:44px;
}
.fa-question-circle {
  width: 13px;
  font-size: 15px;
  margin-top: 0px;
  padding-left: 3px;
  color: #e13232;
  vertical-align: top;
  padding-top: 3px;
  display: inline-block;
}
.refer label {
  padding-left: 10px;
}
.share-wrapper {
  padding: 10px 5px 10px 5px;
  background: #e5e5e5 !important;
}
.share{
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100px;
}
.share > div{
  display: inline-block;
  width: 20%;
  overflow: hidden;
  vertical-align: top;
  font-size: 12px;
  text-align: center;
  line-height: 11px;
}
.share img{
  width: 100%;
  padding: 7px 11px 7px 11px;
  height: auto;
  max-width: 100px;
}
.address{
  color: white;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  width: 100%;
  font-size: 17px;
  font-weight: 500;
}

.bdrms span{
  padding-right: 3px;
}
.bdrms {
  padding-top:3px;
  font-size: 11px;
  color: white;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
h4 {
  margin-bottom: 0px;
  padding: 10px;
}

h4 span:not:first-child {
  padding:0px 5px;
}
h4 .fa{
  padding-left: 10px;
}

#result, .estimated_price1 {
  position: relative;
}
#result >div {
  width: 50%;
}
#result >div:first-of-type {
  padding-left: 15px;
}
#result .price{
  font-size: 22px;
  font-weight: 500;
}
.small {
  font-size: 12px;
}
#result {
  padding-top: 25px;
  padding-bottom: 15px;
  display: flex;
  align-items: center;
}
.bar-footer {
  vertical-align: middle;
  color: white;
  /* background-color: #efefef; */
  padding-left: 0;
  padding-right: 3px;
  z-index: 1;
  height: 50px;
  border-top: none;
}
.bar-footer .fa {
  color: #e03131;
  top: 3px;
  width: 24px;
  height: 24px;
  padding-top: 0;
  padding-bottom: 0;
  display: block;
  margin: auto;
  font-size: 21px;
}
.bar-footer a {
  text-align: center;
  vertical-align: middle;
  height: 50px;
  cursor: pointer;
  width: 12.5%;
  padding: 4px 0;
}
/* .bar-footer .tab-label {
  display: block;
  font-size: 10px;
  color: #666;
  margin-top: -3px;
  margin-left: -1px;
} */
/* 
.bar-footer .tab-label {
  display: block;
  font-size: 10px;
  color: #666;
}
.bar-footer a.pull-right {
  padding: 8px 7px 0;
} */

#help .bar-footer {
  background-color: #E03131!important;
  text-align: center;
  vertical-align: middle;
  line-height: 44px;
}
.bar-footer span.pull-left {
    height: 100%;
    width: 50%;
    display: -webkit-flex;
    display: flex;
}
.bar-footer span.pull-left .btn-segment {
  top: 0;
  width: 100%;
  color: white;
}
.bar.bar-standard.bar-footer .btn.btn-segment.brkg .brkg-wrapper{
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  margin: auto;
  left: 0;
  right: 0;
}
.bar-footer span.pull-left .btn-segment.brkg span {
  color: white;
  vertical-align: top;
  /* width: calc(100% - 55px); */
  display: inline-block;
  vertical-align: middle;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  font-size: 17px;
  line-height: 20px;
}
.bar-footer a span.sprite16-21{
  top: 3px;
  width: 21px;
  height: 21px;
  padding-top: 0;
  padding-bottom: 0;
  display: block;
  margin: auto;
  font-size: 21px;
  position: relative;
  z-index: 20;
}
.bar-footer a span.tab-label{
  display: block;
  font-size: 10px;
  color: #666;
}
</style>
