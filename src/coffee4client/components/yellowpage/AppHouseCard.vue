<template>
  <div class="prop">
    <div class="detail">
      <div class="addr one-line">
        <span v-show="prop.addr">{{prop.addr}}</span>
        <span v-show="!prop.addr">{{prop.city}} {{prop.prov}} {{prop.zip}}</span>
      </div>
      <div class="prov" v-show="prop.addr">{{prop.city}}, {{prop.prov}}</div>
      <div class="bdrms">
        <span v-show="prop.rmbdrm || prop.tbdrms || prop.bdrms"><span class="fa fa-rmbed"></span> {{prop.rmbdrm || prop.tbdrms || prop.bdrms}}</span>
        <span v-show="prop.rmbthrm || prop.tbthrms || prop.bthrms"><span class="fa fa-rmbath"></span> {{prop.rmbthrm || prop.tbthrms || prop.bthrms}}</span>
        <span v-show="prop.rmgr || prop.gr || prop.tgr"><span class="fa fa-rmcar"></span> {{prop.rmgr || prop.tgr || prop.gr}}</span>
        <!-- <span v-show="prop.ptp">{{prop.ptp}} {{prop.pstyl}}</span> -->
      </div>
      <div class="price">{{computedPrice}}</div>
      <div class="oh" v-show="prop.ohdate"><span class="fa fa-rmhistory"></span>{{_('Open House')}}: {{prop.ohdate}}</div>
    </div>
    <div :class="{oldVerBrowser:oldVerBrowser}" class="img">
      <img src="/img/noPic.png" :src="prop.thumbUrl || '/img/noPic.png'" onerror="hanndleImgUrlError(this)" referrerpolicy="same-origin"/>
      <div v-show="prop.type">{{prop.type}}</div>
    </div>
  </div>
</template>

<script>


export default {
  props: {
    prop: {
      type: Object,
      default: function () {
        return {
        }
      }
    }
  },
  components: {

  },
  computed:{

  },
  data () {
    return {
      oldVerBrowser:false,
      computedPrice:null,
    };
  },
  mounted () {
    if (!window.bus) {
      console.error('global bus is required!');
      return;
    }
    function getAndroidVersion(ua) {
      ua = (ua || navigator.userAgent).toLowerCase();
      var match = ua.match(/android\s([0-9\.]*)/);
      return match ? match[1] : false;
    }
    var self = this, bus = window.bus;
    if (getAndroidVersion() && parseFloat(getAndroidVersion()) < 4.4) {
      self.oldVerBrowser = true;
    }
    this.computedPrice = currencyFormat(this.prop.lp || this.prop.lpr, '$', 0);

  },
  methods: {
  }
}
</script>


<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped>
.prop{
  border-top: 1px solid #f1f1f1;
  padding: 7px 10px 4px 10px;
  display: -webkit-flex;
  display: flex;
  width: 100%;
  cursor: pointer;
}
.prop .img, .prop .detail{
  display: inline-block;
  /*display: table-cell;*/
}
.prop .img{
  width: 130px;
  /*display: table-cell;*/
  vertical-align: top;
  padding-top: 0px;
  position: relative;
}
.prop .img.oldVerBrowser{
  float: right;
}
.prop .detail{
  width: calc(100% - 130px);
  padding-right: 9px;
}
.prop img{
  width: 100%;
  height: 83px;
}
.prop .price{
  color: #e03131;
  padding-top: 3px;
  font-weight: 500;
}
.prop .oh{
  font-size: 12px;
  color: #ababab;
  padding-top: 5px;
}
.prop .oh .fa{
  font-size: 11px;
  padding-right: 5px;
  vertical-align: top;
  display: inline-block;
  padding-top: 4px;
}
.prop .addr {
  font-size: 15px;
}
.one-line {
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
}
.detail {
    width: calc(100% - 130px);
    padding-right: 9px;
}
.prop .prov {
    color: #777;
    font-size: 12px;
}
.prop .bdrms {
    padding: 2px 0 2px 0;
}
.prop .bdrms > span {
    background: #F4F7F9;
    border-radius: 1px;
    color: #839DB6;
    font-size: 12px;
    padding: 1px 4px;
}

</style>
