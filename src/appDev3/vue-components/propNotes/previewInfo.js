var previewInfo = {
  props:{
    prop:{
      type: Object,
      default:{}
    }
  },
  data(){
    return{
    }
  },
  computed:{
    soldOrLeased: function () {
      return (this.prop.saleTpTag_en == 'Sold')|| ['Sld','Lsd','Pnd','Cld'].includes(this.prop.lst);
    },
  },
  mounted(){
  },
  methods:{
    ImgUrl(){
      let img = 'url(' + this.prop.thumbUrl + '), url("/img/noPic.png")';
      return img;
    },
    formatPrice(price) {
      if (typeof price == "number") {
        return '$' + price.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
      } else {
        return price;
      }
    },
    openDetail(prop) {
      var id = (/^RM/.test(prop.id))?prop.id:prop._id;
      openPopup(`/1.5/prop/detail/inapp?id=${id}`,this.$_('RealMaster'));//lang=${this.lang}
    },
    closePreview(){
      bus.$emit("close-preview");
    },
    dotdate (d, isCh, split='.') {
      // console.log(d);
      if (!d) {
        return '';
      }
      if ('number' == typeof d) {
        d += '';
        d = d.slice(0,4)+'/'+d.slice(4,6)+'/'+d.slice(6,8)
      }
      //not supported by ios
      // var isIOS = /iPad|iPhone|iPod|Safari/.test(navigator.userAgent) && !window.MSStream;
      if ('string' == typeof d && !/\d+Z/.test(d)) {
        d += ' EST';
      }
      var split1 = isCh?'年':split;
      var split2 = isCh?'月':split;
      var split3 = isCh?'日':'';
      if (/^2\d{3}-\d{1,2}-\d{1,2}/.test(d) && !/\d+Z/.test(d)) {
        var d2 = d.split(' ')[0].split('-');
        return d2[0]+split1+d2[1]+split2+d2[2]+split3;
      }
      var t = new Date(d);
      if (!t || isNaN( t.getTime() )) {
        // console.log('isIos: '+isIOS+' ivalid date: '+d);
        return d;
      }
      return t.getFullYear() + split1 + (t.getMonth()+1) + split2 + t.getDate() + split3;
    }
  },
  template: `
  <div id="previewInfo" @click="openDetail(prop)">
    <div class="viewImg" :style="{'background-image': ImgUrl()}"></div>
    <div class="viewHead" @click.stop="closePreview()">
      <div class="viewHasOh pull-left" v-if="prop.hasOh"><span class="fa fa-rmhistory timeIcon"></span>{{$_('Open House')}}</div>
      <div class="bgHead  pull-right"><span class="icon icon-close"></span></div>
    </div>
    <div class="bottomInfo">
      <div class="propDisplay">
        <div>
          <span class="viewPrice" v-if="prop.sp && prop.priceValStrRed">{{formatPrice(prop.sp)}}</span>
          <span class="viewPrice" v-show="prop.lp || prop.lpr" :class="{'through':soldOrLeased}">{{formatPrice(prop.lp || prop.lpr)}}</span>
        </div>
        <div class="viewBdrms">
          <span class="viewRmbed" v-show="prop.bdrms">
            <span class="fa fa-rmbed"></span>
            <span class="viewBold">{{prop.bdrms}} {{prop.br_plus?'+ '+prop.br_plus:''}}</span>
          </span>
          <span v-show="prop.rmbthrm || prop.tbthrms || prop.bthrms">
            <span class="fa fa-rmbath"></span>
            <span class="viewBold">{{prop.rmbthrm || prop.tbthrms|| prop.bthrms}}</span>
          </span>
          <span v-show="prop.rmgr || prop.tgr || prop.gr">
            <span class="fa fa-rmcar"></span>
            <span class="viewBold">{{prop.rmgr || prop.tgr || prop.gr}}</span>
          </span>
        </div>
      </div>
      <div class="propDisplay">
        <div>
          <span class="viewTypeTag" v-if="prop.saleTpTag">{{prop.saleTpTag}}</span>
          <span v-if="prop.spcts||prop.mt||prop.ts">&nbsp;({{dotdate(prop.spcts||prop.mt||prop.ts)}})</span>
          <span v-show="prop.ltp == 'exlisting'">{{$_('Exclusive')}}</span>
          <span v-show="prop.ltp == 'assignment'">{{$_('Assignment')}}</span>
          <span v-show="prop.ltp == 'rent' && !prop.cmstn">{{$_('Landlord Rental')}}</span>
          <span v-show="prop.ltp == 'rent' && prop.cmstn">{{$_('Exclusive Rental')}}</span></span>
          <span class="viewDom" v-show="prop.dom != null"> &#183; {{$_('DOM','prop')}} {{prop.dom}}</span>
        </div>
      </div>
      <div class="propDisplay">
        <div class="viewTrim">
          <span class="addr" v-show="prop.addr">{{prop.unt?prop.unt:''}} {{prop.addr}} {{prop.apt_num}}</span>
          <span class="viewPtype"> &#183; {{prop.ptype2?prop.ptype2.join(' '):''}}</span>
        </div>
        <div class="viewSid pull-right" v-if="prop.sid">{{prop.sid}}</div>
      </div>
    </div>
  </div>
  `
}