{{&
var {prop, user, lang} = it;
}}
<style type="text/css">
body {
  font-size: 16px;
  font-family : Helvetica Neue;
}
.address {
  background-color: #666666;
  padding: 20px;
  text-align: center;
  color: #fff;
  margin-bottom: 2%;
}
.address h1 {
  font-size: 24px;
}
.address h3 {
  font-size: 20px;
}
.imgs {
  display: flex;
}
.img {
  display: block;
  width: 100%;
}
.left {
  flex:2;
  position: relative;
}
.saletype-wrapper {
  position: absolute;
  top: 0;
  right: 0;
  width: 0;
  height: 0;
  border-top: 6rem solid #3a3a3c;
  border-left: 6rem solid transparent;
}
.saletype {
  position: absolute;
  top: 20px;
  right: 10px;
  color: #fff;
  text-transform: uppercase;
  font-size: 1rem;
  font-weight: bold;
  transform: rotate(45deg);
}
.saletype.en {
  top: 28px;
  right: -3px;
}
.left .img {
  height: 100%;
}
.right {
  flex:1;
  margin-left: 2%;
}
.right .img {
  margin-bottom: 2%;
}
.right .img.last {
  margin-bottom: 0;
}
.props {
  display: flex;
  padding: 20px 0;
  justify-content: flex-end;
}
.icon {
  display: flex;
  align-items: center;
  margin-right: 10px;
}
.icon-img {
  width: 5vw;
}
.icon-txt {
  color: #2e3f50;
  font-weight: bold;
  text-transform: uppercase;
  font-size: 2.1vw;
  margin-left: 1vw;
  white-space: nowrap;
}
.realtor {
  position: relative;
  margin-bottom: 50px;
  background-color: #3a3a3c;
  color: #fff;
  padding: 20px;
  display: flex;
  justify-content: center;
  align-items: center;
}
.avt {
  position: absolute;
  left: 0;
  top: -1rem;
  width: 50px;
  height: 50px;
  border-radius: 50%;
}
.logo {
  width: 50%;
}
.split {
  width: 2px;
  height: 70px;
  background-color: #fff;
  margin: 0 20px;
}
.info {
  text-transform: uppercase;
}
.nm,.tel {
  font-size: 18px;
}
.role {
  font-size: 12px;
}
.qrcode {
  width: 88px;
  margin-top: 10px;
}
</style>
<div class="address">
  <h1>{{=prop.addr}}</h1>
  <h3>{{=prop.crsst}}</h3>
</div>
<div class="imgs">
  <div class="left">
    <img class="img" src="{{=prop.img1}}" referrerpolicy="same-origin" />
    <div class="saletype-wrapper"></div>
    <span class="saletype {{=lang}}">{{- for sale}}</span>
  </div>
  <div class="right">
    <img class="img" src="{{=prop.img2}}" referrerpolicy="same-origin" />
    <img class="img" src="{{=prop.img3}}" referrerpolicy="same-origin" />
    <img class="img last" src="{{=prop.img4}}" referrerpolicy="same-origin" />
  </div>
</div>
<div class="props">
  <div class="icon">
    <img class="icon-img" src="./ptype.png" referrerpolicy="same-origin" />
    <span class="icon-txt">{{=prop.ptype2}}</span>
  </div>
  <div class="icon">
    <img class="icon-img" src="./bdrms.png" referrerpolicy="same-origin" />
    <span class="icon-txt">{{=prop.rmbdrm||prop.bdrms||prop.tbdrms||''}} {{- bedrooms}}</span>
  </div>
  <div class="icon">
    <img class="icon-img" src="./bthrms.png" referrerpolicy="same-origin" />
    <span class="icon-txt">{{=prop.rmbthrm||prop.tbthrms||prop.bthrms||''}} {{- washrooms}}</span>
  </div>
  <div class="icon">
    <img class="icon-img" src="./sqft.png" referrerpolicy="same-origin" />
    <span class="icon-txt">{{=prop.sqft}} {{- sqft}}</span>
  </div>
</div>
<div class="realtor">
  <img src="{{=user.avt}}" class="avt" referrerpolicy="same-origin" />
  <img class="logo" src="./logo.png" referrerpolicy="same-origin" />
  <div class="split">|</div>
  <div class="info">
    <div class="nm">{{= user.nm || user.nm_en ||user.nm_zh }}</div>
    <div class="role">{{= user.cpny_pstn || 'sales representative'}}</div>
    <div class="tel">{{= user.tel}}</div>
    <img class="qrcode" src="./app-download.png" referrerpolicy="same-origin" />
  </div>
</div>