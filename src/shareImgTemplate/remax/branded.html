<style type="text/css">
img{
  max-width: 100%;
}
#contentWrapper{
  border:6px solid #e03131;
  /* padding: 10px; */
  margin-bottom: 55px;
}
::-webkit-scrollbar{
  display: none;
}
.price-mls{
  margin: 16px 0 -44vw 15px;
  position: relative;
  color: #e03131;
  font-weight: bold;
  font-size: 15px;
  width: 300px;
  z-index: 10;
  height: 40vw;
}
/*  -webkit-text-stroke: 1px white; */
.price-mls .lp{
  font-size: 24px;
  padding-bottom: 10px;
}
.price-mls .crsst{
  position: absolute;
  bottom: 9px;
  /* margin-top: 35px; */
}
.glow{
  text-shadow: 1px 1px 2px #e03131, 0 0 1em #e03131, 0 0 0.2em #ec3535;
  color: white;
}
.icon-wrapper{
  font-weight: bold;
  text-align: center;
  margin: 8px;
}
.icon-wrapper .icon{
  font-size: 12px;
}
.icon-wrapper .icon-desc{
  font-size: 16px;
  background: #e03131;
  color: white;
  padding-top: 23px;
}
.cpny-desc{
  background: #365FA9;
  font-size: 9px;
  color: white;
  text-align: center;
  margin: 0 8px;
  padding: 3px;
  font-weight: 600;
  line-height: 17px;
}
/* .house2, */
.info-row,
.icon-wrapper,
.bottom{
  -webkit-display:flex;
  display: flex;
}
.house.images{
  position: relative;
  padding: 8px 8px 0 8px;
}
.house1 {
  height: 44vw;
}
.house2{
  height: 30vw;
}
.house3{
  height: 20vw;
}
.house1 .left img{
  width: 100%;
  height: 100%;
}
.house1 .left{
  height: 100%;
  width: 65%;
}
.house1 .right{
  width: 34%;
  margin-left: 1%;
  position: relative;
  height: 44vw;
}
.house1 .right .img3{
  padding-top: 3px;
}
.house1 .right img{
  height: 100%;
  width: 100%;
}
.house1 .right .img-wrapper{
  height: 22vw;
  overflow: hidden;
}
/* .house1 .right, */
.house3 .img-wrapper,
.house2 .img-wrapper{
  width: 100%;
  position: absolute;
  /* top: 50%; */
  /* transform: translateY(-50%);
  -webkit-transform: translateY(-50%); */
  /* margin-top: -2vw; */
}
.house1,
.house3,
.house2{
  position: relative;
  overflow: hidden;
  font-size: 0;
  white-space: nowrap;
}
.house2,
.house3{
  margin-top: 7px;
}
.house2 img{
  width: 49.5%;
}
.house3 img{
  width: 32.66%;
}
.house3 .img4,
.house3 .img5{
  margin-left: 1%;
}
.house2 .img3{
  margin-left: 1%;
}
.house1 .left,
.house1 .right,
.house1 img,
.house3 img,
.house2 img,
.icon-wrapper > div,
.info-row > div,
.bottom > div{
  display: inline-block;
  vertical-align: top;
}
.bottom > div{
  width: 33.3%;
  transform: scale(0.893);
  /* overflow: hidden; */
}
.icon-wrapper .icon,
.info-row .houseinfo{
  width: 55%;
}
.icon-wrapper .icon-desc,
.info-row .profile{
  width: 45%;
}
.houseinfo .number{
  font-size: 15px;
  padding: 3px 0 0 3px;
  vertical-align: top;
  display: inline-block;
}
.houseinfo img{
  width: 25px;
  height: 25px;
}
.bottom-img{
  margin: 5px 8px 0px 8px;
  font-size: 0px;
  line-height: 0;
}
.bottom-img img{
  width: 100%;
}

.bottom{
  margin: 5px 8px 6px 8px;
  -webkit-text-size-adjust: none;
  font-size: 9px;
  background: #428bca;
  color: white;
  text-align: center;
  line-height: 16px;
}
.bottom .addr{
  white-space: nowrap;
  /* overflow: hidden; */
}
.houseinfo .h3{
  font-size: 18px;
  font-weight: bold;
  white-space: nowrap;
}
.houseinfo{
  font-size: 13px;
  padding: 10px 3px 0 8px;
}
.houseinfo .addr{
  font-size: 12px;
  color:#888;
}
.profile img{
  width: 40px;
  height: 40px;
  /* border-radius: 50%; */
}
.profile{
  padding: 10px 0 0 0;
  font-size: 14px;
}
.profile span,
.profile .detail{
  color: #888;
  font-size: 12px;
}
.power{
  font-size: 9px;
  text-align: center;
  color: #888;
  margin: 4px 0;
  line-height: 10px;
}
.power img{
  vertical-align: top;
  width: 10px;
  height: 10px;
}
.power span{
  vertical-align: top;
  display: inline-block;
  line-height: 10px;
}
.red{
  color:#e03131;
}
.remax{
  display: inline-block;
  margin-top: 0px;
  font-size: 11px;
  vertical-align: top;
}
.white{
  color:white;
}
</style>
<div id="contentWrapper">

<div class="icon-wrapper">
  <div class="icon">
    <img src="./icon.png" alt="" referrerpolicy="same-origin">
    <!-- <div class="website">
      www.remaxcentralbrokerage.com
    </div> -->
  </div>
  <div class="icon-desc">
    <div class="">靓盘推荐</div>
    <div class="">最新上市</div>
  </div>
</div>
<!-- <div class="bottom-img">
  <img src="./cpny_desc.png" alt="" referrerpolicy="same-origin">
</div> -->
<div class="cpny-desc">
世界著名地产品牌，您收房的保证，每年世界上没有哪家地产公司的销售额能超过
<span class="red remax">RE<span class="white">/</span>MAX</span>，
保证在最短的时间内为您卖出最好的价格！
<br/>
欲挂牌出售房屋?请速联系鸿运地产的经纪们！
</div>

<div class="house images">
  <div class="price-mls">
    <div class="lp shadow">{{=it.prop.FormatedLp}}</div>
    <div class="mls ">MLS#: {{=it.prop.sid}}</div>
    {{?it.prop.crsst}}
    <div class="crsst ">{{=it.prop.crsst}}</div>
    {{?}}
  </div>
  <div class="house1 img-wrapper">
    <div class="left">
      <img src="{{=it.prop.img0}}"  referrerpolicy="same-origin"></img>
    </div>
    <div class="right">
      <div class="img-wrapper">
        <img src="{{=it.prop.img1}}"  class="img2" referrerpolicy="same-origin"></img>
      </div>
      <div class="img-wrapper img2">
        <img src="{{=it.prop.img2}}"  class="img3" referrerpolicy="same-origin"></img>
      </div>
    </div>
  </div>
	 <div class="house2">
     <div class="img-wrapper">
       <img src="{{=it.prop.img3}}"  class="img2" referrerpolicy="same-origin"></img>
  	   <img src="{{=it.prop.img4}}"  class="img3" referrerpolicy="same-origin"></img>
     </div>
	 </div>
   <div class="house3 ">
     <div class="img-wrapper">
       <img src="{{=it.prop.img5}}"  class="img3" referrerpolicy="same-origin"></img>
       <img src="{{=it.prop.img6}}"  class="img4" referrerpolicy="same-origin"></img>
       <img src="{{=it.prop.img7}}"  class="img5" referrerpolicy="same-origin"></img>
     </div>
   </div>
</div>


<div class="info-row">
  <div class="houseinfo">
     <img src="./rooms icon-01.png" referrerpolicy="same-origin"></img>
  	 <span class="number">{{=it.prop.bdrms||''}}
     {{?it.prop.br_plus}}
       &nbsp;+&nbsp;{{=it.prop.br_plus}}
     {{?}}
     </span>
     <img src="./rooms icon-02.png" class="left" referrerpolicy="same-origin"></img>
  	 <span class="number">{{=it.prop.rmbthrm||it.prop.tbthrms||it.prop.bthrms||''}}</span>
     <img src="./rooms icon-03.png" class="left" referrerpolicy="same-origin"></img>
  	 <span class="number">{{=it.prop.rmgr || it.prop.tgr || it.prop.gr ||''}}</span>
     <div class="h3">{{=it.prop.addr||''}}</div>
     <div class="addr">{{=it.prop.city||''}}, {{=it.prop.prov||''}}, {{=it.prop.zip||''}}</div>
  	 <!-- <div class="QRcode">
  	    <img src="./app-download.png" width="115px" height="115px" referrerpolicy="same-origin"></img>
  	 </div> -->
  	 {{?it.prop.sqft}}
     <div>{{=it.prop.sqft}} {{-Sqft}}</div>
  	 {{?}}
  	 <!-- <div>ID: {{=it.prop.sid||''}} </div> -->
  	 <div>{{-Type}}:  {{=it.prop.ptype2||''}}</div>
  	 {{?it.prop.tax}}
  	 <div>{{-Tax}}: {{=it.prop.tax}}/{{=it.prop.taxyr||''}}</div>
  	 {{?}}
     <!-- <h2 style="display:inline-block">{{=it.prop.FormatedLp}}</h2> -->
     <!-- <span style="padding-left:10px; color: #E1302F">{{=it.prop.saletp||''}}</span> -->
  	 <!-- <div class="m">{{=it.prop.m}}</div> -->
  </div>

  <div class="profile">
     <img src="{{=it.user.avt}}"  referrerpolicy="same-origin"></img>
     <div class="h4">{{=it.user.fnm}}</div>
     <span>{{=it.user.cpny_pstn || ''}}</span>
     <div class="detail">{{=it.user.mbl}}</div>
     <!-- <div class="detail cpny">{{=it.user.cpny}}</div> -->
  	 <!-- <div class="split"></div> -->
  </div>
</div>
<div class="bottom-img">
  <img src="./remax_addr.png" alt="" referrerpolicy="same-origin">
</div>
<div class="power">
  <img src="./logo_s.png" alt="" referrerpolicy="same-origin">
  <span>{{-Powered by RealMaster}}</span>
</div>
<!-- <div class="bottom">
  <div class="">
    <div class="city">北约克</div>
    <div class="addr">#106-2175 Sheppard Ave E.</div>
    <div class="addr">Toronto, ON, M2J 1W8</div>
    <div class="">TEL: ************</div>
  </div>
  <div class="">
    <div class="city">万锦</div>
    <div class="addr">#513-3601 Highway 7 E.</div>
    <div class="addr">Markham, ON, L3R 0M3</div>
    <div class="">TEL: ************</div>
  </div>
  <div class="">
    <div class="city">密市</div>
    <div class="addr">#111-1140 Burhamthrope Rd. W.</div>
    <div class="addr">Mississauga, ON, L5C 4E9</div>
    <div class="">TEL: ************</div>
  </div>
</div> -->


</div>