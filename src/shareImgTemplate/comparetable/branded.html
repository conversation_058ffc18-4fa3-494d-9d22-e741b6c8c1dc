<link rel="stylesheet" type="text/css" href="/css/font-awesome.min.css">
{{&
  var FIELD = [{
    title: 'Address'},{
    title: 'Distance to listing#1',abbr:'dist'},{
    title: 'Listing Date',abbr: 'onD'},{
    title: 'DOM',abbr: 'dom'},{
    title: 'Occupancy Date',abbr: 'occupancy'},{
    title: 'Locker',abbr: 'lkr'},{
    title: 'List Price',abbr: 'lpValStrRed',diff:'difflp',diffStr:'difflpStr'},{
    title: 'Sold Price',abbr: 'spValStrRed',diff:'diffsp',diffStr:'diffspStr'},{
    title: 'Sold Date',abbr: 'sldd'},{
    title: 'Maint Fee',abbr: 'mfee'},{
    title: 'Property Tax',abbr: 'tax'},{
    title: 'Prop Type',abbr: 'ptype2'},{
    title: 'Bedrooms',abbr: 'rmbdrm',rabbr:'bdrms',brPlus:'br_plus',diff:'diffbdrms'},{
    title: 'Bathrooms',abbr: 'rmbthrm',rabbr:'bthrms',diff:'diffbthrms'},{
    title: 'Kitchen',abbr: 'kch',diff: 'diffkch'},{
    title: 'Parking'},{
    title: 'Included',abbr: 'xInclude'},{
    title: 'Size'},{
    title: 'Estimated Price',abbr:'pricePerSqft'},{
    title: 'Lot Size'},{
    title: 'Exposure',abbr: 'fce'},{
    title: 'Building Storeys',abbr: 'rmStoreys'},{
    title: 'Built Year',abbr: 'bltYrField'},{
    title: 'Basement',abbr: 'bsmt'},{
    title: 'Balcony',abbr: 'blcny'},{
    title: 'Remarks'
  }];
  var removeTitle = ['Maint Fee', 'Property Tax', 'Building Storeys','Basement','List Price','Locker'];
  var contentField = ['Sold Date','Exposure','Balcony','Distance to listing#1','Occupancy Date','Included','Listing Date'];
}}
<style type="text/css">
  #compareTable{
    overflow: auto;
    font-size: 16px;
    padding: 25px 20px 50px 20px;
  }
  table {
    border-collapse: collapse;
    table-layout:fixed
  }
  table,th,td {
    border: 1px solid black;
    padding: 6px 5px;
    white-space: normal;
    width: 219px;
  }
  #fontColor{
    color: #dd2524;
  }
  .textareaDiv {
    width: 89%;
    resize: none;
    border: none;
    color: #000;
    display: inline-block;
    word-wrap: break-word;
    white-space: normal;
  }
  .iconEdit::after{
    content: attr(icon);
    font-family: 'fa' !important;
    float: right;
    color: #808080;
    -webkit-font-smoothing: antialiased;
    width: 21px;
    height: 21px;
  }
  .powerImg{
    width:80px;
    height:80px;
    vertical-align: top;
  }
  [contenteditable]:focus{outline: none;}
  .diffDown{
    color: #e03131;
  }
  .diffUp{
    color: #048a04;
  }
  .headPic{
    height: 75px;
    width: 75px;
    border-radius: 50%;
    border: 1px solid white;
    vertical-align: top;
  }
  .iconPosition{
    display: inline-block;
    color: #b5b7b8;
    min-width: 20px;
    font-size: 13px;
    margin-right: 2px;
  }
  .agentName{
    padding-bottom: 5px;
    font-size: 15px;
    font-weight: bold;
  }
  #compareTable hr {
    display: block;
    height: 2px;
    border: 0;
    border-top: 1px solid #808080;
    margin: 0.8em 0;
    padding: 0;
  }
  #tableTitle{
    white-space: nowrap;
    margin-bottom: 25px;
  }
  .bottomText{
    margin-left: 5px;
    flex-direction: column;
    flex: 1;
  }
  .logoPic{
    vertical-align: middle;
    width: 25px;
    height: 25px;
  }
  .titleFont{
    vertical-align: middle;
    font-size: 22px;
    font-weight: bold;
    margin-left: 10px;
  }
  #agentInfo{
    margin: 0 5px;
    flex: 1;
    overflow: hidden;
  }
  .spaceWrap{
    white-space: normal;
  }
  .diffFont{
    font-size: 14px;
    color: #4f4d4d;
  }
  .cpny{
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    font-size: 13px;
    color: #777;
  }
  .tdHeadWidth{
    width: 172px;
  }
  #footer{
    display: flex;
    margin: 20px 0 10px 0;
    white-space: nowrap;
    max-width: 630px;
  }
  .flexWidth{
    flex: 50%;
  }
</style>
<div id="compareTable">
  <div id="tableTitle"><img src="./logo.jpeg" class="logoPic" referrerpolicy="same-origin"><span class="titleFont">{{-Comparative Market Analysis}}</span></div>
  <table id="tableInfo">
    <tr>
      <td class="tdHeadWidth">{{-Comparable listings}}</td>
      {{~it.props :item:index}}
        <td {{? index+1>1 && it.selModel == 'diff'}} id="fontColor"{{?}}>{{=index+1}}</td>
      {{~}}
    </tr>
    {{~FIELD :field}}
      <tr>
        {{? field.title == 'Maint Fee' && it.showMaint}}
        <td class="tdHeadWidth">{{- Maint Fee}} / {{- Month}}</td>
        {{?? field.title == 'Property Tax' && it.showMaint}}
        <td class="tdHeadWidth">{{='{{-'+field.title+'}}'}} {{? it.props.titleTaxyr>0}}/ {{=it.props.titleTaxyr}}{{?}}</td>
        {{?? field.title == 'Exposure'}}
        <td class="tdHeadWidth">{{-, exposure,propertylisting}}</td>
        {{?? field.title == 'List Price'}}
        <td class="tdHeadWidth">{{-, List Price,propertylisting}}</td>
        {{?? field.title == 'Basement' && it.showBsmt}}
        <td class="tdHeadWidth">{{='{{-'+field.title+'}}'}}</td>
        {{?? (field.title == 'Building Storeys' || field.title == 'Locker') && it.showStoreys}}
        <td class="tdHeadWidth">{{='{{-'+field.title+'}}'}}</td>
        {{?? !removeTitle.includes(field.title)}}
        <td class="tdHeadWidth">{{='{{-'+field.title+'}}'}}</td>
        {{?}}
        {{~it.props :item:index}}
          {{? field.title == 'Address'}}
          <td class="spaceWrap" {{? index+1>1 && it.selModel == 'diff'}} id="fontColor"{{?}}>
            {{? item.addr}}{{? item.unt}}{{=item.unt}}{{?}} {{=item.addr}}
            {{?? !item.addr && item.cmty!=undefined}}{{=item.cmty}}
            {{?}}{{? item.city}},{{=item.city}}{{?}}</td>
          {{?? field.title == 'DOM'}}
          <td {{? index+1>1 && it.selModel == 'diff'}} id="fontColor"{{?}}>{{? item[field.abbr]!=undefined}}{{=item[field.abbr]}}{{-Days}}{{?}}</td>
          {{?? field.title == 'List Price' || field.title == 'Sold Price'}}
          <td><div {{? index+1>1 && it.selModel == 'diff'}} id="fontColor"{{?}}>
            {{? field.title == 'List Price' && item[field.abbr]}}
              {{=item[field.abbr]}}
            {{?? field.title == 'Sold Price' && item[field.abbr]}}
              {{=item[field.abbr]}}
            {{?}}</div>
            {{? item[field.diff]!=undefined && item[field.diff]>0}}
              <div class="diffFont"><span class="fa fa-caret-up diffUp"></span>{{=item[field.diffStr]}}</div>
            {{?? item[field.diff]!=undefined && item[field.diff] < 0}}
              <div class="diffFont"><span class="fa fa-caret-down diffDown"></span>{{=item[field.diffStr]}}</div>
            {{?}}</td>
          {{?? (field.title == 'Maint Fee' || field.title == 'Property Tax') && it.showMaint}}
          <td {{? index+1>1 && it.selModel == 'diff'}} id="fontColor"{{?}}>{{? item[field.abbr]!=undefined}}${{=item[field.abbr]}}{{?}}</td>
          {{?? field.title == 'Bedrooms' || field.title == 'Bathrooms' || field.title == 'Kitchen'}}
          <td><span {{? index+1>1 && it.selModel == 'diff'}} id="fontColor"{{?}}>
            {{? item[field.abbr]!=undefined}}
              {{=item[field.abbr]}}
            {{?? item[field.rabbr]!=undefined}}
              {{=item[field.rabbr]}}
            {{?}}
            {{? field.title == 'Bedrooms' && item[field.abbr]==undefined && item[field.rabbr]!=undefined && item[field.brPlus]}}
            +{{=item[field.brPlus]}}
            {{?}}
            </span>
            {{? item[field.diff]!=undefined && item[field.diff] > 0}}
              <span class="diffFont">(<span class="fa fa-caret-up diffUp"></span>{{=Math.abs(item[field.diff])}})</span>
            {{?? item[field.diff]!=undefined && item[field.diff] < 0}}
              <span class="diffFont">(<span class="fa fa-caret-down diffDown"></span>{{=Math.abs(item[field.diff])}})</span>
            {{?}}
            {{? field.title == 'Bedrooms' && item.bedSize}}<span class="diffFont">{{= item.bedSize}}(m²)</span>{{?}}
          </td>
          {{?? field.title == 'Parking'}}
            <td {{? index+1>1 && it.selModel == 'diff'}} id="fontColor"{{?}}>{{? item.rmgr != undefined}}{{=item.rmgr}}{{?? item.tgr != undefined}}{{=item.tgr}}{{?}}</td>
          {{?? field.title == 'Size'}}
              <td class="iconEdit sizeClass" id="sizeID" style="vertical-align:top" icon='&#xf040'>
              <div class="textareaDiv sizeDiv" contenteditable="true" onblur="document.getElementsByClassName('sizeDiv')[0].style.paddingBottom = '0px'" onfocus="document.getElementsByClassName('sizeDiv')[0].style.paddingBottom = '200px'">
                {{? item.size!=undefined}}<span {{? index+1>1 && it.selModel == 'diff'}} id="fontColor"{{?}}>{{=item.size}}</span>{{?}}</div></td>
          {{?? field.title == 'Estimated Price'}}
            <td {{? index+1>1 && it.selModel == 'diff'}} id="fontColor"{{?}}>{{? item[field.abbr]}}${{=item[field.abbr]}}/{{- ft&sup2;}}{{?}}</td>
          {{?? field.title == 'Prop Type'}}
            <td {{? index+1>1 && it.selModel == 'diff'}} id="fontColor"{{?}}>{{? item[field.abbr]}}{{=item[field.abbr].join(', ')}}{{?}}</td>
          {{?? field.title == 'Lot Size'}}
            <td {{? index+1>1 && it.selModel == 'diff'}} id="fontColor"{{?}}>{{? item.front_ft!=undefined && item.depth!=undefined}}{{=item.front_ft}} * {{=item.depth}}{{? item.lotsz_code!=undefined}} {{=item.lotsz_code}}{{?}}{{?? item.lotsz}}{{= item.lotsz}}{{? item.lotszUt!=undefined}} {{=item.lotszUt}}{{?}}{{?? item.irreg}}{{= item.irreg}}{{?}}</td>
            {{?? field.title == 'Building Storeys' && it.showStoreys}}
            <td {{? index+1>1 && it.selModel == 'diff'}} id="fontColor"{{?}}>{{? item[field.abbr]!=undefined}}{{=item[field.abbr]}}({{-Estimated}}){{?}}</td>
          {{?? field.title == 'Built Year'}}
            <td class="iconEdit bltYrClass" style="vertical-align:top" icon='&#xf040'>
              <div class="textareaDiv bltyrDiv" contenteditable="true" onblur="document.getElementsByClassName('bltyrDiv')[0].style.paddingBottom = '0px'" onfocus="document.getElementsByClassName('bltyrDiv')[0].style.paddingBottom = '200px'">
                {{? item.bltYrField!=undefined}}<span {{? index+1>1 && it.selModel == 'diff'}} id="fontColor"{{?}}>{{=item.bltYrField}}</span>{{?}}</div></td>
          {{?? field.title == 'Basement' && it.showBsmt}}
            <td {{? index+1>1 && it.selModel == 'diff'}} id="fontColor"{{?}}>{{? item.bsmt}}{{=item.bsmt.join(', ')}}{{?}}</td>
          {{?? field.title == 'Locker' && it.showStoreys}}
            <td {{? index+1>1 && it.selModel == 'diff'}} id="fontColor"{{?}}>{{? item.lkr!=undefined || item.Locker!=undefined}}{{=item.lkr || item.Locker}}{{?}}</td>
          {{?? field.title == 'Remarks'}}
            <td {{? index+1>1 && it.selModel == 'diff'}} id="fontColor"{{?}} style="vertical-align:top" class="iconEdit remarkClass" icon='&#xf040'>
            <div class="textareaDiv remarkDiv" contenteditable="true" onblur="document.getElementsByClassName('remarkDiv')[0].style.paddingBottom = '0px'" onfocus="document.getElementsByClassName('remarkDiv')[0].style.paddingBottom = '200px'">
              {{? item.remark!=undefined}}<span {{? index+1>1 && it.selModel == 'diff'}} id="fontColor"{{?}}>{{=item.remark}}</span>{{?}}</div></td>
          {{?? contentField.includes(field.title)}}
            <td {{? index+1>1 && it.selModel == 'diff'}} id="fontColor"{{?}}>{{? item[field.abbr]!=undefined}}{{=item[field.abbr]}}{{?}}</td>
          {{?}}
        {{~}}
      </tr>
    {{~}}
  </table>
  <div id="footer">
    <div class="signature">
      {{? it.user.avt}}<img src="{{=it.user.avt}}" class="headPic" referrerpolicy="same-origin">
      {{?? it.user.wxavt}}<img src="{{=it.user.wxavt}}" class="headPic" referrerpolicy="same-origin">
      {{??}}<img src="./headPic.png" class="headPic"  referrerpolicy="same-origin">
      {{?}}
    </div>
    <div id="agentInfo" class="signature">
      {{? it.user.fnm}}
        <div class="agentName">{{=it.user.fnm}}</div>
      {{?}}
      {{? it.user.cpny_pstn}}
        <div class="cpny">{{=it.user.cpny_pstn}}</div>
      {{?}}
      {{? it.user.cpny}}
        <div class="cpny">{{=it.user.cpny}}</div>
      {{?}}
      {{? it.user.mbl}}
        <div class="cpny"><span class="iconPosition fa fa-phone"></span>{{=it.user.mbl}}</div>
      {{?}}
      {{? it.user.eml}}
        <div class="cpny"><span class="iconPosition fa fa-envelope"></span>{{=it.user.eml}}</div>
      {{?}}
    </div>
    <img src="./app-download.png" class="powerImg" referrerpolicy="same-origin">
    <div class="bottomText">
      <div class="agentName">{{-RealMaster}}</div>
      <div class="cpny" style="white-space: break-spaces;">{{-Scan QR-Code to download app}}</div>
    </div>
  </div>
</div>
