<style type="text/css">
#templateWrapper{
  padding: 10px;
}
.addr{
  color: white;
  background: linear-gradient(to bottom right, #e03131 , #ec632e);
  font-size: 10px;
  padding: 10px;
}
.addr .city{
  color: #ddd;
}
img{
  max-width: 100%;
}
.row{
  white-space: nowrap;
  display: flex;
}
.row > div{
  vertical-align:top;
  display: inline-block;
  margin-top: 10px;
}
.row > div.left{
  width: 70%;
}
.row > div.right{
  width: 30%;
  margin-left: 7px;
}
.houseinfo img{
  width: 10px;
  height: 10px;
}
.houseinfo .left{
  font-size: 10px;
  color: #777;
  background: #f1f1f1;
  padding: 10px 0 10px 10px;
}
.houseinfo .right{
  color: white;
  font-size: 11px;
  background: #E1302F;
  text-align: center;
  padding-top: 10px;
}
.houseinfo .type{
  padding-left: 7px;
}
.houseinfo .lp{
  font-size: 14px;
  padding-top: 10px;
}
.profile{
  text-align: center;
}
.profile img{
  width: 65px;
  height: 65px;
}
.profile .detail{
  display: block;
  font-size: 10px;
  color: #666;
  vertical-align: bottom;
  line-height: 14px;
  padding-bottom: 3px;
  max-height: 66px;
}
.profile .fnm{
  font-size: 13px;
  color: black;
  padding-bottom: 4px;
}
.profile .right{
  border-left: 2px solid #f1f1f1;
  margin-left: 0;
  padding-left: 7px;
}
.profile .right .detail{
  white-space: normal;
  overflow-y: hidden;
  font-size: 10px;
  line-height: 9px;
}
.profile .imgWrapper{
  text-align: center;
  padding-top: 10px;
}
.bottom{
  background: #f1f1f1;
  text-align: center;
  margin-top: 10px;
  padding: 10px;
  font-size: 13px;
  color: #777;
}
.powerImg{
  height: 20px;
  width: 20px;
}
.bottom > *{
  display: inline-block;
  vertical-align: top;
}
.bottom span{
  margin-top: 1px;
}
.houseImages .right .pic2{
  text-align: bottom;
  position: relative;
}
.split{
  margin-top: 10px;
  border-top: 3px solid #f1f1f1;
}
</style>

<div id="templateWrapper">
<div class="addr">
  <div class="h4">{{=it.prop.addr||''}}</div>
  <div class="city">{{=it.prop.city||''}}, {{=it.prop.prov||''}}, {{=it.prop.zip||''}}</div>
</div>
<div class="houseImages row">
  <div class="left">
    <img src="{{=it.prop.img0}}"   referrerpolicy="same-origin"></img>
  </div>
  <div class="right">
    <div class="">
      <img src="{{=it.prop.img1}}"  class="img2" referrerpolicy="same-origin"></img>
    </div>
    <div class="pic2">
      <img src="{{=it.prop.img2}}"  class="img3" referrerpolicy="same-origin"></img>
    </div>
  </div>
</div>
<div class="houseinfo row">
  <div class="left">
    <div class="">
      <img src="./rooms icon-01.png"   referrerpolicy="same-origin"></img>
      <span class="number">{{=it.prop.rmbdrm||it.prop.bdrms||it.prop.tbdrms||''}}</span>
      <img src="./rooms icon-02.png"   referrerpolicy="same-origin"></img>
      <span class="number">{{=it.prop.rmbthrm||it.prop.tbthrms||it.prop.bthrms||''}}</span>
      <img src="./rooms icon-03.png"   referrerpolicy="same-origin"></img>
      <span class="number">{{=it.prop.rmgr || it.prop.tgr || it.prop.gr ||''}}</span>
      {{?it.prop.sqft}}
      <span>{{=it.prop.sqft}} {{-Sqft}}</span>
      {{?}}
    </div>
    <div>ID: {{=it.prop.sid||''}}
      <span class="type">{{-Type}}:  {{=it.prop.ptype2||''}}</span>
    </div>
    {{?it.prop.tax}}
    <div>{{-Tax}}: {{=it.prop.tax}}/{{=it.prop.taxyr||''}}</div>
    {{?}}
  </div>
  <div class="right">
    <div class="lp">{{=it.prop.FormatedLp}}</div>
    <div>{{=it.prop.saleTpTag||''}}</div>
  </div>
</div>
<div class="split">

</div>
<div class="profile">
  <div class="imgWrapper">
    <img src="./app-download.png" width="115px" height="115px" referrerpolicy="same-origin"></img>
  </div>
</div>

<div class="bottom">
   <span>{{-Powered By RealMaster}}</span>
	 <img src="./icon.png"  class="powerImg" referrerpolicy="same-origin"></img>
</div>


</div>
