var LANGUAGE_OBJ=[{lang:"en",nm:"English"},{lang:"zh-cn",nm:"简体中文"},{lang:"zh",nm:"繁体中文"},{lang:"kr",nm:"한국어"}];function createLangTemplate(e){var t=`\n  <div style="z-index: 400;position: fixed;top: 0;right: 0;bottom: 0;left: 0;background-color: rgba(0, 0, 0, .8);">\n    <div class="language-box"\n      style="background: rgba(245,245,245,0.8);position: fixed;padding: 20px;border-radius: 15px;top: 50%;left: 50%;transform: translate(-50%, -50%);">\n      ${LANGUAGE_OBJ.map((t=>`\n        <div class="lang" style="padding: 10px 20px;border-radius: 5px;text-align: center;background: ${e==t.lang?"rgba(130,130,130,0.6)":"transparent"}" onClick="selectLang('${t.lang}')" onMouseOver="this.style.background='rgba(130,130,130,0.6)';" onMouseOut="this.style.background='transparent';">\n          ${t.nm}\n        </div>\n        `)).join("")}\n    </div>\n  </div>\n  `,s=document.createElement("div");s.innerHTML=t,s.id="langSelectBox",document.body.appendChild(s)}function closeLangTemplate(){var e=document.getElementById("langSelectBox");document.body.removeChild(e)}function selectLang(e){closeLangTemplate(),setLang(e)}var CitySelectModal={components:{flashMessage:flashMessage},mixins:[pageDataMixins],props:{needLoc:{type:Boolean,default:!1},showProvBar:{type:Boolean,default:!1},curCity:{type:Object,default:function(){return{o:"Toronto",n:"多伦多"}}},nobar:{type:Boolean,default:!1},showSubscribe:{type:Boolean,default:!0},lang:{type:String}},computed:{computedFavCities:function(){if(!this.filter)return this.favCities;return this.favCities.filter(this.filterFn)}},data:()=>({setCurCity:!1,filter:"",prov:"CA",loading:!1,provs:[],extCities:[],extCitiesCp:[],favCities:[],favCitiesCp:[],userCities:[],histCities:[],dispVar:{lang:"en"},datas:["lang"]}),mounted(){this.getProvs(),this.getUsercities();var e=window.bus,t=this;this.lang?this.dispVar.lang=this.lang:this.getPageData(this.datas,{},!0),e.$on("pagedata-retrieved",(function(e){t.dispVar=Object.assign(t.dispVar,e)})),window.bus?(e.$on("select-city",(function(e={}){if(t.doSearch=!!e.doSearch,t.favCities&&t.favCities.length)return toggleModal("citySelectModal");e.noloading||(t.loading=!0),fetchData("/1.5/props/cities.json",{body:{loc:t.needLoc}},(function(e,s){s.ok&&(t.favCities=t.parseCityList(s.fc),s.cl&&(t.extCitiesCp=s.cl.slice()),t.favCitiesCp=t.favCities.slice(),t.loading=!1,toggleModal("citySelectModal"))}));window.bus.$emit("track-log-event",{e:"open",type:"CitySelect"})})),localStorage.histCities&&(this.histCities=JSON.parse(localStorage.histCities))):console.error("global bus is required!")},watch:{filter:function(e,t){if(e&&e.length>0&&this.extCitiesCp.length){var s=this.extCitiesCp.filter(this.filterFn);this.extCities=this.formatCityList(s,{hide:!1})}else e&&0==e.length&&(this.extCities=this.formatCityList(this.extCitiesCp,{hide:!1}))}},methods:{getCityidx(e){if(!this.userCities||!this.userCities.length)return-1;return this.userCities.findIndex((function(t){return t.o==e.o}))},unSubscribeCity(e,t){t||(t=this.getCityidx(e)),trackEventOnGoogle("citySelectModal","unsubscribe");var s=this;fetchData("/1.5/index/subscribe",{body:{mode:"unsubscribe",city:e}},(function(e,i){console.log(i,e),i.ok?(s.userCities.splice(t,1),s.unSubscribe=!0,i.msg&&window.bus.$emit("flash-message",i.msg)):"Need login"==i.e?document.location.href="/1.5/user/login":window.bus.$emit("flash-message",i.e)}))},subscribeCity(e){trackEventOnGoogle("citySelectModal","subscribe");var t=this;fetchData("/1.5/index/subscribe",{body:{city:e}},(function(s,i){console.log(i,s),i.ok?(console.log("ok"),window.bus.$emit("flash-message",t.$_("Subscribed")),t.userCities.push(e)):i.e&&("Need login"==i.e?document.location.href="/1.5/user/login":window.bus.$emit("flash-message",i.e))}))},filterFn(e){var t=this.filter;if(t){var s=new RegExp(t,"ig");return s.test(e.o)||s.test(e.n)}},parseCityList(e){for(var t=[],s=0;s<e.length;s++){let i=e[s];i.split=!1,0==s&&t.push({split:!0,pn:i.pn,p:i.p,o:i.o,n:i.n}),t.push(i);let o=e[s+1]||{p:i.p,pn:i.pn};i.p!==o.p&&t.push({split:!0,pn:o.pn,p:o.p,o:o.o,n:o.n})}return t},closeCitySelect(){toggleModal("citySelectModal"),window.bus.$emit("city-select-close")},addCityHistory(e,t){var s=-1;return e.forEach(((e,i)=>{e.o==t.o&&(s=i)})),s>-1&&e.splice(s,1),delete t.n,delete t.pn,e.unshift(t),e.length>10&&(e.length=10),e},setCity(e){this.setCurCity&&(this.curCity=e),e.cnty||e.ncity||(e.cnty="Canada"),this.histCities=this.addCityHistory(this.histCities,e),localStorage.histCities=JSON.stringify(this.histCities),window.bus.$emit("set-city",{city:e,doSearch:this.doSearch})},getUsercities(){var e=this;fetchData("/1.5/index/userCities",{},(function(t,s){s.ok&&(e.userCities=s.cities)}))},getProvs(){var e=this;fetchData("/1.5/props/provs.json",{},(function(t,s){s.ok&&(e.provs=s.p)}))},changeProv(){if("CA"==this.prov)return this.favCities=this.favCitiesCp,void(this.extCities=[]);this.favCities=[],this.extCities=[],this.extCitiesCp=[],window.bus.$emit("clear-cache"),this.getCitiesFromProv()},formatCityList(e,t={}){for(var s=[],i={},o=0;o<e.length;o++){var n=e[o];if(!t.hide||"en"===this.dispVar.lang||n.o!=n.n){var a=n.o.charAt(0);i[a]||(i[a]=[]),i[a].push(n)}}for(let e of"ABCDEFGHIGKLMNOPQRSTUVWXYZ")i[e]&&s.push({i:e,l:i[e]});return s},getCitiesFromProv(e){e||(e=this.prov);var t=this;t.loading=!0,window.bus.$emit("clear-cache");var s={p:e,loc:t.needLoc};fetchData("/1.5/props/cities.json",{body:s},(function(e,s){s.ok&&(t.extCitiesCp=s.cl,t.extCities=t.formatCityList(s.cl,{hide:!0}),t.favCities=t.parseCityList(s.fc),t.loading=!1,window.bus.$emit("clear-cache"))}))}},template:'\n  <div class="modal" id="citySelectModal">\n  <header class="bar bar-nav" v-if="!nobar"><a class="icon icon-close pull-right" @click="closeCitySelect()" href="javascript:void 0"></a>\n    <h1 class="title">{{$_(\'Select City\')}}</h1>\n  </header>\n  <div class="content">\n    <div class="table-view-cell provbar" v-if="showProvBar"><span class="blue" @click="setCity({o:null,p:null,cnty:\'Canada\'})">{{$_(\'Canada\')}}</span><span class="blue" @click="setCity({o:null,p:null,cnty: \'United States\'})">{{$_(\'United States\')}}</span><span class="blue" @click="setCity({o:null,p:null,cnty: \'China\'})">{{$_(\'China\')}}</span>\n        <span\n            class="blue" v-for="p in provs" v-if="p.o_ab!=\'CA\'" @click="setCity({o:null, p:p.o, cnty: \'Canada\'})">{{p.n || p.o}}</span><span class="blue" @click="setCity({o:null,p:null,cnty:null, ncity: true})">{{$_(\'No City\')}}</span></div>\n    <div class="filter">\n        <div class="prov"><select v-model="prov" v-on:change="changeProv()"><option v-for="p in provs" v-bind:value="p.o_ab">{{p.n || p.o}}</option></select></div>\n        <div class="input"><span class="desc" v-show="prov == \'CA\'"><i class="fa fa-long-arrow-left"></i>{{$_(\'Select Province to see All Cities\')}}</span><input v-show="prov !== \'CA\'" type="text" v-model="filter" :placeholder="$_(\'Input City\')" /></div>\n    </div>\n    <ul class="table-view" v-show="curCity.o">\n        <li class="table-view-divider">{{$_(\'Current City\')}}</li>\n        <li class="table-view-cell"><a href="javascript:void 0" @click="setCity(curCity)">{{curCity.o}}<span v-show="curCity.o !== curCity.n" :class="{\'right-2\':showSubscribe, \'right\':!showSubscribe}">{{curCity.n}}</span></a></li>\n    </ul>\n    <ul class="table-view" v-show="histCities && histCities.length">\n        <li class="table-view-divider">{{$_(\'History\')}}</li>\n        <li class="table-view-cell" style="padding-right: 0px;padding-bottom:5px;"><span class="subscribed-city-tag" v-for="(city,index) in histCities"><span @click="setCity(city)">{{$_(city.o)}}</span></span>\n        </li>\n    </ul>\n    <ul class="table-view" v-show="showSubscribe && userCities && userCities.length">\n        <li class="table-view-divider">{{$_(\'Subscribed Cities\')}}</li>\n        <li class="table-view-cell" style="padding-right: 0px;padding-bottom:5px;"><span class="subscribed-city-tag" v-for="(city,index) in userCities"><span @click="setCity(city)">{{city.n}}</span><span class="icon icon-close" @click="unSubscribeCity(city,index)"></span></span>\n        </li>\n    </ul>\n    <ul class="table-view" v-show="favCities.length">\n        <li class="table-view-divider">{{$_(\'Popular Cities\')}}</li>\n        <li v-for="city in computedFavCities" :class="{\'table-view-cell\':!city.split, \'table-view-divider cust\':city.split}"><span v-if="city.split">{{city.pn}}</span><span v-if="!city.split"><a class="name" href="javascript:;" @click="setCity(city)">{{city.o}}<span v-show="city.o !== city.n" :class="{\'right-2\':showSubscribe, \'right\':!showSubscribe}">{{city.n}}</span></a>\n          <span v-if="showSubscribe">\n            <span class="right fa fa-bell" v-if="getCityidx(city)>=0" @click="unSubscribeCity(city)"></span>\n            <span class="right fa fa-bell-o" v-else @click="subscribeCity(city)"></span></span>\n          </span>\n        </li>\n    </ul>\n    <ul class="table-view" v-for="idx in extCities">\n        <li class="table-view-divider">{{idx.i}}</li>\n        <li class="table-view-cell" v-for="city in idx.l"><span><a class="name" href="javascript:;" @click="setCity(city)">{{city.o}}<span v-show="city.o !== city.n" :class="{\'right-2\':showSubscribe, \'right\':!showSubscribe}">{{city.n}}</span></a><span v-if="showSubscribe"><span class="right fa fa-bell" v-if="getCityidx(city)>=0" @click="unSubscribeCity(city)"></span>\n          <span class="right fa fa-bell-o" v-else @click="subscribeCity(city)"></span>\n              </span>\n            </span>\n        </li>\n    </ul>\n  </div>\n  <flash-message/>\n</div>\n  '},LangSelectCover={props:{oldVer:{type:Boolean,default:!1},dispVar:{type:Object,default:function(){return{userCity:{o:"Toronto"},languageObj:[]}}}},mounted(){var e=this;window.bus.$on("pagedata-retrieved",(function(t){t.lang&&(e.locale=t.lang);var s="";t.sessionUser&&t.sessionUser.defaultHomeTab&&(s=t.sessionUser.defaultHomeTab),e.defaultHomeTab=s||localStorage.defaultHomeTab||"new",t.coreVer&&(e.is63NewerVer=e.$parent.isNewerVer(t.coreVer,"6.3.1"),e.is65NewerVer=e.$parent.isNewerVer(t.coreVer,"6.4.0"),e.is63NewerVer&&RMSrv.onAppStateChange?RMSrv.onAppStateChange((t=>{e.doCheckSequence()})):e.canRefresh=!0,e.isAndroid&&e.is65NewerVer&&(e.steps=2))})),RMSrv.isIOS()&&(e.steps=2),e.isAndroid=RMSrv.isAndroid(),e.doCheckSequence(),e.showCover()},data:()=>({canContinue:!0,coreVer:"5.2.0",location:!1,locationState:"denied",notification:!1,notificationState:"denied",clickedLocation:0,clickedNotification:0,wordings:["To find nearby properties and schools you need to enable location.","To get latest messages and listing alerts you need to enable notification."],locale:"en",steps:3,curStep:0,homeTabs:[{k:"feed",v:"Feeds",ctx:"discover"},{k:"new",v:"New",ctx:"tag"}],canRefresh:!1,refresh:!1,confirmStep:0,locationInit:!1,notificationInit:!1,is63NewerVer:!1,is65NewerVer:!1,isAndroid:!1,isInitApp:!1}),methods:{showCover(){var e=this;/hideLang=1/.test(document.URL)||(/src=setting/.test(document.URL)&&(e.curStep=1,e.addToStorage("curStep",e.curStep),e.openCover()),e.getFromStorage("initApp",(t=>{var s=0;null==t?(e.readCookie("locale")?(s=1,e.addToStorage("notificationPermission",new Date)):e.isAndroid&&e.addToStorage("notificationPermission",new Date),e.addToStorage("initApp",s)):s=t,e.isInitApp=1!=s,e.getFromStorage("curStep",(t=>{e.isInitApp?(t>0?e.curStep=t:(e.curStep=1,e.addToStorage("curStep",e.curStep)),e.openCover()):null!=t&&t>0&&(e.curStep=t,e.openCover())}))})))},openCover(){setTimeout((function(){toggleModal("coverPageModal","open")}),0)},isAuthedRet:(e="")=>["authorized","granted"].indexOf(e)>-1,doCheckSequence(){var e=this;e.refresh=!1,RMSrv.onReady((()=>{e.checkPermissions("notification",(()=>{e.checkPermissions("location")}))}))},setStatus(e,t,s,i){this[e]=this.isAuthedRet(t),this[e+"State"]=t,"denied"==t&&(i=!0),this[e+"Init"]=!!i,s&&s()},checkPermissions(e,t){var s=this;RMSrv.permissions&&RMSrv.permissions(e,"check",-1,(function(i){if("denied"!=i)return s.setStatus(e,i,t);s.isAndroid&&"notification"==e&&(s.isInitApp?s.setStatus(e,i,t,"init"):s.setStatus(e,"block",t)),s.getFromStorage(e+"Permission",(o=>{null==o?s.setStatus(e,i,t,"init"):s.setStatus(e,i,t)}))}))},setCity(){this.addToStorage("curStep",this.curStep),this.$parent.getCityList("setUserCity")},blockAlert(e,t){var s=this;e=e||"To find nearby houses and schools you need to enable location";var i=this.$_?this.$_:this.$parent.$_;"function"!=typeof i&&(i=e=>e);var o=i(e),n=i("Skip"),a=i("Cancel"),r=r||"";return RMSrv.dialogConfirm(o,(function(e){e+""=="1"?s.next():"function"==typeof t&&t()}),r,[n,a])},confirmNext(e,t){var s=this,i=s.getWording(e,s[e+"State"]);i?s.blockAlert(i):t()},getWording(e,t){var s;return"location"==e?this.isAuthedRet(t)||(s=this.wordings[0]):"notification"==e&&(this.isAuthedRet(t)||(s=this.wordings[1])),s},setLang(e){localStorage.lang=e,this.locale=e,this.addToStorage("curStep",this.curStep),setLoaderVisibility("block");var t="/1.5/settings/lang?l="+e+"&d=";t+=encodeURIComponent("/1.5/index?src=setting"),window.location=t},confirmNextStep(e){var t=this;t.confirmStep=e;var s=2==e?"location":"notification";if(!t[s+"Init"])return t.showRefresh?bus.$emit("flash-message",t.$_(t.$parent.strings.refreshAlert.key)):t[s]?t.next(e):void t.confirmNext(s)},setHomeTab(e){this.defaultHomeTab!=e&&(this.defaultHomeTab=e,this.$forceUpdate(),this.dispVar.isLoggedIn?fetchData("/1.5/settings/user",{body:{defaultHomeTab:e}},(function(e,t){if(e||t.e)return bus.$emit("flash-message",e||t.e)})):localStorage.defaultHomeTab=e)},requestPermissions(e){var t=this;"denied"==t[e+"State"]?RMSrv.permissions&&RMSrv.permissions(e,"request",(function(s){t.addToStorage(e+"Permission",new Date),t.isAndroid&&"denied"==s&&(s="blocked"),t.setStatus(e,s)})):t.openAppSettings()},openAppSettings(){this.refresh=!0,RMSrv.openSettings&&RMSrv.openSettings()},addToStorage(e,t,s){localStorage[e]=t,RMSrv.setItemObj({key:e,value:t,stringify:!0,store:!0},s)},getFromStorage(e,t){var s=this;RMSrv.getItemObj(e,!0,(i=>{if(!i)return t(null);if(i.indexOf("null")>-1&&null==localStorage[e])return t(null);var o=i.match(/\d+/);if(asyncStatus=o?parseInt(o[0],10):null,localStatus=localStorage[e],asyncStatus==localStatus)return t(asyncStatus);t(s.is65NewerVer?asyncStatus:localStatus)}))},next(e){if((e=e||this.confirmStep)>=this.steps)return this.done();setLoaderVisibility("block"),setTimeout((()=>{this.curStep=e+1>this.steps?this.steps:e+1,this.addToStorage("curStep",this.curStep),setLoaderVisibility("none")}),500)},back(e){e>this.steps&&(e=e>=this.steps?this.steps:e),this.curStep=e-1>=1?e-1:1,this.addToStorage("curStep",this.curStep)},handleRedirect(e){var t=this;if("undefined"!=typeof localStorage&&e){localStorage.lastCoverHideDate=new Date,"/1.5/settings/lang?l=";var s="/1.5/settings/lang?l="+e+`&d=${encodeURIComponent("/1.5/user/login#index")}`;if(t.isInitApp){if(t.dispVar.homePageABTest)return t.sendTestInfo(s);window.location=s}else window.location="/1.5/index"}else toggleModal("coverPageModal")},done(){var e=this,t=this.locale;setLoaderVisibility("block"),e.setCookie("locale",t,365),e.addToStorage("curStep",0),e.addToStorage("initApp",1,(function(){setTimeout((()=>{e.handleRedirect(t)}),500)}))},sendTestInfo(e){var t="home";let s=getABTestSeed();this.setCookie(t+"seed",s),axios.put("/abtest",{groupName:t}).then((s=>{(s=s.data).ok&&(checkAndSendLogger(null,{sub:t,act:"abtest",tgp:"hp"+s.group}),"A"==s.group?window.location=e:window.location="/1.5/index")})).catch((e=>{console.error("server-error")}))}},computed:{showRefresh:function(){return this.canRefresh&&this.refresh}},template:'\n  <div>\n    <div style="display:none">\n      <span v-for="w in wordings">{{ $_(w)}}</span>\n    </div>\n    <div class="modal" id="coverPageModal">\n      <div class="content">\n        <header class="bar bar-nav" v-if=\'!isInitApp\'>\n          <a class="icon icon-close pull-right" @click="done()"></a>\n        </header>\n        <div class="title-wrapper">\n          <div class="tl">{{ $_("Setup")}}\n            <span class="desc gray">{{curStep}}/{{steps}}</span>\n          </div>\n        </div>\n        <div class="step-line-wapper">\n          <span v-for=\'i in steps\' :class="{active:i == curStep}"></span>\n        </div>\n        <div class="steps-wrapper">\n          <div class="page" v-show=\'curStep == 1\'>\n            <div class="cata lang">\n              <div class="tl">{{ $_("Language")}}</div>\n              <div class="desc" v-for="lang in dispVar.languageObj" @click="setLang(lang.k)">\n                {{lang.v}}\n                <span class="fa pull-right" :class=\'{"fa-check-circle":locale == lang.k,"fa-circle-thin":locale != lang.k}\'></span>\n              </div>\n            </div>\n            <div class="cata city">\n              <div class="tl">{{ $_("Default City")}}</div>\n              <div class="opts">\n                <div class="desc">\n                  <div>{{dispVar.userCity.n || dispVar.userCity.o}}</div>\n                </div>\n                <div class="switch"><span class="positive" @click="setCity()">{{ $_("Change","lang select")}}</span>\n                </div>\n              </div>\n            </div>\n            <div class="cata home">\n              <div class="tl">{{ $_("Default Homepage Tab")}}</div>\n              <div class="desc" v-for="tab in homeTabs" @click="setHomeTab(tab.k)">\n                {{ $_(tab.v,tab.ctx)}}\n                <span class="fa pull-right" :class=\'{"fa-check-circle":defaultHomeTab == tab.k,"fa-circle-thin":defaultHomeTab != tab.k}\'></span>\n              </div>\n            </div>\n            <div class="btn-box login-wrapper">\n              <span type="button" class="btn btn-block btn-positive full" @click=\'next(1)\'> {{ $_("Continue")}}</span>\n            </div>\n          </div>\n\n          <div class="page" v-show=\'(curStep == 2) && (steps == 3)\'>\n            <div class="cata location">\n              <div class="tl">{{ $_("Turn On Location Permission")}}\n              </div>\n              <div class="desc" v-if=\'showRefresh\' @click="doCheckSequence()">\n                {{ $_("Permissions status updated.")}}\n                <span class="positive pull-right refresh">{{ $_("Refresh")}}</span>\n              </div>\n              <div class="desc" @click="requestPermissions(\'location\')">\n                {{$_(\'ON\')}}\n                <span class="fa pull-right" v-show=\'!showRefresh\' :class=\'{"fa-check-circle":(location && !locationInit),"fa-circle-thin":((!location) || locationInit)}\'></span>\n              </div>\n              <div class="desc" @click="requestPermissions(\'location\')">\n                {{$_(\'OFF\')}}\n                <span class="fa pull-right" v-show=\'!showRefresh\' :class=\'{"fa-check-circle":(!location && !locationInit),"fa-circle-thin":(location || locationInit)}\'></span>\n              </div>\n              <div class="desc gray">{{ $_("We highly recommend you grant permission, as it will be used for a map-based property search.")}} {{ $_("We use GPS to zoom in/out on the user\'s current location and filter properties nearby.")}}</div>\n              <div class="desc gray">{{ $_("The use of your location data is subject to our privacy policy.")}}</div>\n            </div>\n            <div class="btn-box login-wrapper">\n              <span type="button" class="btn btn-block" @click=\'back(2)\'> {{ $_("Back")}}</span>\n              <span type="button" class="btn btn-block btn-positive" :class="{\'disabled\':locationInit}" @click=\'confirmNextStep(2)\'> {{ $_("Continue")}}</span>\n            </div>\n          </div>\n\n          <div class="page" v-show=\'curStep == steps\'>\n            <div class="cata notification">\n              <div class="tl">{{ $_("Turn On Notification Permission")}}\n              </div>\n              <div class="desc" v-if=\'showRefresh\' @click="doCheckSequence()">\n                {{ $_("Permissions status updated.")}}\n                <span class="positive pull-right refresh">{{ $_("Refresh")}}</span>\n              </div>\n              <div class="desc" @click="requestPermissions(\'notification\')">\n                {{$_(\'ON\')}}\n                <span class="fa pull-right" v-show=\'!showRefresh\' :class=\'{"fa-check-circle":(notification && !notificationInit),"fa-circle-thin":((!notification) || notificationInit)}\'></span>\n              </div>\n              <div class="desc" @click="requestPermissions(\'notification\')">\n                {{$_(\'OFF\')}}\n                <span class="fa pull-right" v-show=\'!showRefresh\' :class=\'{"fa-check-circle":(!notification && !notificationInit),"fa-circle-thin":(notification || notificationInit)}\'></span>\n              </div>\n              <div class="desc gray">{{ $_("Enable notification to get timely push notifications for property listings and relevant news.")}} {{ $_("Stay informed and never miss any new listings or status updates like price changes to your favorite listings.")}}</div>\n            </div>\n            <div class="btn-box login-wrapper">\n              <span type="button" class="btn btn-block" @click=\'back(3)\'> {{ $_("Back")}}</span>\n              <span type="button" class="btn btn-block btn-positive" :class="{\'disabled\':notificationInit}" @click=\'confirmNextStep(3)\'> {{ $_("Done")}}</span>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n  '},PageSpinner={props:{loading:{type:Boolean,default:!1}},data:()=>({}),template:'\n  <div class="overlay loader-wrapper" id="busy-icon" v-show="loading">\n    <div class="loader"></div>\n  </div>\n  '},rmsrvMixins={created:function(){"undefined"!=typeof RMSrv&&null!==RMSrv&&(this.ready=!0)},methods:{trackEventOnGoogle(e,t,s,i){trackEventOnGoogle(e,t,s,i)},exMap(e,t){let s;return this.dispVar.isApp||(document.location.href="/adPage/needAPP"),e.useMlatlng||"N"===e.daddr&&e.lat&&e.lng?s=e.lat+","+e.lng:(s=(e.city_en||e.city||"")+", "+(e.prov_en||e.prov||"")+", "+(e.cnty_en||e.cnty||""),s="N"!==e.daddr?(e.addr||"")+", "+s:s+", "+e.zip),t=(t||this.dispVar.exMapURL)+encodeURIComponent(s),RMSrv.showInBrowser(t)},goBack(){if("nativeMap"==vars.src||"nativeAutocomplete"==vars.src)return window.rmCall(":ctx::cancel");vars.d?document.location.href=vars.d:window.history.back()},sprintf(){var e=arguments,t=1;return e[0].replace(/%((%)|s|d)/g,(function(s){var i=null;if(s[2])i=s[2];else{if(i=e[t],"%d"===s)i=parseFloat(i),isNaN(i)&&(i=0);t++}return i}))},appendLocToUrl(e,t,s){if(null!=t.lat&&null!=t.lng){var i=e.indexOf("?")>0?"&":"?";return e+=i+"loc="+t.lat+","+t.lng}return e},appendCityToUrl(e,t,s={}){if(!t.o)return e;var i=e.indexOf("?")>0?"&":"?";return e+=i+"city="+t.o,t.p&&(e+="&prov="+t.p),t.n&&(e+="&cityName="+t.n),t.pn&&(e+="&provName="+t.pn),t.lat&&(e+="&lat="+t.lat),t.lng&&(e+="&lng="+t.lng),s.saletp&&(e+="&saletp="+s.saletp),null!=s.dom&&(e+="&dom="+s.dom),null!=s.oh&&(e+="&oh="+!0),s.ptype&&(e+="&ptype="+s.ptype),e},appendDomain(e){var t=window.location.href.split("/");return e=t[0]+"//"+t[2]+e},clickedAd(e,t,s,i){var o=e._id;if(e.inapp)return window.location=e.tgt;t&&trackEventOnGoogle(t,"clickPos"+s),o=this.appendDomain("/adJump/"+o),RMSrv.showInBrowser(o)},goTo(e,t={}){if(e.googleCat&&e.googleAction&&trackEventOnGoogle(e.googleCat,e.googleAction),e.t){let t=e.t;"For Rent"==e.t&&(t="Lease");var s=e.cat||"homeTopDrawer";trackEventOnGoogle(s,"open"+t)}var i=e.url,o=e.ipb,n=this;if(i){if(e.login&&!this.dispVar.isLoggedIn)return window.location="/1.5/user/login";if(!this.jumping){if(e.vipplus&&!this.dispVar.isVipPlus)return this.confirmVip();if(e.trebuser&&!this.dispVar.hasAid)return this.confirmTreb();if(e.t,"Agent"==e.t&&!e.direct)return this.dispVar.isLoggedIn?this.dispVar.isProdSales||this.dispVar.marketAdmin?window.location=e.url:null:window.location="/1.5/user/login";if("Services"==e.t)return window.location=i;if(1==o){return e.jumpUrl&&(i=e.jumpUrl+"?url="+encodeURIComponent(e.url)),this.tbrowser(i,{backButton:{image:"back",imagePressed:"back_pressed",align:"left",event:"backPressed"}})}if(3==o)return RMSrv.scanQR("/1.5/iframe?u=");if(4==o)return console.log(i),RMSrv.showInBrowser(i);if(1==e.loc){var a=this.dispVar.userCity;i=this.appendCityToUrl(i,a)}if(e.projQuery){var r=this.dispVar.projLastQuery||{};i+="?";for(let e of["city","prov","mode","tp1"])r[e]&&(i+=e+"="+r[e],i+="&"+e+"Name="+r[e+"Name"],i+="&")}if(1==e.gps){a=this.dispVar.userCity;i=this.appendLocToUrl(i,a)}1==e.loccmty&&(i=this.appendCityToUrl(i,t)),e.tpName&&(i+="&tpName="+this.$_(e.t,e.ctx)),this.jumping=!1,n.isNewerVer(n.dispVar.coreVer,"5.8.0")&&/mapSearch|autocomplete/.test(i)&&!/mode=list/.test(i)||(n.jumping=!0),setTimeout((function(){window.location=i}),10)}}},clearCache(){"undefined"!=typeof RMSrv&&null!==RMSrv&&RMSrv.clearCache&&RMSrv.clearCache()},confirmNoFnLn:function(e,t,s,i){t=t||"To be presented here, please complete your personal profile.";var o=this.$_?this.$_:this.$parent.$_,n=o(t),a=o("Later"),r=o("Do it Now");s=s||"";return RMSrv.dialogConfirm(n,(function(e){e+""=="2"?window.location="/1.5/settings/editProfile":i&&(window.location=i)}),s,[a,r])},confirmSettings:function(e,t){e=e||"To find nearby houses and schools you need to enable location";var s=this.$_?this.$_:this.$parent.$_;"function"!=typeof s&&(s=e=>e);var i=s(e),o=s("Later"),n=s("Go to settings"),a=a||"";return RMSrv.dialogConfirm(i,(function(e){e+""=="2"?RMSrv.openSettings():"function"==typeof t&&t()}),a,[o,n])},confirmNotAvailable(e){e=e||"According to the Real Estate Board notice, the sold price information is open by Oct. 22. We will keep you updated at earliest time possible.";var t=this.$_?this.$_:this.$parent.$_,s=t(e),i=t("I Know"),o=o||"";return RMSrv.dialogConfirm(s,(function(e){}),o,[i])},confirmUpgrade:function(e,t){t=t||"Only available in new version! Upgrade and get more advanced features.";var s=this.$_?this.$_:this.$parent.$_,i=s(t),o=s("Later"),n=s("Upgrade"),a=this.appendDomain("/app-download");return RMSrv.dialogConfirm(i,(function(t){e&&(a+="?lang="+e),t+""=="2"&&RMSrv.closeAndRedirect(a)}),"Upgrade",[o,n])},confirmVip:function(e,t){t=t||"Available only for Premium VIP user! Upgrade and get more advanced features.";var s=this.$_?this.$_:this.$parent.$_,i=s(t),o=s("Later"),n=s("See More");return RMSrv.dialogConfirm(i,(function(e){e+""=="2"&&RMSrv.showInBrowser("https://www.realmaster.ca/membership")}),"VIP",[o,n])},tbrowser:function(e,t={}){var s;s={toolbar:{height:44,color:"#E03131"},closeButton:{image:"close",align:"right",event:"closePressed"},fullscreen:!1},s=Object.assign(s,t),RMSrv.openTBrowser(e,s)}}};function time(e){return(e=new Date(e)).getMonth()+1+"/"+e.getDate()+" "+e.getHours()+":"+(e.getMinutes()<10?"0":"")+e.getMinutes()}function day(e){if(e)return(e=new Date(e)).getUTCDate()}function yearMonth(e){if(e)return(e=new Date(e)).getFullYear()+"."+(e.getUTCMonth()+1)}function number(e,t){return null!=e?(t=parseInt(t),isNaN(e)?0:parseFloat(e.toFixed(t))):e}function propPrice(e,t){return null!=e?(e=parseInt(e),isNaN(e)||0==e?"":(e<1e3?e+="":e=e<1e4?(e/1e3).toFixed(1)+"K":e<999500?Math.round(e/1e3).toFixed(0)+"K":(e/1e6).toFixed(t=t||1)+"M",e)):""}function percentage(e,t){return null!=e?(e=parseFloat(e),isNaN(e)?0:(100*e).toFixed(2)):e}function dotdate(e,t,s="."){if(!e)return"";"number"==typeof e&&(e=(e+="").slice(0,4)+"/"+e.slice(4,6)+"/"+e.slice(6,8)),"string"!=typeof e||/\d+Z/.test(e)||(e+=" EST");var i=t?"年":s,o=t?"月":s,n=t?"日":"";if(/^2\d{3}-\d{1,2}-\d{1,2}/.test(e)&&!/\d+Z/.test(e)){var a=e.split(" ")[0].split("-");return a[0]+i+a[1]+o+a[2]+n}var r=new Date(e);return!r||isNaN(r.getTime())?e:r.getFullYear()+i+(r.getMonth()+1)+o+r.getDate()+n}function datetime(e){return(e=new Date(e)).getMonth()+1+"/"+e.getDate()+"/"+e.getFullYear()+" "+e.getHours()+":"+(e.getMinutes()<10?"0":"")+e.getMinutes()}function monthNameAndDate(e){if(!e)return"";var t=new Date(e);return["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sept","Oct","Nov","Dec"][t.getMonth()]+"."+t.getDate()}function currency(e,t="$",s){try{if("string"==typeof e&&-1!=e.indexOf(t))return e;var i=parseInt(e);if(isNaN(i))return null;i<100&&s<2&&(s=2);var o=e.toString().split(".");return o[0]=o[0].replace(/\B(?=(\d{3})+(?!\d))/g,","),0==s?o[1]=void 0:s>0&&o[1]&&(o[1]=o[1].substr(0,s)),t+o.filter((function(e){return e})).join(".")}catch(e){return console.error(e),null}}function arrayValue(e){return Array.isArray(e)?e.join(" "):e}var filters={time:time,day:day,number:number,dotdate:dotdate,datetime:datetime,propPrice:propPrice,percentage:percentage,yearMonth:yearMonth,monthNameAndDate:monthNameAndDate,currency:currency,arrayValue:arrayValue},LazyImage={name:"v-lazy-item",props:{dispVar:{type:Object,default:function(){return{}}},dataindex:{type:Number},imgstyle:{type:String,default:""},imgclass:{type:String,default:""},alt:{type:String,default:""},load:{type:Function,default:function(e){}},error:{type:Function,default:function(e){window.hanndleImgUrlError&&hanndleImgUrlError(e.target||e.srcElement)}},data:{type:Object,default:function(e){return{}}},emit:{type:Boolean,default:!0},src:{type:String,default:""},placeholder:{type:String,default:"data:image/png;base64,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"}},data:()=>({lazyExist:!0,intersected:!1,intersectionOptions:{}}),computed:{computedSrc:function(){return this.lazyExist&&!this.intersected?this.placeholder:this.src?this.src:this.placeholder}},methods:{imgOnPress(e){this.emit&&window.bus.$emit("lazy-image-onpress",e,this.data)}},mounted(){"IntersectionObserver"in window?(this.observer=new IntersectionObserver((e=>{e[0].isIntersecting&&(this.intersected=!0,this.observer.disconnect())}),this.intersectionOptions),this.observer.observe(this.$el)):window.replaceSrc?setTimeout((function(){replaceSrc()}),100):this.lazyExist=!1},destroyed(){"IntersectionObserver"in window&&this.observer.disconnect()},template:'\n  <div class="v-lazy-item">\n    <img :rm-data-src="src" :src="computedSrc" @error="error" :alt="alt" :class="imgclass" :style="imgstyle" @load="load" @click="imgOnPress" :dataindex="dataindex" referrerpolicy="same-origin"/>\n  </div>\n  '},forumCommonMixins={created:function(){},data:()=>({monthArray:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"]}),computed:{computedAdmin:function(){var e=!1,t=this;if(!this.dispVar)return!1;if(this.post&&this.dispVar.userGroups){var s=this.dispVar.userGroups.find((function(e){return e._id==t.post.gid}));s&&(s.isAdmin||s.isOwner)&&(e=!0)}return this.dispVar.forumAdmin||e}},methods:{showComments(e){event.stopPropagation(),window.bus.$emit("showComments",e)},getImageUrl:e=>e?"url("+e+"), url('/img/user-icon-placeholder.png')":"url('/img/user-icon-placeholder.png')",formatTs2:function(e){if(!e)return"";e=new Date(e);var t=new Date-e;return t<6e4?this.$_("Just now"):t>864e5?this.$_(this.monthArray[e.getMonth()])+" "+e.getDate():t>36e5?parseInt(t/36e5)+this.$_("Hrs"):parseInt(t/6e4)+this.$_("Mins")},trimStr(e,t){if(!e||!t)return"";var s=0,i=0,o="";for(i=0;i<e.length;i++){if(e.charCodeAt(i)>255?s+=2:s++,s>t)return o+"...";o+=e.charAt(i)}return e},formatTs(e){if(e){var t=(e=new Date(e)).getMinutes();return t<10&&(t="0"+t),e.getFullYear()+"-"+(e.getMonth()+1)+"-"+e.getDate()+" "+e.getHours()+":"+t}return""},blockCmnt(e,t){axios.post("/1.5/forum/blockCmnt",e).then((e=>(e=e.data).ok?t(null,e):(e.e,null))).catch((e=>t(e.status+":"+e.statusText,null)))}}},forumMixins={created:function(){},data:()=>({}),computed:{},methods:{reloadPosts(){var e=this;e.allPosts=[];var t={};e.computedListKey&&(e[e.computedListKey]=[],t.listKey=this.computedListKey,t.hasMoreKey=this.computedHasMoreKey),document.getElementsByClassName("tabs-container").length>0&&document.getElementsByClassName("tabs-container")[0].click(),e.pgNum=1;var s=e.getSearchParmas();s.page=e.pgNum,e.getAllPost(s,t)},goTo(e){window.location.href=e},openTBrowser(e,t={}){this.dispVar.isApp?this.tbrowser(e):window.location.href=e},changeStatus(e,t,s,i){var o=this;axios.post("/1.5/forum/changeStatus",{id:t,status:e,gid:s}).then((t=>{(t=t.data).ok&&(o.post.readStatus=e,i())})).catch((e=>{console.error(e.status+":"+e.statusText)}))},refreshPost(e,t){var s=this;axios.post("/1.5/forum/detail/"+e,{gid:t,type:"summary"}).then((e=>{(e=e.data).ok&&window.bus.$emit("forum-view-close",e.post)})).catch((e=>{s.loading=!1,console.error(e.status+":"+e.statusText)}))},showPostView(e,t,s,i,o,n){var a=this;if(e&&"null"!=e){if(window.vars&&vars.postid&&vars.postid==e){vars.postid=null;var r=new URL(window.location);r.searchParams.set("postid",null),r.search=r.searchParams,r=r.toString(),history.replaceState({},null,r),timeout=0}if(i&&o&&n)axios.post("/1.5/forum/adClick",{id:e,index:0,gid:s}).then((e=>{(e=e.data).ok&&RMSrv.showInBrowser(o)})).catch((e=>{console.error(e.status+":"+e.statusText)}));else{let i=null;i="psch"==t?"/1.5/school/private/detail/"+e:"sch"==t?"/1.5/school/public/detail?id="+e+"&redirect=1":"/1.5/forum/details?id="+e,s&&(i+="&gid="+s),i=this.appendDomain(i);var l={hide:!1,title:this.$_("RealMaster")};if(this.isForumList)return i+="&from=forumList",window.open(i,"_blank");if(!this.dispVar.isApp)return i+="&iswebAdmin=1",document.location.href=i;i.indexOf("?")>0?i+="&inFrame=1":i+="?inFrame=1",RMSrv.getPageContent(i,"#callBackString",l,(function(t){try{if(/^cmd-redirect:/.test(t)){var i=t.split("cmd-redirect:")[1];return window.location=i}}catch(e){console.error(e)}if(":cancel"==t||":later"==t)if(s){var o=":cancel"==t?"read":"later";a.changeStatus(o,e,s,(function(){a.refreshPost(e,s)}))}else a.refreshPost(e,s);else{console.log(t);try{var n=JSON.parse(t);window.bus.$emit("forum-view-close",n)}catch(e){console.error(e)}}}))}}},appendDomain(e){var t=window.location.href.split("/");return e=t[0]+"//"+t[2]+e},getAllPost(e,t={}){var s=this,i="posts_more";t.hasMoreKey&&(i=t.hasMoreKey),s[i]=!1,s.doSearch(e,(function(o){s.loading=!1,s.refreshing=!1,o.length>20&&(s[i]=!0);var n="allPosts";t.listKey&&(n=t.listKey),s[n]=s[n].concat(o.splice(0,20)),s.updateForumsAfterBlocked(n),e.scroll&&setTimeout((()=>{s.scrollPostIntoView()}),20)}))},isForumUserBlocked(e,{blkUids:t,blkCmnts:s}){var i=e.uid;return s[e._id]&&s[e._id].b||i&&t[i]},updateForumsAfterBlocked(e="allPosts"){try{var t=JSON.parse(localStorage.getItem("blkUids"))||{},s=JSON.parse(localStorage.getItem("blkCmnts"))||{}}catch(e){alert(e)}var i=this[e].length,o=[];for(let n=0;n<i;n++){const i=this[e][n];this.isForumUserBlocked(i,{blkUids:t,blkCmnts:s})||o.push(i)}this[e]=o},doSearch(e,t){var s=this;trackEventOnGoogle(this.form,"search"),axios.post("/1.5/forum/query",e).then((e=>{(e=e.data).ok?t(e.forums):e.url&&s.goTo(e.url)})).catch((e=>{console.error(e)}))},setUpPreview(e){var t,s;s=e.naturalWidth,t=e.naturalHeight,s>0&&t>0&&(this.sizes.push(s+"x"+t),e.setAttribute("data-size",s+"x"+t))},listScrolled(){var e=this;e.scrollElement=document.getElementById("forum-containter"),!e.waiting&&e.posts_more&&(e.waiting=!0,e.loading=!0,setTimeout((function(){e.waiting=!1;var t=e.scrollElement;if(t.scrollHeight-t.scrollTop<=t.clientHeight+260)if(e.posts_more){e.pgNum+=1;var s=e.getSearchParmas();s.page=e.pgNum,e.getAllPost(s)}else e.loading=!1;else e.loading=!1}),400))},formatNews(e){var t=document.querySelectorAll(".post-content *[style]");for(var s of t)s.style.removeProperty("font-size"),s.style.removeProperty("line-height"),s.style.removeProperty("color");var i=document.querySelectorAll(".post-content a")||[];for(var o of i)if("realmaster"!=o.getAttribute("data-src"))o.setAttribute("href","javascript:void(0)");else{var n=o.getAttribute("href");/^tel:/.test(n)||/^mailto:/.test(n)||(e?(o.setAttribute("href","javascript:void(0)"),o.setAttribute("onclick","window.RMSrv.showInBrowser('"+n+"')")):(o.setAttribute("href",n),o.setAttribute("target","_blank")))}var a=document.querySelectorAll(".post-content img")||[];for(var r of a){var l=r.getAttribute("data-src");r.getAttribute("data-s");/^(http|https):\/\/mmbiz.qpic.cn/i.test(r.src)?r.src=r.src.replace(/&tp=\w+&wxfrom=\d&wx_lazy=\d/gi,""):l&&(r.src=l),r.setAttribute("style",""),r.style.height="auto",r.style.width="100%",r.getAttribute("data-ignore")||(r.addEventListener("click",this.previewPic),this.setUpPreview(r)),r.parentElement&&(r.parentElement.style.height="auto",r.parentElement.style.width="100%")}var c=document.querySelectorAll("iframe")||[];for(var d of c){var p=d.getAttribute("style");l=d.getAttribute("data-src"),n=d.getAttribute("src");"realmaster"!=l&&(d.src="");var u=/width=((\d|\.)+)&height=((\d|\.)+)/;if(u.test(n)){var g=window.innerWidth-30,h=parseFloat(n.match(u)[1]),m=parseFloat(n.match(u)[3])/h*g,v=n.replace(/width=((\d|\.)+)&/,"width="+g+"&");v=v.replace(/&height=((\d|\.)+)&/,"&height="+m+"&"),d.src=v}if(p){m=d.style.height;var f=d.style.minHeight;d.setAttribute("style",""),d.style.height=m||"auto",d.style.minHeight=f||"240px",d.style.width="100%"}}},getThumbUrl(e){if(e){var t=`img.${this.dispVar.shareHostNameCn||"realmaster.cn"}`;return"url("+e+"),url("+e.replace(t,"img.realmaster.com")+"), url('/img/no-pic.png')"}return"url('/img/no-pic.png')"},inValidNickName:(e,t)=>!1,saveForumName(e){axios.post("/1.5/forum/setFornm",{fornm:this.nickname}).then((t=>{(t=t.data).ok?e():e(t.e)})).catch((e=>{console.error(e)}))},handleMsgAfterBlock(e){var t=this;window.bus.$emit("flash-message",e),setTimeout((function(){t.toggleFlagModal(!1),t.updateForumsAfterBlocked()}),1e3)},blockAuthor(e){var t=e.uid;if(!this.dispVar.isLoggedIn)return this.blkUids[t]=1,localStorage.setItem("blkUids",JSON.stringify(this.blkUids)),void this.handleMsgAfterBlock(this.$_("User was block"));trackEventOnGoogle(this.form,"blockAuthor");var s=this;this.blockCmnt({type:"user",uid:t},(function(e,i){if(e)return window.bus.$emit("flash-message",e);s.blkUids[t]=1,localStorage.setItem("blkUids",JSON.stringify(s.blkUids)),s.handleMsgAfterBlock(i.msg)}))},updateBlkCmnts(e){this.blkCmnts[e._id]?this.blkCmnts[e._id].b=1:this.blkCmnts[e._id]={b:1},localStorage.setItem("blkCmnts",JSON.stringify(this.blkCmnts))},blockPost(e){var t=this;if(!this.dispVar.isLoggedIn)return this.updateBlkCmnts(e),window.bus.$emit("flash-message",this.$_("Post was blocked")),void setTimeout((function(){t.toggleFlagModal(!1),t.showMask=!1,t.reloadPosts()}),1e3);trackEventOnGoogle(this.form,"blockPost");var s={type:"post",forumId:e._id};this.blockCmnt(s,(function(s,i){if(s)return window.bus.$emit("flash-message",s);setTimeout((function(){t.toggleFlagModal(!1),t.showMask=!1,t.reloadPosts()}),1e3),t.updateBlkCmnts(e),t.handleMsgAfterBlock(i.msg)}))},reportPost(e){this.toggleFlagModal(!1),this.showMask=!1,this.showReportForm=!0;var t=this.reportForm,{protocol:s,host:i}=location;if(this.dispVar.isLoggedIn){var{eml:o,nm:n}=this.dispVar.sessionUser;t.userForm.eml=o,t.userForm.nm=n}trackEventOnGoogle(this.form,"reportPost"),t.userForm.id=e._id,t.userForm.url=s+"//"+i+"/1.5/forum/details?id="+e._id,t.userForm.img=e.thumb,t.userForm.tl=e.tl,this.reportForm=t},closeReportForm(){this.showReportForm=!1,this.showMask=!1;var e=this.reportForm;e.userForm.violation="",e.userForm.m="",e.userForm.url="",e.userForm.img="",e.userForm.id="",this.reportForm=e},contactUs(){trackEventOnGoogle(this.form,"contactUs"),RMSrv.showInBrowser("https://realmaster.ca/about-us")},toggleFlagModal(e){var t=document.querySelector("body");e?(t.classList.add("smb-open"),this.flagPost=e,this.showMask=!0):(t.classList.remove("smb-open"),this.flagPost=!1,this.showMask=!1)}}},ForumSummaryCard={mixins:[pageDataMixins,forumMixins,forumCommonMixins],props:{postType:{type:String,default:"forum"},parentPage:{type:String,default:""},post:{type:Object,default:function(){return{hasUpdate:!1,src:"news"}}},dispVar:{type:Object,default:function(){return{}}},hideStickyIcon:{type:Boolean,default:!1},noTag:{type:Object,default:function(){return{}}},noTagAction:{type:Boolean,default:!1},isWeb:{type:Boolean,default:!1},displayPage:{type:String,default:"all"},curStyle:{type:String,default:"left-right"}},components:{LazyImage:LazyImage},computed:{computedThumb:function(){return this.post.thumb?this.post.thumb:"sch"==this.post.src?"/img/school/school_forum.png":"psch"==this.post.src?"/img/school/school_forum_p.png":null},computedForumFas:function(){return this.isForumFas(this.dispVar,{city:this.post.city,prov:this.post.prov})},commentsHeight:function(){return this.post.tags&&this.post.tags[0]||"property"==this.post.src?40:60},computedVc:function(){return(this.post.vc||0)+(this.post.vcc||0)},computedTp:function(){return this.post.tpbl&&this.post.tpbl[0]?this.post.tpbl[0]:this.post.tp?this.$_("Topic"):null},computedImgStyle:function(){return this.layoutStyles[this.curStyle]?this.layoutStyles[this.curStyle].image:this.layoutStyles["left-right"].image},computedContainerStyle:function(){return this.layoutStyles[this.curStyle]?this.layoutStyles[this.curStyle].container:this.layoutStyles["left-right"].container},imageRenderConfig:function(){const e="up-down"===this.curStyle;return{isUpDown:e,containerClass:e?"image-section":"image-section-lr",imageClass:e?"img post-img-ud":"img post-img",imageComponent:e?"div":"lazy-image",imageStyle:this.computedImgStyle,showVideoTags:!0}},videoTagsConfig:function(){return{videoTagClass:this.imageRenderConfig.isUpDown||this.computedThumb?"":"noPic"}}},data:()=>({imageLoadError:!1,layoutStyles:{"left-right":{container:"height: 103px;",image:"width: 120px;height: 90px;background-size: 100% 100%;"},"up-down":{container:"height: auto;",image:"width: 100%; height: 0; padding-bottom: 66.67%; background-size: 100%; background-position: center; background-repeat: no-repeat;"}}}),mounted(){window.bus?this.post.del||(this.post.del=!1):console.error("global bus is required!")},methods:{renderImageComponent(){return{...this.imageRenderConfig,...this.videoTagsConfig,hasThumb:!!this.computedThumb,thumbUrl:this.computedThumb,altText:this.post.tl,firstLetter:this.post.tl?this.post.tl.substr(0,1):"",isVideo:"video"===this.post.src,hasVideoRecord:this.post.vidRecord||this.post.passedLive,isLiving:this.post.vidLive&&this.post.isLiving,isScheduled:this.post.vidLive&&!this.post.isLiving&&!this.post.passedLive,scheduledTime:this.parseDate(this.post.ohv||"")}},toggleFlagModal(e){this.$parent.toggleFlagModal(e)},parseDate(e=""){if(!e||"string"!=typeof e)return"";var t=e.split(" "),s=t[0].split("-"),i=t[1].split("-");return s[1]+"."+s[2]+" "+i[0]},imageLoadError(){this.post.thumb=null},openView(e){return e&&e.stopPropagation(),this.post.hasUpdate&&(this.post.hasUpdate=!1),this.isWeb?window.open("/1.5/forum/webedit?web=true&id="+this.post._id,"_blank"):"wecard"==this.postType?this.$parent.showWecard(this.post):this.$parent.showPostView(this.post._id,this.post.src,this.post.gid),!1},showAd(e){e&&e.stopPropagation(),this.$parent.showPostView(this.post._id,this.post.src,this.post.gid,this.post.adInlist,this.post.adTop,this.post.adTopPhoto)},addCityFilter(e){if(!this.noTagAction)return e&&e.stopPropagation(),this.isWeb?window.open(window.location.href+"&city="+this.post.city+"&prov="+this.post.prov,"_blank"):(this.$parent.curCity={o:this.post.city,p:this.post.prov,cnty:this.post.cnty},this.$parent.reloadPosts()),!1},openTag(e){if(!this.noTagAction){e&&e.stopPropagation();var t=this.post.tags[0];return this.isWeb?window.open(window.location.href+"&tag="+t,"_blank"):(this.$parent.tag=t,this.$parent.reloadPosts()),!1}},openGroup(e){return e&&e.stopPropagation(),this.isWeb?window.open(window.location.href+"&gid="+this.post.gid,"_blank"):(this.$parent.gid=this.post.gid,this.$parent.gnm=this.post.gnm,this.$parent.reloadPosts()),!1},openBySrcView(e,t){this.noTagAction||(t&&t.stopPropagation(),this.isWeb?window.open(window.location.href+"&src="+e,"_blank"):(this.$parent.src=e,this.$parent.reloadPosts()))},openTp(e){if(!this.noTagAction){if(e&&e.stopPropagation(),this.post.tp)this.isWeb?window.open("http://"+this.dispVar.reqHost+"/forum/"+this.post._id+"/"+formatUrlStr(this.post.tl),"_blank"):this.$parent.showPostView(this.post._id,this.post.src);else{var t=this;axios.get("/1.5/forum/findPostByTp/"+t.computedTp).then((e=>{if(!(e=e.data).ok)return window.bus.$emit("flash-message",e.e);this.isWeb?window.open("http://"+t.dispVar.reqHost+"/forum/"+e.postid,"_blank"):this.$parent.showPostView(e.postid,this.post.src)})).catch((()=>{console.error(err.status+":"+err.statusText)}))}return!1}}},template:'\n  <div class="forum-summary-card" v-bind:class="{summaryWeb: isWeb , greybg: post.gid, \'layout-up-down\': curStyle == \'up-down\',\'box-shadow\': curStyle == \'up-down\'}">\n    \x3c!-- 广告模式，仅显示图片部分 --\x3e\n    <div v-if="post.adInlist && post.adTopPhoto && post.adTop">\n      <div class="post-top-div" style="position:relative; display:block!important;">\n        <div class="edit" v-if="dispVar.forumAdmin" @click="openView()"><span>{{post.vcad0}}</span><span>{{$_(\'Edit\')}}</span></div>\n        <img class="post-top-img" v-if="post.adTopPhoto" :src="post.adTopPhoto" @click="showAd()" referrerpolicy="same-origin"/>\n        <div class="post-top-text">{{$_(\'AD\')}}</div>\n      </div>\n    </div>\n    \x3c!-- 非广告模式，显示正常内容 --\x3e\n    <div v-else :style="computedContainerStyle" :class="{noCity: dispVar.forumAdmin && !post.city && !post.cnty && !post.prov}">\n        \x3c!-- 上下布局：图片在上 --\x3e\n        <div v-if="curStyle == \'up-down\'" :class="renderImageComponent().containerClass" @click="openView()">\n            <div v-if="renderImageComponent().hasThumb" :style="renderImageComponent().imageStyle + \'background-image: url(\' + renderImageComponent().thumbUrl + \');\'" :class="renderImageComponent().imageClass">\n                <span class="vidRecord" v-if="renderImageComponent().isVideo && renderImageComponent().hasVideoRecord" style="position: absolute; top: 10px; right: 10px;"><i class="fa fa-video-camera"></i></span>\n                <span class="vidLive vidLive1" v-if="renderImageComponent().isVideo && renderImageComponent().isLiving" style="position: absolute; bottom: 10px; right: 10px;">{{$_(\'LIVE\')}}</span>\n                <span class="vidLive vidLive2" v-if="renderImageComponent().isVideo && renderImageComponent().isScheduled" style="position: absolute; bottom: 10px; right: 10px;">{{renderImageComponent().scheduledTime}}</span>\n            </div>\n            <div v-else :class="renderImageComponent().imageClass" :style="renderImageComponent().imageStyle">\n                <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); font-size: 50px; color: #d2d5d8;">{{renderImageComponent().firstLetter}}</div>\n            </div>\n        </div>\n\n        \x3c!-- 内容区域 --\x3e\n        <div :class="curStyle == \'up-down\' ? \'content-section-ud\' : \'post-summary\'">\n            \x3c!-- 标题区域 --\x3e\n            <div :class="curStyle == \'up-down\' ? \'post-title-ud\' : \'post-title\'" v-bind:class="{deleted: post.del}" @click="openView()">\n                <span class="red-dot-forum fa fa-circle" v-if="post.hasUpdate"></span>\n                <span  class="red-button"  v-if="post.adInlist && post.adTopPhoto && post.adTop && curStyle == \'up-down\'">{{$_(\'AD\')}}</span>\n                <span class="red-button" v-if="post.sticky && !noTag.top">{{$_(\'TOP\')}}</span>\n                <span class="red-button" @click="openGroup()" v-if="post.gid && post.gnm && !noTag.gid">{{post.gnm}}</span>\n                <span class="red-button blue" @click="openTp()" v-else-if="(post.tpbl && post.tpbl.length) || (post.tp && !noTag.topic)">{{computedTp}}</span>\n                <span class="red-button blue" @click="openTag()" v-else-if="(post.tags && post.tags[0]) && !noTag.tag">\n                    <span v-if="post.tags[0]==\'HOT\'">{{$_(\'HOT\')}}</span>\n                    <span v-else>{{post.tags[0]}}</span>\n                </span>\n                <span class="red-button blue" @click="openBySrcView(\'property\')" v-else-if="post.src==\'property\' && post.cc>0 && !noTag.property">{{$_(\'Home Review\')}}</span>\n                <span class="red-button blue" @click="openBySrcView(\'sch\')" v-else-if="(post.src==\'sch\'||post.src==\'psch\') && !noTag.sch && !noTag.psch">{{$_(\'School Review\')}}</span>\n                <span class="red-button blue" @click="addCityFilter()" v-show="(post.city||post.prov||post.cnty) && !noTag.city && post.cnty!==\'No City\'">{{$_(post.city||post.prov||post.cnty,\'city\')}}</span>\n                \x3c!-- 标题文本 --\x3e\n                <span :class="curStyle == \'up-down\' ? \'txt-ud\' : \'txt\'" v-if="[\'property\',\'psch\',\'sch\'].indexOf(post.src)>=0 && post.cmntl">{{post.cmntl + \' \' + post.tl}}</span>\n                <span :class="curStyle == \'up-down\' ? \'txt-ud\' : \'txt\'" v-else>{{post.tl}}</span>\n            </div>\n\n            \x3c!-- 评论信息区域 --\x3e\n            <div class="post-comments" :style="curStyle == \'up-down\' ? \'margin-top: 8px;\' : \'\'">\n                <span class="post-name pull-left">{{trimStr(post.fornm,12)}}</span>\n                <span class="post-bottom">\n                    <span v-if="post.src!=\'sch\' && post.src!==\'psch\'">\n                        <span v-if="dispVar.isAdmin">{{post.vc}} | {{post.vcapp}} | {{post.vcc}}</span>\n                        <span v-else>{{computedVc}}</span>\n                        <span class="fa fa-eye" style="padding-left:\'5px\'"></span>\n                    </span>\n                    <span v-if="(post.cc > 0) && (!post.discmnt || dispVar.forumAdmin)">\n                        <span v-if="post.src!=\'sch\' && post.src!==\'psch\'">|</span>\n                        <span>{{post.cc}}</span>\n                        <span class="fa fa-comments"></span>\n                    </span>\n                    <span v-if="dispVar.forumAdmin && post.similars">| S: {{post.similars.length}} |</span>\n                </span>\n                <span class="post-ts" v-if="dispVar.isAdmin">{{formatTs2(post.mt)}}</span>\n                <span class="post-ts" style="padding-right:5px;">{{formatTs2(post.ts)}}</span>\n                <span class="realtor-only" v-if="post.realtorOnly">{{$_(\'Realtor Only\')}}</span>\n                <span class="icon icon-close reportForumIcon" v-if="parentPage == \'forum\'" data-sub="report forum" :data-id="post._id" @click="toggleFlagModal(post)"></span>\n            </div>\n        </div>\n\n        \x3c!-- 左右布局：图片在右 --\x3e\n        <div v-if="curStyle !== \'up-down\'" :class="renderImageComponent().containerClass" @click="openView()">\n            <lazy-image :class="renderImageComponent().imageClass" v-if="renderImageComponent().hasThumb" :alt="renderImageComponent().altText" :error="imageLoadError" :src="renderImageComponent().thumbUrl" :imgstyle="renderImageComponent().imageStyle"></lazy-image>\n            <span class="vidRecord" v-if="renderImageComponent().isVideo && renderImageComponent().hasVideoRecord" :class="renderImageComponent().videoTagClass"><i class="fa fa-video-camera"></i></span>\n            <span class="vidLive vidLive1" v-if="renderImageComponent().isVideo && renderImageComponent().isLiving" :class="renderImageComponent().videoTagClass">{{$_(\'LIVE\')}}</span>\n            <span class="vidLive vidLive2" v-if="renderImageComponent().isVideo && renderImageComponent().isScheduled" :class="renderImageComponent().videoTagClass">{{renderImageComponent().scheduledTime}}</span>\n            <div :class="renderImageComponent().imageClass" v-if="!renderImageComponent().hasThumb">\n                <div class="no-img">{{renderImageComponent().firstLetter}}</div>\n            </div>\n        </div>\n    </div>\n  </div>\n  '},indexHeader={data:()=>({datas:["sessionUser","hasFollowedVipRealtor","userCity","isLoggedIn","isRealtor","isVipPlus","hasAid","lang","isApp","isCip","coreVer","projLastQuery","languageObj","isEmailVerified","homePageABTest"],dispVar:{lang:"zh",userCity:{o:"Toronto",n:"多伦多"}},nativeSearchClicked:!1,isNativeSearch:!1,focused:!1,userForm:{},signupTitle:"",feedurl:"/1.5/form/forminput",hasWechat:!1,strings:{refreshAlert:{key:"Please refresh first."}}}),mounted(){function e(e){var t=(e=(e||navigator.userAgent).toLowerCase()).match(/android\s([0-9\.]*)/);return!!t&&t[1]}this.$getTranslate(this);var t=this,s=window.bus;e()&&parseFloat(e())<4.4&&(t.oldVerBrowser=!0),/mls/.test(location.href)&&(t.datas=t.datas.concat(["savedSearches","favCmtys","propTags","edmDetail"])),/rm/.test(location.href)&&(t.datas=t.datas.concat(["forumAdmin","reqHost","shareHostNameCn","userGroups","isAdmin"])),t.getPageData(t.datas,{},!0),s.$on("pagedata-retrieved",(function(e){if(t.dispVar=Object.assign(t.dispVar,e),t.isNativeSearch=t.isNewerVer(t.dispVar.coreVer,"6.0.1"),t.hasDeviceId=t.isNewerVer(t.dispVar.coreVer,"6.1.5"),!t.updatedPN&&e.updatePN&&localStorage.pn&&t.postPN(),e.userCity&&(t.rcmdCity={o:e.userCity.o,n:e.userCity.n,lat:e.userCity.lat,lng:e.userCity.lng,p:e.userCity.p},e.userCity.cnt&&(t.userCityCnt=e.userCity.cnt)),t.dispVar.sessionUser&&t.dispVar.sessionUser.eml){for(let e of["nm","eml","mbl","fnm"])t.userForm[e]=t.dispVar.sessionUser[e];t.userForm.nm&&!t.userForm.fnm||(t.userForm.nm=t.userForm.fnm||e.sessionUser.nm_en||e.sessionUser.nm_zh)}t.recordEmlAndTime(),e.lang&&RMSrv.setAppLang&&t.dispVar.lang&&"function"==typeof RMSrv.onReady&&RMSrv.onReady((()=>{t.hasDeviceId&&RMSrv.getUniqueId((e=>{e&&e.length>5&&t.logUserId(e)})),RMSrv.hasWechat&&RMSrv.hasWechat((e=>{null!=e&&(t.logUserHasWechat(e),t.hasWechat=e,e||(t.hideElemById("#banners"),t.hideElemBySelectors("#indexAd > div.sp-holder  div.img > div"),t.hideDrawAndMove(),setTimeout((()=>{t.dispVar.isVipPlus||(t.hideElemById("#realtorTodos"),t.hideElemById("#promotionsMarketing"))}),100)))})),RMSrv.hasGoogleService&&RMSrv.hasGoogleService((e=>{var s="#drawers > div.swiper-wrapper  a.drawer.link-transit",i=document.querySelector(s);!e&&i&&(t.hideElemBySelectors(s),t.hideElemBySelectors("#drawers > div.swiper-wrapper  a.drawer.link-co-ophouse"))}));var e=window.location.href,s=t.extractDomain(e);RMSrv.setCookie&&RMSrv.setCookie(),RMSrv.action("pushToken"),RMSrv.setAppLang(t.dispVar.lang),RMSrv.setItemObj({key:"lastLoadDomain",value:s,stringify:!1,store:!1},(()=>{})),RMSrv.refreshSystemValue&&RMSrv.refreshSystemValue(),RMSrv.removeItemObj&&!window.localStorage.lastRMMapPosTs&&(window.localStorage.lastRMMapPosTs="202004230653",RMSrv.removeItemObj("lastMapPosition"))})),t.inited||t.initIndexData()})),s.$on("index-redirect-goto",(function(e){t.goTo(e)})),s.$on("prop-changed",(function(e){if(s=/^RM/.test(e.id)?e.id:e._id,!t.isNewerVer(t.dispVar.coreVer,"5.3.0"))return window.location="/1.5/search/prop?d=/1.5/index&id="+s;var s,i={hide:!1,title:t.$_("RealMaster")},o="/1.5/prop/detail/inapp?lang="+t.dispVar.lang+"&id="+s;o=t.appendDomain(o),RMSrv.getPageContent(o,"#callBackString",i,(function(e){if(":cancel"!=e){if(/^redirect/.test(e))return window.location=e.split("redirect:")[1];try{if(/^cmd-redirect:/.test(e)){var t=e.split("cmd-redirect:")[1];return window.location=t}window.location="/1.5/mapSearch?d=/1.5/index&"+e}catch(e){console.error(e)}}}))})),s.$on("set-city",(function(e){var s="",i={city:e.city};s="subscribe"==t.mode?"/1.5/index/subscribe":"/1.5/settings/city",axios.post(s,i).then((e=>{(e=e.data).ok?(toggleModal("citySelectModal"),e.msg&&window.bus.$emit("flash-message",e.msg),setLoaderVisibility("block"),setTimeout((()=>{location.reload()}),500)):window.bus.$emit("flash-message",e.e)})).catch((e=>{console.error("server-error")}))}))},methods:{openCamera(){var e,t=-1,s=this;RMSrv.scanQR((function(i){if(i&&(t=i.indexOf("/s/"))>0)return e=i.substr(t),openPopup(e,s.$_("RealMaster"));i&&/[1.5|qrcode]/.test(i)?(t=i.indexOf("/qrcode")>0?i.indexOf("/qrcode"):i.indexOf("/1.5/"),e=i.substr(t).replace("share=1","").replace("inFrame=1","")):e="/qrcode/act404",window.location.href=e}))},getTranslate:function(e){return TRANSLATES[e]||e},hideElemById(e){var t=document.querySelector(e);t&&t.remove()},hideDrawAndMove(){var e="#drawers > div.swiper-wrapper  a.drawer.link-agent";document.querySelector(e)&&(this.hideElemBySelectors(e),this.hideElemBySelectors("#drawers > div.swiper-wrapper  a.drawer.link-yellowpage"))},appendEmptyPlaceHolder(){let e=document.querySelector("#drawers > div.swiper-wrapper  .wrapper.newDrawers3");if(e)for(let t=0;t<=1;t++){let t=document.createElement("a");t.classList.add("drawer"),e.append(t)}},hideElemBySelectors(e){var t=document.querySelectorAll(e);if(t&&t.length)for(let e of t)e.remove()},logUserHasWechat(e){var t=this;t.dispVar.isLoggedIn&&(t.loggedWechat||(t.loggedWechat=!0,axios.post("/loguserwechat",{hasWechat:e}).then((e=>{(e=e.data).ok})).catch((e=>{}))))},logUserId(e){this.dispVar.isLoggedIn&&axios.post("/matchuser",{id:e}).then((e=>{(e=e.data).ok&&console.log(e)})).catch((e=>{}))},onClickSearchBar(){if(!this.nativeSearchClicked){var e=this.dispVar.userCity.o,t=this.dispVar.userCity.p,s=this.dispVar.lang||"en";this.nativeSearchClicked=!0,this.goTo({url:"/1.5/autocomplete?referer=index&city="+e+"&prov="+t+"&lang="+s}),setTimeout((()=>{this.nativeSearchClicked=!1}),1e3)}},extractDomain:e=>(e.indexOf("//")>-1?e.split("/")[2]:e.split("/")[0]).split(":")[0].split("?")[0],postPN(){if(localStorage.pn&&!this.updatedPN){var e=this;e.updatedPN=!0,axios.post("/1.5/user/updPn",{pn:localStorage.pn}).then((t=>{(t=t.data).r||(console.error(t.e),e.updatedPN=!1)})).catch((e=>{console.error("server-error")}))}},unsubscribe(){trackEventOnGoogle("homeSubscribedCities","unsubscribe");var e=this;axios.post("/1.5/index/subscribe",{mode:"unsubscribe",idx:this.cityIdx}).then((t=>{(t=t.data).ok?(null!=t.cnt?e.userCityCnt=t.cnt:e.userCityCnt-=1,t.msg&&window.bus.$emit("flash-message",t.msg),e.cityIdx=0):window.bus.$emit("flash-message",t.e)})).catch((e=>{console.error("server-error")}))},initIndexData(){this.inited=!0,this.hist=("undefined"!=typeof indexHistCtrl&&null!==indexHistCtrl?indexHistCtrl.getHist():void 0)||[]},getCityList(e){let t="homeTopBar";"subscribe"==e&&(t="homeSubscribedCities",window.bus.$emit("select-city",{noloading:!0}),this.dispVar.isLoggedIn||(window.location="/1.5/user/login")),this.mode=e,window.bus.$emit("select-city",{noloading:!0}),trackEventOnGoogle(t,"selectCity")},recordEmlAndTime(){this.dispVar.sessionUser&&RMSrv.getItemObj("recordEml",!0,(e=>{e&&e.length||fetchData("/1.5/user/encryptEml",{body:{}},(function(e,t){if(1==t.ok&&t.encryptedEml){let e={eml:t.encryptedEml,ts:new Date};RMSrv.setItemObj({key:"recordEml",value:e,stringify:!0,store:!0})}}))}))}},computed:{}};function showVipInfo(){RMSrv.showInBrowser("https://www.realmaster.ca/membership")}function changeTab(e){for(var t=document.getElementsByClassName("promotionsTl"),s=0;s<t.length;s++)t[s].id=="title-"+e?t[s].classList.add("active"):t[s].classList.remove("active");var i=document.getElementsByClassName("promotionsList");for(s=0;s<i.length;s++)i[s].id==e?i[s].classList.add("active"):i[s].classList.remove("active")}function hideAlert(){if(window.angular){var e=document.querySelector("[ng-controller=ctrlNotifications]"),t=window.angular.element(e).scope();t&&(t.showAppUpgrade=!1,t.$apply())}else{var s=document.getElementById("alert-window-bg");s&&(s.style.display="none")}}function closeApp(){RMSrv&&RMSrv.exitApp?RMSrv.exitApp():hideAlert()}function openDownloadLink(e){"android"===document.getElementById("currentOS").value&&RMSrv.hasGoogleService&&RMSrv.hasGoogleService((function(e){e&&RMSrv.showInBrowser("https://play.google.com/store/apps/details?id=com.realmaster")})),RMSrv.showInBrowser(e)}function swipeTodos(){new Swiper(".todoSwiper",{pagination:{el:".todoSwiperPagination",type:"fraction"}})}function swipeBanners(){new Swiper("#banners",{autoplay:{delay:5e3,disableOnInteraction:!1},pagination:{el:".bannerPagination",clickable:!0}})}function swipeNews(){new Swiper("#news",{loop:!0,autoplay:{delay:3e3,disableOnInteraction:!1},pagination:{el:".newsPagination",clickable:!0}})}function swipeDrawers(){new Swiper("#drawers",{loop:!1,pagination:{el:".drawersPagination",clickable:!0}})}function swipeAssigment(){new Swiper("#trustedAssign",{loop:!0,autoplay:{delay:3e3,disableOnInteraction:!1},pagination:{el:".assignPagination",clickable:!0},slidesPerView:2,loopedSlides:2,spaceBetween:10})}function swipePreCon(){new Swiper("#preCon",{loop:!0,autoplay:{delay:3e3,disableOnInteraction:!1},pagination:{el:".preConPagination",clickable:!0},slidesPerView:2,loopedSlides:4,spaceBetween:10,loopAdditionalSlides:2})}function toggleHome(e){gotoLink("/home/"+e,{key:"homeSwitch",val:e})}function calcSpWraper(){if(document.getElementsByClassName("sp-wrapper").length){var e=parseInt((window.innerWidth-30)/2.05),t=e*document.querySelectorAll(".sp").length;document.getElementsByClassName("sp-wrapper")[0].style.width=t+"px";var s=document.getElementsByClassName("sp");for(i=0;i<s.length;i++)s[i].style.width=e+"px"}}function calcMktWraper(){if(document.getElementsByClassName("mktrcmd").length){var e=parseInt((window.innerWidth-30)/2.05),t=e*document.querySelectorAll(".mkt").length;document.getElementsByClassName("mktrcmd")[0].style.width=t+"px";var s=document.getElementsByClassName("mkt");for(i=0;i<s.length;i++)s[i].style.width=e+"px"}}function showMoreDrawers(){let e,t=document.querySelector("#drawers-wrapper");e=t.classList.contains("limited-height")?"down":"up",t.classList.toggle("limited-height"),document.querySelector("#moreDrawers .fa-home-arrow-up").classList.toggle("hide"),document.querySelector("#moreDrawers .fa-home-arrow-down").classList.toggle("hide"),checkAndSendLogger(null,{sub:"mls",query:{direction:e},act:"toggle drawer"})}function addDrewersEventListener(e){e.ontouchstart=function(e){e.preventDefault(),e.changedTouches[0].pageY},e.ontouchend=function(e){e.preventDefault(),e.changedTouches[0].pageY,showMoreDrawers()},e.onclick=function(e){e.preventDefault(),showMoreDrawers()}}(app=Vue.createApp(indexHeader)).component("city-select-modal",CitySelectModal),app.component("lang-select-cover",LangSelectCover),app.component("flash-message",flashMessage),app.mixin(rmsrvMixins),app.mixin(pageDataMixins),trans.install(app,{ref:Vue.ref}),app.mount("#index-header"),document.addEventListener("DOMContentLoaded",(function(){})),document.addEventListener("DOMContentLoaded",(function(e){setTimeout((function(){swipeBanners(),document.querySelector(".newsPagination")&&swipeNews(),swipeTodos(),calcSpWraper(),swipeAssigment(),document.querySelector(".mls")&&swipePreCon(),document.querySelector("#moreDrawers")&&addDrewersEventListener(document.querySelector("#moreDrawers"))}),300)})),window.addEventListener("click",checkAndSendLogger);var BlockPopup={props:{dispVar:{type:Object,default:function(){return{}}},flagPost:{type:Object,default:function(){return{}}}},data:()=>({}),mounted(){if(this.closeCountdownTimer&&(clearInterval(this.closeCountdownTimer),this.closeCountdown=3),window.bus);else console.error("global bus is required!")},methods:{blockPost(){this.$parent.blockPost(this.flagPost)},blockAuthor(){this.$parent.blockAuthor(this.flagPost)},reportPost(){this.$parent.reportPost(this.flagPost)},contactUs(){this.$parent.contactUs()},toggleFlagModal(){this.$parent.toggleFlagModal()}},template:'\n<div>\n  <nav class="menu slide-menu-bottom smb-auto">\n    <ul class="table-view">\n        <li class="table-view-cell"><a class="flagModalLink" data-sub="block post" :data-id="flagPost&&flagPost._id?flagPost._id:\'\'" @click="blockPost(flagPost)"><i class="fa fa-ban"></i>{{$_(\'Block this post\')}}</a></li>\n        <li class="table-view-cell" v-if="flagPost && flagPost.author && (!flagPost.author.isAdmin)"><a class="flagModalLink" data-sub="block user" :data-id="flagPost&&flagPost.author&&flagPost.author._id?flagPost.author._id:\'\'" @click="blockAuthor(flagPost)"><i class="fa fa-ban"></i>{{$_(\'Block this user\')}}</a></li>\n        <li class="table-view-cell"><a class="flagModalLink" data-sub="report info" :data-id="flagPost&&flagPost._id?flagPost._id:\'\'" @click="reportPost(flagPost)"><i class="fa fa-flag"></i>{{$_(\'Report\',\'forum\')}}</a></li>\n        <li class="table-view-cell"><a class="flagModalLink" data-sub="contact us" :data-id="flagPost&&flagPost._id?flagPost._id:\'\'" @click="contactUs()"><i class="fa fa-fw fa-phone"></i>{{$_(\'Contact us\')}}</a></li>\n        <li class="cancel table-view-cell" @click="toggleFlagModal(false)" data-sub="cancel report" :data-id="flagPost&&flagPost._id?flagPost._id:\'\'">{{$_(\'Cancel\')}}</li>\n    </ul>\n  </nav>\n</div>\n'},ReportForumForm={components:{PageSpinner:PageSpinner},props:{dispVar:{type:Object,default:function(){return{}}},target:{type:Object,default:function(){return{}}},needWxid:{type:Boolean,default:!1},cstyle:{type:Object,default:function(){return{}}},owner:{type:Object,default:function(){return{vip:!1}}},userForm:{type:Object},page:{type:String,default:"forumHome"},isWeb:{type:Boolean,default:!1},title:{type:String,default:""},feedurl:{type:String,default:"/chat/api/feedback"},mblNotRequired:{type:Boolean,default:!1},mRequired:{type:Boolean,default:!1}},data(){return{closeCountdown:3,closeCountdownTimer:null,options:["Uncivil or offensive","Safety issue, fraud or illegal","Misinformation","Copyright violation","Spam or annoying"],nmErr:!1,emlErr:!1,mblErr:!1,mErr:!1,sending:!1,message:null,picUrls:this.$parent.picUrls||[]}},mounted(){if(this.closeCountdownTimer&&(clearInterval(this.closeCountdownTimer),this.closeCountdown=3),window.bus){var e=this;bus.$on("reset-signup",(function(t){e.message=null;var s=document.querySelector("#ForumSignUpSuccess"),i=document.querySelector("#signUpForm");i&&e.owner.vip&&(i.style.display="block"),s&&(s.style.display="none")}))}else console.error("global bus is required!")},methods:{closeReportForm(){this.$parent.closeReportForm()},signUp(){var e=this;e.nmErr=!1,e.emlErr=!1,e.mblErr=!1,e.mErr=!1,e.message=null;let t=document.querySelector("#ForumSignUpSuccess");t.style.display="none",e.sending||(e.sending=!0,axios.post(e.feedurl,e.userForm).then((s=>(s=s.data,e.sending=!1,s.ok?(document.querySelector("#signUpForm").style.display="none",e.closeCountdownTimer=setInterval((()=>{e.closeCountdown>1?e.closeCountdown--:(clearInterval(e.closeCountdownTimer),e.$parent.closeReportForm())}),1e3),s.msg&&(e.message=s.msg)):e.message=s.err||s.e,t.style.display="block"))).catch((()=>{e.sending=!1,console.error("Server Error")})))}},template:'\n  <div id="reportForm">\n    <div class="mask show"></div>\n    <div id="reportFormContainer" :class="page">\n        <div id="ForumSignUpSuccess" :style="cstyle">\n          <i class="fa fa-check-circle" v-if="!message"></i>\n          <span v-if="message">{{message}}</span>\n          <span v-else>{{$_(\'Your feedback has been submitted.\')}}\n            <span>{{$_(\'This will close in \') + closeCountdown + \'s\'}}</span>\n          </span>\n        </div>\n        <form id="signUpForm" :class="{\'visible\':owner.vip, \'web\':isWeb}" :style="cstyle">\n            <page-spinner :loading.sync="sending"></page-spinner>\n            <div class="tl"><span v-if="title">{{title}}</span><span v-else>{{$_(\'Contact Me\')}}</span></div>\n            <div><label><span class="tp">{{$_(\'What is wrong\')}}</span><span class="ast">*</span></label><select style="height: 35px;background-color:#fff" v-model="userForm.violation">\n                    <option value="" selected="selected">{{$_(\'Please Select\')}}</option>\n                    <option v-for="(o,idx) in options" :value="o">{{$_(o)}}</option>\n                </select></div>\n            <div><label><span class="tp">{{$_(\'Message\',\'signUpForm\')}}</span><span class="ast" v-if="mRequired">*</span></label><textarea class="m" style="padding:10px;" rows="3" v-model="userForm.m" :class="{\'error\':mErr}"></textarea></div>\n            <div><button class="btn btn-block btn-signup" :disabled="!userForm.violation" type="button" @click="signUp()">{{$_(\'Submit\',\'signUpForm\')}}</button></div>\n        </form><img class="close" @click="closeReportForm()" src="/img/staging/close.png" />\n    </div>\n  </div>\n'};ONE_DAY=864e5,initUrlVars();var app,forum={data:()=>({showSearchbar:!1,search:"",dispVar:{isLoggedIn:!1,lang:"zh",isApp:!0,isCip:!1,forumAdmin:!1,sessionUser:"",newsAdmin:!1,isAdmin:!1},pageTag:"news",forumLangs:[],flagPost:null,newsList:[],newsPageNum:1,newsHasMore:!0,newsTags:{},videoList:[],videoPageNum:1,videoHasMore:!0,videoTags:{},posts_more:!1,qna:[],qna_more:!1,todayChoices:[],todayChoices_more:!1,news:[],news_more:!1,curCity:{},exCities:null,curPost:{},loading:!1,displayPage:"all",scrollElement:null,tag:null,src:null,gid:null,blkUids:{},blkCmnts:{},lastPropTag:"",showMask:!1,title:"RealMaster",showReportForm:!1,reportForm:{postId:null,title:"Report",feedurl:"/1.5/form/forminput",userForm:{m:"",tp:"feedback",subjectTp:"Forum Complaint",formid:"system",id:"",violation:"",tl:""}},form:"homeForum"}),mounted(){this.$getTranslate(this);var e=this;try{this.blkUids=JSON.parse(localStorage.getItem("blkUids"))||{},this.blkCmnts=JSON.parse(localStorage.getItem("blkCmnts"))||{}}catch(e){return window.bus.$emit("flash-message",e.toString())}var t=!1;if(/\/home\/marketplace/.test(document.URL)&&(t=!0),t||e.$nextTick((function(){$("#forum-containter").xpull({callback:function(){e.reloadPosts()}})})),window.bus){e=this;window.bus.$on("pagedata-retrieved",(function(t){e.dispVar=Object.assign(e.dispVar,t),t.isCip||(clearTimeout(e.getForumTimeout),e.getForumTimeout=setTimeout((()=>{e.getShowData()}),500),e.scrollElement=document.getElementById("content"),e.scrollElement.addEventListener("scroll",(function(){e.handleLoadMore()})))}))}else console.error("global bus is required!")},methods:{calcDirection:function(e,t){let s="news"==this.pageTag?0:1,i="news"==this.lastPropTag?0:1;return t==s?i>t?"r2l":"l2r":t==i?"out":""},clickMask(){this.showMask=!1,this.toggleFlagModal(!1)},selectPage(e){this.pageTag!=e&&(this.lastPropTag=this.pageTag,this[this.computedTags]={tag:this.tag,src:this.src,curCity:this.curCity},this.pageTag=e,this.tag=this[this.computedTags].tag||null,this.src=this[this.computedTags].src||null,this.curCity=this[this.computedTags].curCity||{},this[this.computedListKey].length||this.getShowData(),trackEventOnGoogle(this.form,"tag",e)),setTimeout((()=>{document.getElementsByClassName("tabs-container")[0].click()}),10)},getShowData(){"news"!=this.pageTag&&(this.tag="视频");var e=this.getSearchParmas();e.page=this[this.computedPageNumber],this.getAllPost(e,{listKey:this.computedListKey,hasMoreKey:this.computedHasMoreKey})},clearTag(e){switch(e){case"all":this.tag=null,this.src=null,this.curCity={};break;case"tag":this.tag=null;break;case"gid":this.gid=null;break;case"src":this.src=null;break;case"city":this.curCity={}}this[this.computedListKey]=[],this[this.computedPageNumber]=1,this.getShowData(),setTimeout((()=>{document.getElementsByClassName("tabs-container")[0].click()}),10)},getSearchParmas(){var e={};return this.search&&(e.search=this.search),this.curCity&&this.curCity.o&&(e.city=this.curCity.o),this.gid&&(e.gid=this.gid),this.curCity&&this.curCity.p&&(e.prov=this.curCity.p),this.curCity&&this.curCity.cnty&&(e.cnty=this.curCity.cnty),this.tag&&(e.tag=this.tag),e},handleLoadMore:function(){var e=this;e.waiting||(e.waiting=!0,e.scrollLoading=!0,setTimeout((function(){e.waiting=!1;var t=e.scrollElement;t.scrollHeight-t.scrollTop<=t.clientHeight+260&&e[e.computedHasMoreKey]&&(e[e.computedPageNumber]+=1,e.getShowData()),e.checkScrollAndSendLogger(t)}),400))}},computed:{hasTag:function(){return this.tag&&"视频"!=this.tag||this.gid||this.curCity.o||this.curCity.p||this.curCity.cnty},computedPageNumber:function(){return this.pageTag+"PageNum"},computedListKey:function(){return this.pageTag+"List"},computedHasMoreKey:function(){return this.pageTag+"HasMore"},computedTags:function(){return this.pageTag+"Tags"},noTag:function(){return{tag:this.tag,property:"property"==this.src,psch:"psch"==this.src,sch:"sch"==this.src,topic:"topic"==this.displayPage,city:this.curCity.o,gid:this.gid}}}};(app=Vue.createApp(forum)).mixin(rmsrvMixins),app.mixin(pageDataMixins),app.mixin(forumMixins),app.mixin(forumCommonMixins),trans.install(app,{ref:Vue.ref}),app.component("forum-summary-card",ForumSummaryCard),app.component("flash-message",flashMessage),app.component("report-forum-form",ReportForumForm),app.component("block-popup",BlockPopup),app.config.globalProperties.$filters={currency:filters.currency},app.mount("#forum");
