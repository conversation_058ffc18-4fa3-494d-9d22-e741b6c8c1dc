var LANGUAGE_OBJ=[{lang:"en",nm:"English"},{lang:"zh-cn",nm:"简体中文"},{lang:"zh",nm:"繁体中文"},{lang:"kr",nm:"한국어"}];function createLangTemplate(e){var t=`\n  <div style="z-index: 400;position: fixed;top: 0;right: 0;bottom: 0;left: 0;background-color: rgba(0, 0, 0, .8);">\n    <div class="language-box"\n      style="background: rgba(245,245,245,0.8);position: fixed;padding: 20px;border-radius: 15px;top: 50%;left: 50%;transform: translate(-50%, -50%);">\n      ${LANGUAGE_OBJ.map((t=>`\n        <div class="lang" style="padding: 10px 20px;border-radius: 5px;text-align: center;background: ${e==t.lang?"rgba(130,130,130,0.6)":"transparent"}" onClick="selectLang('${t.lang}')" onMouseOver="this.style.background='rgba(130,130,130,0.6)';" onMouseOut="this.style.background='transparent';">\n          ${t.nm}\n        </div>\n        `)).join("")}\n    </div>\n  </div>\n  `,i=document.createElement("div");i.innerHTML=t,i.id="langSelectBox",document.body.appendChild(i)}function closeLangTemplate(){var e=document.getElementById("langSelectBox");document.body.removeChild(e)}function selectLang(e){closeLangTemplate(),setLang(e)}var CitySelectModal={components:{flashMessage:flashMessage},mixins:[pageDataMixins],props:{needLoc:{type:Boolean,default:!1},showProvBar:{type:Boolean,default:!1},curCity:{type:Object,default:function(){return{o:"Toronto",n:"多伦多"}}},nobar:{type:Boolean,default:!1},showSubscribe:{type:Boolean,default:!0},lang:{type:String}},computed:{computedFavCities:function(){if(!this.filter)return this.favCities;return this.favCities.filter(this.filterFn)}},data:()=>({setCurCity:!1,filter:"",prov:"CA",loading:!1,provs:[],extCities:[],extCitiesCp:[],favCities:[],favCitiesCp:[],userCities:[],histCities:[],dispVar:{lang:"en"},datas:["lang"]}),mounted(){this.getProvs(),this.getUsercities();var e=window.bus,t=this;this.lang?this.dispVar.lang=this.lang:this.getPageData(this.datas,{},!0),e.$on("pagedata-retrieved",(function(e){t.dispVar=Object.assign(t.dispVar,e)})),window.bus?(e.$on("select-city",(function(e={}){if(t.doSearch=!!e.doSearch,t.favCities&&t.favCities.length)return toggleModal("citySelectModal");e.noloading||(t.loading=!0),fetchData("/1.5/props/cities.json",{body:{loc:t.needLoc}},(function(e,i){i.ok&&(t.favCities=t.parseCityList(i.fc),i.cl&&(t.extCitiesCp=i.cl.slice()),t.favCitiesCp=t.favCities.slice(),t.loading=!1,toggleModal("citySelectModal"))}));window.bus.$emit("track-log-event",{e:"open",type:"CitySelect"})})),localStorage.histCities&&(this.histCities=JSON.parse(localStorage.histCities))):console.error("global bus is required!")},watch:{filter:function(e,t){if(e&&e.length>0&&this.extCitiesCp.length){var i=this.extCitiesCp.filter(this.filterFn);this.extCities=this.formatCityList(i,{hide:!1})}else e&&0==e.length&&(this.extCities=this.formatCityList(this.extCitiesCp,{hide:!1}))}},methods:{getCityidx(e){if(!this.userCities||!this.userCities.length)return-1;return this.userCities.findIndex((function(t){return t.o==e.o}))},unSubscribeCity(e,t){t||(t=this.getCityidx(e)),trackEventOnGoogle("citySelectModal","unsubscribe");var i=this;fetchData("/1.5/index/subscribe",{body:{mode:"unsubscribe",city:e}},(function(e,s){console.log(s,e),s.ok?(i.userCities.splice(t,1),i.unSubscribe=!0,s.msg&&window.bus.$emit("flash-message",s.msg)):"Need login"==s.e?document.location.href="/1.5/user/login":window.bus.$emit("flash-message",s.e)}))},subscribeCity(e){trackEventOnGoogle("citySelectModal","subscribe");var t=this;fetchData("/1.5/index/subscribe",{body:{city:e}},(function(i,s){console.log(s,i),s.ok?(console.log("ok"),window.bus.$emit("flash-message",t.$_("Subscribed")),t.userCities.push(e)):s.e&&("Need login"==s.e?document.location.href="/1.5/user/login":window.bus.$emit("flash-message",s.e))}))},filterFn(e){var t=this.filter;if(t){var i=new RegExp(t,"ig");return i.test(e.o)||i.test(e.n)}},parseCityList(e){for(var t=[],i=0;i<e.length;i++){let s=e[i];s.split=!1,0==i&&t.push({split:!0,pn:s.pn,p:s.p,o:s.o,n:s.n}),t.push(s);let a=e[i+1]||{p:s.p,pn:s.pn};s.p!==a.p&&t.push({split:!0,pn:a.pn,p:a.p,o:a.o,n:a.n})}return t},closeCitySelect(){toggleModal("citySelectModal"),window.bus.$emit("city-select-close")},addCityHistory(e,t){var i=-1;return e.forEach(((e,s)=>{e.o==t.o&&(i=s)})),i>-1&&e.splice(i,1),delete t.n,delete t.pn,e.unshift(t),e.length>10&&(e.length=10),e},setCity(e){this.setCurCity&&(this.curCity=e),e.cnty||e.ncity||(e.cnty="Canada"),this.histCities=this.addCityHistory(this.histCities,e),localStorage.histCities=JSON.stringify(this.histCities),window.bus.$emit("set-city",{city:e,doSearch:this.doSearch})},getUsercities(){var e=this;fetchData("/1.5/index/userCities",{},(function(t,i){i.ok&&(e.userCities=i.cities)}))},getProvs(){var e=this;fetchData("/1.5/props/provs.json",{},(function(t,i){i.ok&&(e.provs=i.p)}))},changeProv(){if("CA"==this.prov)return this.favCities=this.favCitiesCp,void(this.extCities=[]);this.favCities=[],this.extCities=[],this.extCitiesCp=[],window.bus.$emit("clear-cache"),this.getCitiesFromProv()},formatCityList(e,t={}){for(var i=[],s={},a=0;a<e.length;a++){var n=e[a];if(!t.hide||"en"===this.dispVar.lang||n.o!=n.n){var o=n.o.charAt(0);s[o]||(s[o]=[]),s[o].push(n)}}for(let e of"ABCDEFGHIGKLMNOPQRSTUVWXYZ")s[e]&&i.push({i:e,l:s[e]});return i},getCitiesFromProv(e){e||(e=this.prov);var t=this;t.loading=!0,window.bus.$emit("clear-cache");var i={p:e,loc:t.needLoc};fetchData("/1.5/props/cities.json",{body:i},(function(e,i){i.ok&&(t.extCitiesCp=i.cl,t.extCities=t.formatCityList(i.cl,{hide:!0}),t.favCities=t.parseCityList(i.fc),t.loading=!1,window.bus.$emit("clear-cache"))}))}},template:'\n  <div class="modal" id="citySelectModal">\n  <header class="bar bar-nav" v-if="!nobar"><a class="icon icon-close pull-right" @click="closeCitySelect()" href="javascript:void 0"></a>\n    <h1 class="title">{{$_(\'Select City\')}}</h1>\n  </header>\n  <div class="content">\n    <div class="table-view-cell provbar" v-if="showProvBar"><span class="blue" @click="setCity({o:null,p:null,cnty:\'Canada\'})">{{$_(\'Canada\')}}</span><span class="blue" @click="setCity({o:null,p:null,cnty: \'United States\'})">{{$_(\'United States\')}}</span><span class="blue" @click="setCity({o:null,p:null,cnty: \'China\'})">{{$_(\'China\')}}</span>\n        <span\n            class="blue" v-for="p in provs" v-if="p.o_ab!=\'CA\'" @click="setCity({o:null, p:p.o, cnty: \'Canada\'})">{{p.n || p.o}}</span><span class="blue" @click="setCity({o:null,p:null,cnty:null, ncity: true})">{{$_(\'No City\')}}</span></div>\n    <div class="filter">\n        <div class="prov"><select v-model="prov" v-on:change="changeProv()"><option v-for="p in provs" v-bind:value="p.o_ab">{{p.n || p.o}}</option></select></div>\n        <div class="input"><span class="desc" v-show="prov == \'CA\'"><i class="fa fa-long-arrow-left"></i>{{$_(\'Select Province to see All Cities\')}}</span><input v-show="prov !== \'CA\'" type="text" v-model="filter" :placeholder="$_(\'Input City\')" /></div>\n    </div>\n    <ul class="table-view" v-show="curCity.o">\n        <li class="table-view-divider">{{$_(\'Current City\')}}</li>\n        <li class="table-view-cell"><a href="javascript:void 0" @click="setCity(curCity)">{{curCity.o}}<span v-show="curCity.o !== curCity.n" :class="{\'right-2\':showSubscribe, \'right\':!showSubscribe}">{{curCity.n}}</span></a></li>\n    </ul>\n    <ul class="table-view" v-show="histCities && histCities.length">\n        <li class="table-view-divider">{{$_(\'History\')}}</li>\n        <li class="table-view-cell" style="padding-right: 0px;padding-bottom:5px;"><span class="subscribed-city-tag" v-for="(city,index) in histCities"><span @click="setCity(city)">{{$_(city.o)}}</span></span>\n        </li>\n    </ul>\n    <ul class="table-view" v-show="showSubscribe && userCities && userCities.length">\n        <li class="table-view-divider">{{$_(\'Subscribed Cities\')}}</li>\n        <li class="table-view-cell" style="padding-right: 0px;padding-bottom:5px;"><span class="subscribed-city-tag" v-for="(city,index) in userCities"><span @click="setCity(city)">{{city.n}}</span><span class="icon icon-close" @click="unSubscribeCity(city,index)"></span></span>\n        </li>\n    </ul>\n    <ul class="table-view" v-show="favCities.length">\n        <li class="table-view-divider">{{$_(\'Popular Cities\')}}</li>\n        <li v-for="city in computedFavCities" :class="{\'table-view-cell\':!city.split, \'table-view-divider cust\':city.split}"><span v-if="city.split">{{city.pn}}</span><span v-if="!city.split"><a class="name" href="javascript:;" @click="setCity(city)">{{city.o}}<span v-show="city.o !== city.n" :class="{\'right-2\':showSubscribe, \'right\':!showSubscribe}">{{city.n}}</span></a>\n          <span v-if="showSubscribe">\n            <span class="right fa fa-bell" v-if="getCityidx(city)>=0" @click="unSubscribeCity(city)"></span>\n            <span class="right fa fa-bell-o" v-else @click="subscribeCity(city)"></span></span>\n          </span>\n        </li>\n    </ul>\n    <ul class="table-view" v-for="idx in extCities">\n        <li class="table-view-divider">{{idx.i}}</li>\n        <li class="table-view-cell" v-for="city in idx.l"><span><a class="name" href="javascript:;" @click="setCity(city)">{{city.o}}<span v-show="city.o !== city.n" :class="{\'right-2\':showSubscribe, \'right\':!showSubscribe}">{{city.n}}</span></a><span v-if="showSubscribe"><span class="right fa fa-bell" v-if="getCityidx(city)>=0" @click="unSubscribeCity(city)"></span>\n          <span class="right fa fa-bell-o" v-else @click="subscribeCity(city)"></span>\n              </span>\n            </span>\n        </li>\n    </ul>\n  </div>\n  <flash-message/>\n</div>\n  '},LangSelectCover={props:{oldVer:{type:Boolean,default:!1},dispVar:{type:Object,default:function(){return{userCity:{o:"Toronto"},languageObj:[]}}}},mounted(){var e=this;window.bus.$on("pagedata-retrieved",(function(t){t.lang&&(e.locale=t.lang);var i="";t.sessionUser&&t.sessionUser.defaultHomeTab&&(i=t.sessionUser.defaultHomeTab),e.defaultHomeTab=i||localStorage.defaultHomeTab||"new",t.coreVer&&(e.is63NewerVer=e.$parent.isNewerVer(t.coreVer,"6.3.1"),e.is65NewerVer=e.$parent.isNewerVer(t.coreVer,"6.4.0"),e.is63NewerVer&&RMSrv.onAppStateChange?RMSrv.onAppStateChange((t=>{e.doCheckSequence()})):e.canRefresh=!0,e.isAndroid&&e.is65NewerVer&&(e.steps=2))})),RMSrv.isIOS()&&(e.steps=2),e.isAndroid=RMSrv.isAndroid(),e.doCheckSequence(),e.showCover()},data:()=>({canContinue:!0,coreVer:"5.2.0",location:!1,locationState:"denied",notification:!1,notificationState:"denied",clickedLocation:0,clickedNotification:0,wordings:["To find nearby properties and schools you need to enable location.","To get latest messages and listing alerts you need to enable notification."],locale:"en",steps:3,curStep:0,homeTabs:[{k:"feed",v:"Feeds",ctx:"discover"},{k:"new",v:"New",ctx:"tag"}],canRefresh:!1,refresh:!1,confirmStep:0,locationInit:!1,notificationInit:!1,is63NewerVer:!1,is65NewerVer:!1,isAndroid:!1,isInitApp:!1}),methods:{showCover(){var e=this;/hideLang=1/.test(document.URL)||(/src=setting/.test(document.URL)&&(e.curStep=1,e.addToStorage("curStep",e.curStep),e.openCover()),e.getFromStorage("initApp",(t=>{var i=0;null==t?(e.readCookie("locale")?(i=1,e.addToStorage("notificationPermission",new Date)):e.isAndroid&&e.addToStorage("notificationPermission",new Date),e.addToStorage("initApp",i)):i=t,e.isInitApp=1!=i,e.getFromStorage("curStep",(t=>{e.isInitApp?(t>0?e.curStep=t:(e.curStep=1,e.addToStorage("curStep",e.curStep)),e.openCover()):null!=t&&t>0&&(e.curStep=t,e.openCover())}))})))},openCover(){setTimeout((function(){toggleModal("coverPageModal","open")}),0)},isAuthedRet:(e="")=>["authorized","granted"].indexOf(e)>-1,doCheckSequence(){var e=this;e.refresh=!1,RMSrv.onReady((()=>{e.checkPermissions("notification",(()=>{e.checkPermissions("location")}))}))},setStatus(e,t,i,s){this[e]=this.isAuthedRet(t),this[e+"State"]=t,"denied"==t&&(s=!0),this[e+"Init"]=!!s,i&&i()},checkPermissions(e,t){var i=this;RMSrv.permissions&&RMSrv.permissions(e,"check",-1,(function(s){if("denied"!=s)return i.setStatus(e,s,t);i.isAndroid&&"notification"==e&&(i.isInitApp?i.setStatus(e,s,t,"init"):i.setStatus(e,"block",t)),i.getFromStorage(e+"Permission",(a=>{null==a?i.setStatus(e,s,t,"init"):i.setStatus(e,s,t)}))}))},setCity(){this.addToStorage("curStep",this.curStep),this.$parent.getCityList("setUserCity")},blockAlert(e,t){var i=this;e=e||"To find nearby houses and schools you need to enable location";var s=this.$_?this.$_:this.$parent.$_;"function"!=typeof s&&(s=e=>e);var a=s(e),n=s("Skip"),o=s("Cancel"),r=r||"";return RMSrv.dialogConfirm(a,(function(e){e+""=="1"?i.next():"function"==typeof t&&t()}),r,[n,o])},confirmNext(e,t){var i=this,s=i.getWording(e,i[e+"State"]);s?i.blockAlert(s):t()},getWording(e,t){var i;return"location"==e?this.isAuthedRet(t)||(i=this.wordings[0]):"notification"==e&&(this.isAuthedRet(t)||(i=this.wordings[1])),i},setLang(e){localStorage.lang=e,this.locale=e,this.addToStorage("curStep",this.curStep),setLoaderVisibility("block");var t="/1.5/settings/lang?l="+e+"&d=";t+=encodeURIComponent("/1.5/index?src=setting"),window.location=t},confirmNextStep(e){var t=this;t.confirmStep=e;var i=2==e?"location":"notification";if(!t[i+"Init"])return t.showRefresh?bus.$emit("flash-message",t.$_(t.$parent.strings.refreshAlert.key)):t[i]?t.next(e):void t.confirmNext(i)},setHomeTab(e){this.defaultHomeTab!=e&&(this.defaultHomeTab=e,this.$forceUpdate(),this.dispVar.isLoggedIn?fetchData("/1.5/settings/user",{body:{defaultHomeTab:e}},(function(e,t){if(e||t.e)return bus.$emit("flash-message",e||t.e)})):localStorage.defaultHomeTab=e)},requestPermissions(e){var t=this;"denied"==t[e+"State"]?RMSrv.permissions&&RMSrv.permissions(e,"request",(function(i){t.addToStorage(e+"Permission",new Date),t.isAndroid&&"denied"==i&&(i="blocked"),t.setStatus(e,i)})):t.openAppSettings()},openAppSettings(){this.refresh=!0,RMSrv.openSettings&&RMSrv.openSettings()},addToStorage(e,t,i){localStorage[e]=t,RMSrv.setItemObj({key:e,value:t,stringify:!0,store:!0},i)},getFromStorage(e,t){var i=this;RMSrv.getItemObj(e,!0,(s=>{if(!s)return t(null);if(s.indexOf("null")>-1&&null==localStorage[e])return t(null);var a=s.match(/\d+/);if(asyncStatus=a?parseInt(a[0],10):null,localStatus=localStorage[e],asyncStatus==localStatus)return t(asyncStatus);t(i.is65NewerVer?asyncStatus:localStatus)}))},next(e){if((e=e||this.confirmStep)>=this.steps)return this.done();setLoaderVisibility("block"),setTimeout((()=>{this.curStep=e+1>this.steps?this.steps:e+1,this.addToStorage("curStep",this.curStep),setLoaderVisibility("none")}),500)},back(e){e>this.steps&&(e=e>=this.steps?this.steps:e),this.curStep=e-1>=1?e-1:1,this.addToStorage("curStep",this.curStep)},handleRedirect(e){var t=this;if("undefined"!=typeof localStorage&&e){localStorage.lastCoverHideDate=new Date,"/1.5/settings/lang?l=";var i="/1.5/settings/lang?l="+e+`&d=${encodeURIComponent("/1.5/user/login#index")}`;if(t.isInitApp){if(t.dispVar.homePageABTest)return t.sendTestInfo(i);window.location=i}else window.location="/1.5/index"}else toggleModal("coverPageModal")},done(){var e=this,t=this.locale;setLoaderVisibility("block"),e.setCookie("locale",t,365),e.addToStorage("curStep",0),e.addToStorage("initApp",1,(function(){setTimeout((()=>{e.handleRedirect(t)}),500)}))},sendTestInfo(e){var t="home";let i=getABTestSeed();this.setCookie(t+"seed",i),axios.put("/abtest",{groupName:t}).then((i=>{(i=i.data).ok&&(checkAndSendLogger(null,{sub:t,act:"abtest",tgp:"hp"+i.group}),"A"==i.group?window.location=e:window.location="/1.5/index")})).catch((e=>{console.error("server-error")}))}},computed:{showRefresh:function(){return this.canRefresh&&this.refresh}},template:'\n  <div>\n    <div style="display:none">\n      <span v-for="w in wordings">{{ $_(w)}}</span>\n    </div>\n    <div class="modal" id="coverPageModal">\n      <div class="content">\n        <header class="bar bar-nav" v-if=\'!isInitApp\'>\n          <a class="icon icon-close pull-right" @click="done()"></a>\n        </header>\n        <div class="title-wrapper">\n          <div class="tl">{{ $_("Setup")}}\n            <span class="desc gray">{{curStep}}/{{steps}}</span>\n          </div>\n        </div>\n        <div class="step-line-wapper">\n          <span v-for=\'i in steps\' :class="{active:i == curStep}"></span>\n        </div>\n        <div class="steps-wrapper">\n          <div class="page" v-show=\'curStep == 1\'>\n            <div class="cata lang">\n              <div class="tl">{{ $_("Language")}}</div>\n              <div class="desc" v-for="lang in dispVar.languageObj" @click="setLang(lang.k)">\n                {{lang.v}}\n                <span class="fa pull-right" :class=\'{"fa-check-circle":locale == lang.k,"fa-circle-thin":locale != lang.k}\'></span>\n              </div>\n            </div>\n            <div class="cata city">\n              <div class="tl">{{ $_("Default City")}}</div>\n              <div class="opts">\n                <div class="desc">\n                  <div>{{dispVar.userCity.n || dispVar.userCity.o}}</div>\n                </div>\n                <div class="switch"><span class="positive" @click="setCity()">{{ $_("Change","lang select")}}</span>\n                </div>\n              </div>\n            </div>\n            <div class="cata home">\n              <div class="tl">{{ $_("Default Homepage Tab")}}</div>\n              <div class="desc" v-for="tab in homeTabs" @click="setHomeTab(tab.k)">\n                {{ $_(tab.v,tab.ctx)}}\n                <span class="fa pull-right" :class=\'{"fa-check-circle":defaultHomeTab == tab.k,"fa-circle-thin":defaultHomeTab != tab.k}\'></span>\n              </div>\n            </div>\n            <div class="btn-box login-wrapper">\n              <span type="button" class="btn btn-block btn-positive full" @click=\'next(1)\'> {{ $_("Continue")}}</span>\n            </div>\n          </div>\n\n          <div class="page" v-show=\'(curStep == 2) && (steps == 3)\'>\n            <div class="cata location">\n              <div class="tl">{{ $_("Turn On Location Permission")}}\n              </div>\n              <div class="desc" v-if=\'showRefresh\' @click="doCheckSequence()">\n                {{ $_("Permissions status updated.")}}\n                <span class="positive pull-right refresh">{{ $_("Refresh")}}</span>\n              </div>\n              <div class="desc" @click="requestPermissions(\'location\')">\n                {{$_(\'ON\')}}\n                <span class="fa pull-right" v-show=\'!showRefresh\' :class=\'{"fa-check-circle":(location && !locationInit),"fa-circle-thin":((!location) || locationInit)}\'></span>\n              </div>\n              <div class="desc" @click="requestPermissions(\'location\')">\n                {{$_(\'OFF\')}}\n                <span class="fa pull-right" v-show=\'!showRefresh\' :class=\'{"fa-check-circle":(!location && !locationInit),"fa-circle-thin":(location || locationInit)}\'></span>\n              </div>\n              <div class="desc gray">{{ $_("We highly recommend you grant permission, as it will be used for a map-based property search.")}} {{ $_("We use GPS to zoom in/out on the user\'s current location and filter properties nearby.")}}</div>\n              <div class="desc gray">{{ $_("The use of your location data is subject to our privacy policy.")}}</div>\n            </div>\n            <div class="btn-box login-wrapper">\n              <span type="button" class="btn btn-block" @click=\'back(2)\'> {{ $_("Back")}}</span>\n              <span type="button" class="btn btn-block btn-positive" :class="{\'disabled\':locationInit}" @click=\'confirmNextStep(2)\'> {{ $_("Continue")}}</span>\n            </div>\n          </div>\n\n          <div class="page" v-show=\'curStep == steps\'>\n            <div class="cata notification">\n              <div class="tl">{{ $_("Turn On Notification Permission")}}\n              </div>\n              <div class="desc" v-if=\'showRefresh\' @click="doCheckSequence()">\n                {{ $_("Permissions status updated.")}}\n                <span class="positive pull-right refresh">{{ $_("Refresh")}}</span>\n              </div>\n              <div class="desc" @click="requestPermissions(\'notification\')">\n                {{$_(\'ON\')}}\n                <span class="fa pull-right" v-show=\'!showRefresh\' :class=\'{"fa-check-circle":(notification && !notificationInit),"fa-circle-thin":((!notification) || notificationInit)}\'></span>\n              </div>\n              <div class="desc" @click="requestPermissions(\'notification\')">\n                {{$_(\'OFF\')}}\n                <span class="fa pull-right" v-show=\'!showRefresh\' :class=\'{"fa-check-circle":(!notification && !notificationInit),"fa-circle-thin":(notification || notificationInit)}\'></span>\n              </div>\n              <div class="desc gray">{{ $_("Enable notification to get timely push notifications for property listings and relevant news.")}} {{ $_("Stay informed and never miss any new listings or status updates like price changes to your favorite listings.")}}</div>\n            </div>\n            <div class="btn-box login-wrapper">\n              <span type="button" class="btn btn-block" @click=\'back(3)\'> {{ $_("Back")}}</span>\n              <span type="button" class="btn btn-block btn-positive" :class="{\'disabled\':notificationInit}" @click=\'confirmNextStep(3)\'> {{ $_("Done")}}</span>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n  '},rmsrvMixins={created:function(){"undefined"!=typeof RMSrv&&null!==RMSrv&&(this.ready=!0)},methods:{trackEventOnGoogle(e,t,i,s){trackEventOnGoogle(e,t,i,s)},exMap(e,t){let i;return this.dispVar.isApp||(document.location.href="/adPage/needAPP"),e.useMlatlng||"N"===e.daddr&&e.lat&&e.lng?i=e.lat+","+e.lng:(i=(e.city_en||e.city||"")+", "+(e.prov_en||e.prov||"")+", "+(e.cnty_en||e.cnty||""),i="N"!==e.daddr?(e.addr||"")+", "+i:i+", "+e.zip),t=(t||this.dispVar.exMapURL)+encodeURIComponent(i),RMSrv.showInBrowser(t)},goBack(){if("nativeMap"==vars.src||"nativeAutocomplete"==vars.src)return window.rmCall(":ctx::cancel");vars.d?document.location.href=vars.d:window.history.back()},sprintf(){var e=arguments,t=1;return e[0].replace(/%((%)|s|d)/g,(function(i){var s=null;if(i[2])s=i[2];else{if(s=e[t],"%d"===i)s=parseFloat(s),isNaN(s)&&(s=0);t++}return s}))},appendLocToUrl(e,t,i){if(null!=t.lat&&null!=t.lng){var s=e.indexOf("?")>0?"&":"?";return e+=s+"loc="+t.lat+","+t.lng}return e},appendCityToUrl(e,t,i={}){if(!t.o)return e;var s=e.indexOf("?")>0?"&":"?";return e+=s+"city="+t.o,t.p&&(e+="&prov="+t.p),t.n&&(e+="&cityName="+t.n),t.pn&&(e+="&provName="+t.pn),t.lat&&(e+="&lat="+t.lat),t.lng&&(e+="&lng="+t.lng),i.saletp&&(e+="&saletp="+i.saletp),null!=i.dom&&(e+="&dom="+i.dom),null!=i.oh&&(e+="&oh="+!0),i.ptype&&(e+="&ptype="+i.ptype),e},appendDomain(e){var t=window.location.href.split("/");return e=t[0]+"//"+t[2]+e},clickedAd(e,t,i,s){var a=e._id;if(e.inapp)return window.location=e.tgt;t&&trackEventOnGoogle(t,"clickPos"+i),a=this.appendDomain("/adJump/"+a),RMSrv.showInBrowser(a)},goTo(e,t={}){if(e.googleCat&&e.googleAction&&trackEventOnGoogle(e.googleCat,e.googleAction),e.t){let t=e.t;"For Rent"==e.t&&(t="Lease");var i=e.cat||"homeTopDrawer";trackEventOnGoogle(i,"open"+t)}var s=e.url,a=e.ipb,n=this;if(s){if(e.login&&!this.dispVar.isLoggedIn)return window.location="/1.5/user/login";if(!this.jumping){if(e.vipplus&&!this.dispVar.isVipPlus)return this.confirmVip();if(e.trebuser&&!this.dispVar.hasAid)return this.confirmTreb();if(e.t,"Agent"==e.t&&!e.direct)return this.dispVar.isLoggedIn?this.dispVar.isProdSales||this.dispVar.marketAdmin?window.location=e.url:null:window.location="/1.5/user/login";if("Services"==e.t)return window.location=s;if(1==a){return e.jumpUrl&&(s=e.jumpUrl+"?url="+encodeURIComponent(e.url)),this.tbrowser(s,{backButton:{image:"back",imagePressed:"back_pressed",align:"left",event:"backPressed"}})}if(3==a)return RMSrv.scanQR("/1.5/iframe?u=");if(4==a)return console.log(s),RMSrv.showInBrowser(s);if(1==e.loc){var o=this.dispVar.userCity;s=this.appendCityToUrl(s,o)}if(e.projQuery){var r=this.dispVar.projLastQuery||{};s+="?";for(let e of["city","prov","mode","tp1"])r[e]&&(s+=e+"="+r[e],s+="&"+e+"Name="+r[e+"Name"],s+="&")}if(1==e.gps){o=this.dispVar.userCity;s=this.appendLocToUrl(s,o)}1==e.loccmty&&(s=this.appendCityToUrl(s,t)),e.tpName&&(s+="&tpName="+this.$_(e.t,e.ctx)),this.jumping=!1,n.isNewerVer(n.dispVar.coreVer,"5.8.0")&&/mapSearch|autocomplete/.test(s)&&!/mode=list/.test(s)||(n.jumping=!0),setTimeout((function(){window.location=s}),10)}}},clearCache(){"undefined"!=typeof RMSrv&&null!==RMSrv&&RMSrv.clearCache&&RMSrv.clearCache()},confirmNoFnLn:function(e,t,i,s){t=t||"To be presented here, please complete your personal profile.";var a=this.$_?this.$_:this.$parent.$_,n=a(t),o=a("Later"),r=a("Do it Now");i=i||"";return RMSrv.dialogConfirm(n,(function(e){e+""=="2"?window.location="/1.5/settings/editProfile":s&&(window.location=s)}),i,[o,r])},confirmSettings:function(e,t){e=e||"To find nearby houses and schools you need to enable location";var i=this.$_?this.$_:this.$parent.$_;"function"!=typeof i&&(i=e=>e);var s=i(e),a=i("Later"),n=i("Go to settings"),o=o||"";return RMSrv.dialogConfirm(s,(function(e){e+""=="2"?RMSrv.openSettings():"function"==typeof t&&t()}),o,[a,n])},confirmNotAvailable(e){e=e||"According to the Real Estate Board notice, the sold price information is open by Oct. 22. We will keep you updated at earliest time possible.";var t=this.$_?this.$_:this.$parent.$_,i=t(e),s=t("I Know"),a=a||"";return RMSrv.dialogConfirm(i,(function(e){}),a,[s])},confirmUpgrade:function(e,t){t=t||"Only available in new version! Upgrade and get more advanced features.";var i=this.$_?this.$_:this.$parent.$_,s=i(t),a=i("Later"),n=i("Upgrade"),o=this.appendDomain("/app-download");return RMSrv.dialogConfirm(s,(function(t){e&&(o+="?lang="+e),t+""=="2"&&RMSrv.closeAndRedirect(o)}),"Upgrade",[a,n])},confirmVip:function(e,t){t=t||"Available only for Premium VIP user! Upgrade and get more advanced features.";var i=this.$_?this.$_:this.$parent.$_,s=i(t),a=i("Later"),n=i("See More");return RMSrv.dialogConfirm(s,(function(e){e+""=="2"&&RMSrv.showInBrowser("https://www.realmaster.ca/membership")}),"VIP",[a,n])},tbrowser:function(e,t={}){var i;i={toolbar:{height:44,color:"#E03131"},closeButton:{image:"close",align:"right",event:"closePressed"},fullscreen:!1},i=Object.assign(i,t),RMSrv.openTBrowser(e,i)}}},PageSpinner={props:{loading:{type:Boolean,default:!1}},data:()=>({}),template:'\n  <div class="overlay loader-wrapper" id="busy-icon" v-show="loading">\n    <div class="loader"></div>\n  </div>\n  '},propItem={props:{prop:{type:Object,default:function(){return{pnSrc:[]}}},loading:{type:Boolean},lang:{type:String},tag:{type:String,default:""},cantClick:{type:Boolean,default:!1}},mounted(){},computed:{showBlur:function(){return!("sold"!=this.tag&&"fast"!=this.tag||!this.prop.login)}},methods:{openDetail:function(e){if(!this.cantClick){var t=/^RM/.test(e.id)?e.id:e._id;openPopup(`/1.5/prop/detail/inapp?id=${t}`,this.$_("RealMaster"))}},getPropDate:e=>"U"==e.status_en?e.sldd:e.mt?e.mt.substr(0,10):"",formatPrice:e=>"number"==typeof e?"$"+(e=Math.ceil(e)).toString().replace(/\B(?=(\d{3})+(?!\d))/g,","):e,popupSrcList(){window.bus.$emit("popup-match-list",this.prop)},cntSrc(e){if(!e)return 0;var t=0;return e.forEach((e=>{t+=e.v.length})),t},formateSrc(e){if(!e||"string"==typeof e)return e;var t=[];return e.forEach((e=>{e.nm?t.push(e.nm):t.push(e)})),t.join(",")},showLpInDelisted(e){return"Delisted"==e.saleTpTag_en?this.formatPrice(e.lp||e.lpr):this.formatPrice(e.sp||e.lp||e.lpr)}},template:'\n    <div class="prop" :class="loading?\'loading\':\'\'" @click="openDetail(prop)" data-sub="open detail" :data-id="prop._id">\n      <div class="detail">\n        <div class="addr one-line" :class=\'{blur:showBlur}\'>\n          <span v-if="prop.addr">{{prop.unt}} {{prop.addr}}</span>\n        </div>\n        <div class="prov">{{prop.city}}, {{prop.prov}}</div>\n        <div class="bdrms" :class=\'{blur:showBlur}\'>\n          <span v-if="prop.bdrms"><span class="fa fa-rmbed"></span> {{prop.bdrms}}{{prop.br_plus?\'+\'+prop.br_plus:\'\'}}</span>\n          <span v-if="prop.rmbthrm || prop.tbthrms || prop.bthrms"><span class="fa fa-rmbath"></span> {{prop.rmbthrm || prop.tbthrms || prop.bthrms}}</span>\n          <span v-if="prop.rmgr||prop.tgr||prop.gr"><span class="fa fa-rmcar"></span> {{prop.rmgr||prop.tgr||prop.gr}}</span>\n        </div>\n        <div v-if="showBlur" class=\'prov\'>{{$_(\'Please login to see this listing!\')}}</div>\n        <div v-else>\n          <div v-if="prop.lp == \'$0\'" class="price">{{$_(\'To Be Negotiated\')}}</div>\n          <div v-else-if="tag == \'loss\'" class="price">\n            <span class=\'black\'>-{{formatPrice(prop.lsp - prop.sp)}}</span>\n            <span v-if="prop.lspDifPct && (tag == \'loss\')" class="price-change">{{Math.round(prop.lspDifPct * 10000)/100}}%</span>\n          </div>\n          <div v-else class="price">\n            <span>{{showLpInDelisted(prop)}}</span>\n            <span v-if="prop.pc && (prop.status == \'A\') && prop.pcts" class="price-change">\n              <span :class="prop.pc > 0?\'plus\':\'mins\'">{{prop.pc>0?\'+\':\'-\'}}</span>\n              <span>{{formatPrice(Math.abs(prop.pc))}}</span>\n            </span>\n            <span v-if="prop.lstStr && prop.lst == \'Sc\'" class="price-change">{{prop.lstStr}}</span>\n            <span v-if="(prop.sp && prop.priceValStrRed) && (tag != \'loss\')" class="price-change sold">{{formatPrice(prop.lp || prop.lpr)}}</span>\n          </div>\n        </div>\n        <div v-if="prop.ohdate" class="oh"><span class="fa fa-rmhistory"></span>{{$_(\'Open House\')}}: {{prop.ohdate}}</div>\n      </div>\n      <div class="img" :class=\'{blur:showBlur}\'>\n        <img class="lazy" :src="prop.thumbUrl || \'/img/no-photo.png\'" onerror="this.src=\'/img/no-photo.png\'" referrerpolicy="same-origin" />\n        <div class="tag" :class="prop.tagColor">\n          <span v-if="prop.ytvid || prop.vurlcn"><span class="fa fa-youtube-play"></span></span>\n          <span v-if="prop.ltp == \'exlisting\'" class="ltp">{{$_(\'Exclusive\')}}</span>\n          <span v-if="prop.ltp == \'assignment\'" class="ltp">{{$_(\'Assignment\')}}</span>\n          <span v-if="prop.ltp == \'rent\'" class="ltp">{{$_(\'Rental\')}}</span>\n          <span v-if="prop.type" class="type">{{prop.type}}</span>\n          <span>{{prop.saleTpTag || prop.lstStr}}</span>\n        </div>\n        <div v-if="prop.sldDom >= 0" class="dom">{{prop.sldDom}} {{$_(\'days\')}}</div>\n        \x3c!--div v-else class="dom">{{getPropDate(prop)}}</div--\x3e\n      </div>\n      <div v-if="prop.pnSrc && prop.pnSrc.length>0" class=\'match\' @click.stop=\'popupSrcList(prop)\' data-sub="feeds pnsrc" :data-id="prop._id">\n        <span class="fa fa-radar"></span>\n        <span class="pnSrc" v-if="src = prop.pnSrc[0]">\n        <span class=\'name\'>{{src.transK}} </span>\n          {{formateSrc(src.v)}}\n        </span>\n        <span class="count">{{cntSrc(prop.pnSrc)}}</span>\n        <span class="fa fa-caret-down"></span>\n      </div>\n    </div>\n  '},Caret={props:["changeval"],data:()=>({tp:null}),mounted(){},methods:{getIconClass:function(){if("number"==typeof this.changeval){if(this.changeval>0)var e="up";else if(this.changeval<0)e="down";return`fa-caret-${e}`}}},template:'\n  <span class="caret fa" :class="getIconClass()"></span>\n  '},keyFacts={props:["cmty"],beforeMount(){},mounted(){},components:{Caret:Caret},methods:{displayMomYoY:e=>"number"!=typeof e?"N/A":`${Math.abs(e.toFixed(2))}%`,displayPrice(){return this.cmty.avgP?`$${this.cmty.avgP}`:"N/A"}},template:'\n  <div class="row">\n    <div class="data">\n      <div class="val price">{{displayPrice()}}</div>\n      <div class="nm">{{$_(\'Avg. Price\')}}</div>\n    </div>\n    <div class="data">\n      <Caret :changeval="cmty.mom"></Caret>\n      <div class="val">{{displayMomYoY(cmty.mom)}}</div>\n      <div class="nm">{{$_(\'MoM\')}}</div>\n    </div>\n    <div class="data">\n      <Caret :changeval="cmty.yoy"></Caret>\n      <div class="val">{{displayMomYoY(cmty.yoy)}}</div>\n      <div class="nm">{{$_(\'YoY\')}}</div>\n    </div>\n  </div>\n  '},dailyFeeds={props:["cmty","form"],mounted(){},components:{keyFacts:keyFacts},methods:{gotoStatPage(){var{city_en:e,pr_en:t,nm:i}=this.cmty;openContent(`/1.5/prop/stats?city=${e}&prov=${t}&cmty=${i}&d=/1.5/landlord/owners&isPopup=1&itvl=M`,{toolbar:!1})}},template:'\n    <div class="cmty-stats">\n      <div class="tl">{{cmty.nm}}</div>\n      <div class="sub">{{$_(\'Based on your subscription\')}}</div>\n      <key-facts :cmty="cmty"></key-facts>\n    </div>\n    <div class="monthly-stats" data-sub="daily feeds go trends" @click="gotoStatPage()">\n      <span>{{cmty.tsf}} - {{cmty.tst}}</span>\n      <span>{{$_(\'Trends\')}}<span class="icon icon-right-nav"></span></span>\n    </div>\n  '},MatchListPopup={props:{dispVar:{type:Object,default:function(){return{}}},log:{type:Object,default:function(){return{src:[]}}}},data:()=>({savesUrl:{Saved:"properties","Saved Search":"searches",Community:"communities",Location:"locations"}}),mounted(){if(window.bus);else console.error("global bus is required!")},methods:{toggleModal(){this.$parent.clickMask()},goEdit(e){var t="";"Building"==e&&(e="Location"),t="Showing"==e?"/1.5/showing":`/1.5/saves/${this.savesUrl[e]}`,t+="?d=/home/<USER>",location.href=t}},template:"\n<div id='MatchListPopup' class='modal modal-60pc'>\n  <header class=\"bar bar-nav\">\n    {{$_('Watchlist')}}\n    <span class=\"icon icon-close pull-right\" @click='toggleModal()'></span>\n  </header>\n  <div class='content'>\n    <ul class=\"table-view\" v-for=\"(log,idx) in log.pnSrc\">\n      <div class='tag'>\n        <span>{{log.transK}}</span>\n        <span class='pull-right link' @click='goEdit(log.k)'>{{$_('EDIT')}}</span>\n      </div>\n      <li class='folder' v-if='typeof(log.v)==\"string\"'>{{log.v}}</li>\n      <li v-for=\"(v,idx) in log.v\" class='folder' v-else>\n        <div v-if='v.nm'>\n         {{v.nm}}\n         <div class='clnt' v-show='v.cNm'>\n          <span class='sprite16-18 sprite16-4-6'></span>\n          {{v.cNm}}\n         </div>\n        </div>\n        <div v-else>{{v}}</div>\n      </li>\n    </ul>\n  </div>\n</div>\n"},checkNotification={props:{className:{type:String,default:""}},data:()=>({isOpenNotify:!0,hasGoogleService:!0,canRefresh:!1,refresh:!1,dispVar:{isEmailVerified:!0}}),mounted(){if(window.bus){var e=this;window.bus.$on("pagedata-retrieved",(function(t){if(e.dispVar=Object.assign(e.dispVar,t),t.coreVer){var i=e.$parent.isNewerVer(t.coreVer,"6.3.1");RMSrv.hasGoogleService&&RMSrv.hasGoogleService((function(t){e.hasGoogleService=t,1==t&&(e.checkNotify(),i&&RMSrv.onAppStateChange?RMSrv.onAppStateChange((t=>e.checkNotify())):e.canRefresh=!0)}))}}))}else console.error("global bus is required!")},computed:{showRefresh:function(){return this.canRefresh&&this.refresh},computedHasIssueReceivePN(){return!this.dispVar.isEmailVerified||!this.hasGoogleService||!this.isOpenNotify}},methods:{isAuthedRet:(e="")=>["authorized","granted"].indexOf(e)>-1,checkNotify(){var e=this;this.refresh=!1,RMSrv.onReady((()=>{RMSrv.permissions("notification","check",-1,(function(t){e.isOpenNotify=e.isAuthedRet(t)}))}))},openSetting(){this.refresh=!0,RMSrv.openSettings&&RMSrv.openSettings()},openVerifyPopup(){var e=this;RMSrv.getPageContentIframe("/1.5/settings/verify?verify=v","#callBackString",{transparent:!0},(function(t){if(":cancel"!=t)try{JSON.parse(t).emlV&&(e.dispVar.isEmailVerified=!0)}catch(e){console.error(e)}}))}},template:'\n<div class="check-notification" v-if=\'computedHasIssueReceivePN\' :class=\'className\'>\n  <span class="status fa fa-exclamation-circle" style="color: rgb(255, 205, 0); display: inline;"></span>\n  <span class="field">\n    <span class="field-name" v-if=\'!dispVar.isEmailVerified\'> {{$_(\'Email is not verified.\')}}\n      <span class="explain" @click="openVerifyPopup()">{{$_(\'Verify\')}}</span>\n    </span>\n    <span class="field-name" v-else-if=\'!hasGoogleService\'> {{$_(\'Install Google Play to get listing updates.\')}}\n    </span>\n    <span class="field-name" v-else> {{$_(\'Turn on notifications to get listing updates.\')}}\n      <span class="explain" @click="checkNotify()" v-if=\'showRefresh\'>{{$_("Refresh")}}</span>\n      <span class="explain" @click="openSetting()" v-else>{{$_(\'Notification Settings\')}}</span>\n    </span>\n  </span>\n</div>\n  '},watchingPopup={props:{watchingType:{type:String,default:""},dataFrom:{type:String,default:""},dispVar:{default:function(){return{isEmailVerified:!1}}}},data:()=>({ranges:[0,100,300,500],WATCH_TYPES:WATCH_TYPES,WATCH_EVENTS:WATCH_EVENTS,strings:{location:{key:"Location Notification"},cmty:{key:"Community Notification"}},watchingDefault:{range:0,propType:[],watchEvent:[]},watching:{},prop:{showAddr:"",cmty:"",watching:{}},idx:0,hasPropWatching:!1,isNotOpenNotify:!1,hasGoogleService:!1}),components:{checkNotification:checkNotification},mounted(){if(window.bus){var e=this;window.bus.$on("watch-item-change",(function({item:t,idx:i}){e.prop=t,e.idx=i,e.initWatching(e.watchingType)}))}else console.error("global bus is required!")},methods:{close(){this.watching={},bus.$emit("close-watch-popup",{})},getNotificationType(){return"cmty"==this.watchingType?this.strings.cmty.key:this.strings.location.key},getAddress(){return"cmty"==this.watchingType?`${this.prop.cmty||this.prop.nm}, ${this.prop.city}`:this.prop.nm},setFeature(e,t){if("range"==e)this.watching.range=t;else{var i=this.watching[e].indexOf(t);if(i>-1){if(1==this.watching[e].length)return bus.$emit("flash-message",this.$_("Choose at least one of each"));this.watching[e].splice(i,1)}else this.watching[e].push(t)}},getDisableOKBtn(){for(var e in this.watching)if(0==this.watching[e].length)return!0;return!1},getDisableUnwatchBtn(){return!this.hasPropWatching},watchProps(){var e,t=this;"cmty"==this.dataFrom&&"cmty"==this.watchingType?((e={uaddr:t.prop._id,tp:this.watchingType}).nm=e.cmty=t.prop.nm,t.prop.pos&&(e.loc=t.prop.pos)):e=this.prop,e=Object.assign(e,t.watching);var i=[];for(var s in this.watching){var a=this.watching[s];"range"!=s&&(Array.isArray(a)&&(a=a.join(" • ")),i.push(a))}this.prop.display=i.join(" • "),this.prop.watching=this.watching,this.hasPropWatching=!0,this.close(),bus.$emit("watch-prop",{unwatch:!1,prop:this.prop,idx:this.idx}),this.postWatch(e)},postWatch(e){e.uaddr&&getStandardFunctionCaller()("watchLocation",e,((e,t)=>{e?bus.$emit("flash-message",e.toString()):(bus.$emit("flash-message",t),trackEventOnGoogle("saves","cmty"==this.watchingType?"cmty":"location","editWatch"))}))},unwatch(){var e={tp:this.watchingType};this.prop._id.indexOf("CA")>-1?e.uaddr=this.prop._id:e.uaddr=this.prop.uaddr,e.unwatch=!0,this.hasPropWatching=!1,this.postWatch(e),this.close(),this.initWatching(this.watchingType,!0),bus.$emit("watch-prop",{unwatch:!0,prop:this.prop,idx:this.idx})},initWatching(e,t){this.watching=JSON.parse(JSON.stringify(this.watchingDefault));var i=this.prop.watching;this.hasPropWatching=!t&&(i&&Object.keys(i).length>0),this.hasPropWatching?this.watching=Object.assign({},i):"cmty"==e?delete this.watching.range:"building"==e&&delete this.watching.propType}},template:'\n<div class="modal" id="watchingModal" v-cloak="">\n  <div class="bar bar-standard bar-footer back">\n    <button class="btn btn-half btn-nooutline btn-fill btn-sharp red" :disabled=\'getDisableOKBtn()\' @click="watchProps()">\n      {{$_(\'Save\')}}\n    </button>\n    <button class="btn btn-half btn-nooutline btn-fill btn-sharp gray" :disabled=\'getDisableUnwatchBtn()\' @click="unwatch()">\n      {{$_(\'Unwatch\')}}\n    </button>\n  </div>\n  <div style="background: #f1f1f1;">\n    <div class="wrapper">\n      <ul class="slideMenu table-view options">\n        <li class="table-view-cell">\n          <a>\n            <div class="name" style=\'font-size: 17px;\'>{{$_(getNotificationType())}}</div>\n            <span class="saved-searches-desc">{{getAddress()}}</span>\n          </a>\n          <span class=\'close icon icon-close\' @click=\'close()\'></span>\n        </li>\n        <li class="table-view-cell has-btn-grp" v-if="watching.range != undefined">\n          <div class="tl">\n            <span class="name">{{$_(\'Distance\')}}\n              <span class="saved-searches-desc" v-if=\'watching.range != 0\'>({{$_("Within")}} {{prop.cmty}})</span>\n            </span>\n            <span class="input-desc" v-show=\'watching.range == 0 && watchingType == "building"\'>{{$_(\'Units in this building\')}}</span>\n            <span class="input-desc" v-show=\'watching.range == 0 && watchingType == "location"\'>{{$_(\'Only this property\')}}</span>\n          </div>\n          <div class="btn-sets">\n            <a class="btn btn-default" v-for="range in ranges" href="javascript:void 0"\n              @click="setFeature(\'range\',range)"\n              :class="{active:watching.range == range}">{{range}}{{$_(\'m\',\'distance\')}}</a>\n          </div>\n        </li>\n        <li class="table-view-cell has-btn-grp" v-if="watching.propType">\n          <div class="tl">\n            <span class="name">{{$_(\'Prop Type\')}}</span><span class="input-desc">{{$_(\'Multi-Select\')}}</span>\n          </div>\n          <div class="btn-sets">\n            <a class="btn btn-default" v-for="type in WATCH_TYPES" href="javascript:void 0"\n              @click="setFeature(\'propType\',type)"\n              :class="{active:watching.propType.indexOf(type) > -1}">{{$_(type)}}</a>\n          </div>\n        </li>\n        <li class="table-view-cell has-btn-grp" v-if="watching.watchEvent">\n          <div class="tl">\n            <span class="name">{{$_(\'Events\')}}</span><span class="input-desc">{{$_(\'Multi-Select\')}}</span>\n          </div>\n          <div class="btn-sets">\n            <a class="btn btn-default" href="javascript:void 0" @click="setFeature(\'watchEvent\',event)"\n              v-for="event of WATCH_EVENTS" :class="{active:watching.watchEvent.indexOf(event) > -1}">{{$_(event)}}</a>\n          </div>\n        </li>\n        <check-notification></check-notification>\n      </ul>\n    </div>\n  </div>\n  <div style="display:none">\n    <span v-for="(v,k) of strings">{{$_(v.key)}}</span>\n  </div>\n</div>\n  '},indexHeader={data:()=>({datas:["sessionUser","hasFollowedVipRealtor","userCity","isLoggedIn","isRealtor","isVipPlus","hasAid","lang","isApp","isCip","coreVer","projLastQuery","languageObj","isEmailVerified","homePageABTest"],dispVar:{lang:"zh",userCity:{o:"Toronto",n:"多伦多"}},nativeSearchClicked:!1,isNativeSearch:!1,focused:!1,userForm:{},signupTitle:"",feedurl:"/1.5/form/forminput",hasWechat:!1,strings:{refreshAlert:{key:"Please refresh first."}}}),mounted(){function e(e){var t=(e=(e||navigator.userAgent).toLowerCase()).match(/android\s([0-9\.]*)/);return!!t&&t[1]}this.$getTranslate(this);var t=this,i=window.bus;e()&&parseFloat(e())<4.4&&(t.oldVerBrowser=!0),/mls/.test(location.href)&&(t.datas=t.datas.concat(["savedSearches","favCmtys","propTags","edmDetail"])),/rm/.test(location.href)&&(t.datas=t.datas.concat(["forumAdmin","reqHost","shareHostNameCn","userGroups","isAdmin"])),t.getPageData(t.datas,{},!0),i.$on("pagedata-retrieved",(function(e){if(t.dispVar=Object.assign(t.dispVar,e),t.isNativeSearch=t.isNewerVer(t.dispVar.coreVer,"6.0.1"),t.hasDeviceId=t.isNewerVer(t.dispVar.coreVer,"6.1.5"),!t.updatedPN&&e.updatePN&&localStorage.pn&&t.postPN(),e.userCity&&(t.rcmdCity={o:e.userCity.o,n:e.userCity.n,lat:e.userCity.lat,lng:e.userCity.lng,p:e.userCity.p},e.userCity.cnt&&(t.userCityCnt=e.userCity.cnt)),t.dispVar.sessionUser&&t.dispVar.sessionUser.eml){for(let e of["nm","eml","mbl","fnm"])t.userForm[e]=t.dispVar.sessionUser[e];t.userForm.nm&&!t.userForm.fnm||(t.userForm.nm=t.userForm.fnm||e.sessionUser.nm_en||e.sessionUser.nm_zh)}t.recordEmlAndTime(),e.lang&&RMSrv.setAppLang&&t.dispVar.lang&&"function"==typeof RMSrv.onReady&&RMSrv.onReady((()=>{t.hasDeviceId&&RMSrv.getUniqueId((e=>{e&&e.length>5&&t.logUserId(e)})),RMSrv.hasWechat&&RMSrv.hasWechat((e=>{null!=e&&(t.logUserHasWechat(e),t.hasWechat=e,e||(t.hideElemById("#banners"),t.hideElemBySelectors("#indexAd > div.sp-holder  div.img > div"),t.hideDrawAndMove(),setTimeout((()=>{t.dispVar.isVipPlus||(t.hideElemById("#realtorTodos"),t.hideElemById("#promotionsMarketing"))}),100)))})),RMSrv.hasGoogleService&&RMSrv.hasGoogleService((e=>{var i="#drawers > div.swiper-wrapper  a.drawer.link-transit",s=document.querySelector(i);!e&&s&&(t.hideElemBySelectors(i),t.hideElemBySelectors("#drawers > div.swiper-wrapper  a.drawer.link-co-ophouse"))}));var e=window.location.href,i=t.extractDomain(e);RMSrv.setCookie&&RMSrv.setCookie(),RMSrv.action("pushToken"),RMSrv.setAppLang(t.dispVar.lang),RMSrv.setItemObj({key:"lastLoadDomain",value:i,stringify:!1,store:!1},(()=>{})),RMSrv.refreshSystemValue&&RMSrv.refreshSystemValue(),RMSrv.removeItemObj&&!window.localStorage.lastRMMapPosTs&&(window.localStorage.lastRMMapPosTs="202004230653",RMSrv.removeItemObj("lastMapPosition"))})),t.inited||t.initIndexData()})),i.$on("index-redirect-goto",(function(e){t.goTo(e)})),i.$on("prop-changed",(function(e){if(i=/^RM/.test(e.id)?e.id:e._id,!t.isNewerVer(t.dispVar.coreVer,"5.3.0"))return window.location="/1.5/search/prop?d=/1.5/index&id="+i;var i,s={hide:!1,title:t.$_("RealMaster")},a="/1.5/prop/detail/inapp?lang="+t.dispVar.lang+"&id="+i;a=t.appendDomain(a),RMSrv.getPageContent(a,"#callBackString",s,(function(e){if(":cancel"!=e){if(/^redirect/.test(e))return window.location=e.split("redirect:")[1];try{if(/^cmd-redirect:/.test(e)){var t=e.split("cmd-redirect:")[1];return window.location=t}window.location="/1.5/mapSearch?d=/1.5/index&"+e}catch(e){console.error(e)}}}))})),i.$on("set-city",(function(e){var i="",s={city:e.city};i="subscribe"==t.mode?"/1.5/index/subscribe":"/1.5/settings/city",axios.post(i,s).then((e=>{(e=e.data).ok?(toggleModal("citySelectModal"),e.msg&&window.bus.$emit("flash-message",e.msg),setLoaderVisibility("block"),setTimeout((()=>{location.reload()}),500)):window.bus.$emit("flash-message",e.e)})).catch((e=>{console.error("server-error")}))}))},methods:{openCamera(){var e,t=-1,i=this;RMSrv.scanQR((function(s){if(s&&(t=s.indexOf("/s/"))>0)return e=s.substr(t),openPopup(e,i.$_("RealMaster"));s&&/[1.5|qrcode]/.test(s)?(t=s.indexOf("/qrcode")>0?s.indexOf("/qrcode"):s.indexOf("/1.5/"),e=s.substr(t).replace("share=1","").replace("inFrame=1","")):e="/qrcode/act404",window.location.href=e}))},getTranslate:function(e){return TRANSLATES[e]||e},hideElemById(e){var t=document.querySelector(e);t&&t.remove()},hideDrawAndMove(){var e="#drawers > div.swiper-wrapper  a.drawer.link-agent";document.querySelector(e)&&(this.hideElemBySelectors(e),this.hideElemBySelectors("#drawers > div.swiper-wrapper  a.drawer.link-yellowpage"))},appendEmptyPlaceHolder(){let e=document.querySelector("#drawers > div.swiper-wrapper  .wrapper.newDrawers3");if(e)for(let t=0;t<=1;t++){let t=document.createElement("a");t.classList.add("drawer"),e.append(t)}},hideElemBySelectors(e){var t=document.querySelectorAll(e);if(t&&t.length)for(let e of t)e.remove()},logUserHasWechat(e){var t=this;t.dispVar.isLoggedIn&&(t.loggedWechat||(t.loggedWechat=!0,axios.post("/loguserwechat",{hasWechat:e}).then((e=>{(e=e.data).ok})).catch((e=>{}))))},logUserId(e){this.dispVar.isLoggedIn&&axios.post("/matchuser",{id:e}).then((e=>{(e=e.data).ok&&console.log(e)})).catch((e=>{}))},onClickSearchBar(){if(!this.nativeSearchClicked){var e=this.dispVar.userCity.o,t=this.dispVar.userCity.p,i=this.dispVar.lang||"en";this.nativeSearchClicked=!0,this.goTo({url:"/1.5/autocomplete?referer=index&city="+e+"&prov="+t+"&lang="+i}),setTimeout((()=>{this.nativeSearchClicked=!1}),1e3)}},extractDomain:e=>(e.indexOf("//")>-1?e.split("/")[2]:e.split("/")[0]).split(":")[0].split("?")[0],postPN(){if(localStorage.pn&&!this.updatedPN){var e=this;e.updatedPN=!0,axios.post("/1.5/user/updPn",{pn:localStorage.pn}).then((t=>{(t=t.data).r||(console.error(t.e),e.updatedPN=!1)})).catch((e=>{console.error("server-error")}))}},unsubscribe(){trackEventOnGoogle("homeSubscribedCities","unsubscribe");var e=this;axios.post("/1.5/index/subscribe",{mode:"unsubscribe",idx:this.cityIdx}).then((t=>{(t=t.data).ok?(null!=t.cnt?e.userCityCnt=t.cnt:e.userCityCnt-=1,t.msg&&window.bus.$emit("flash-message",t.msg),e.cityIdx=0):window.bus.$emit("flash-message",t.e)})).catch((e=>{console.error("server-error")}))},initIndexData(){this.inited=!0,this.hist=("undefined"!=typeof indexHistCtrl&&null!==indexHistCtrl?indexHistCtrl.getHist():void 0)||[]},getCityList(e){let t="homeTopBar";"subscribe"==e&&(t="homeSubscribedCities",window.bus.$emit("select-city",{noloading:!0}),this.dispVar.isLoggedIn||(window.location="/1.5/user/login")),this.mode=e,window.bus.$emit("select-city",{noloading:!0}),trackEventOnGoogle(t,"selectCity")},recordEmlAndTime(){this.dispVar.sessionUser&&RMSrv.getItemObj("recordEml",!0,(e=>{e&&e.length||fetchData("/1.5/user/encryptEml",{body:{}},(function(e,t){if(1==t.ok&&t.encryptedEml){let e={eml:t.encryptedEml,ts:new Date};RMSrv.setItemObj({key:"recordEml",value:e,stringify:!0,store:!0})}}))}))}},computed:{}};function showVipInfo(){RMSrv.showInBrowser("https://www.realmaster.ca/membership")}function changeTab(e){for(var t=document.getElementsByClassName("promotionsTl"),i=0;i<t.length;i++)t[i].id=="title-"+e?t[i].classList.add("active"):t[i].classList.remove("active");var s=document.getElementsByClassName("promotionsList");for(i=0;i<s.length;i++)s[i].id==e?s[i].classList.add("active"):s[i].classList.remove("active")}function hideAlert(){if(window.angular){var e=document.querySelector("[ng-controller=ctrlNotifications]"),t=window.angular.element(e).scope();t&&(t.showAppUpgrade=!1,t.$apply())}else{var i=document.getElementById("alert-window-bg");i&&(i.style.display="none")}}function closeApp(){RMSrv&&RMSrv.exitApp?RMSrv.exitApp():hideAlert()}function openDownloadLink(e){"android"===document.getElementById("currentOS").value&&RMSrv.hasGoogleService&&RMSrv.hasGoogleService((function(e){e&&RMSrv.showInBrowser("https://play.google.com/store/apps/details?id=com.realmaster")})),RMSrv.showInBrowser(e)}function swipeTodos(){new Swiper(".todoSwiper",{pagination:{el:".todoSwiperPagination",type:"fraction"}})}function swipeBanners(){new Swiper("#banners",{autoplay:{delay:5e3,disableOnInteraction:!1},pagination:{el:".bannerPagination",clickable:!0}})}function swipeNews(){new Swiper("#news",{loop:!0,autoplay:{delay:3e3,disableOnInteraction:!1},pagination:{el:".newsPagination",clickable:!0}})}function swipeDrawers(){new Swiper("#drawers",{loop:!1,pagination:{el:".drawersPagination",clickable:!0}})}function swipeAssigment(){new Swiper("#trustedAssign",{loop:!0,autoplay:{delay:3e3,disableOnInteraction:!1},pagination:{el:".assignPagination",clickable:!0},slidesPerView:2,loopedSlides:2,spaceBetween:10})}function swipePreCon(){new Swiper("#preCon",{loop:!0,autoplay:{delay:3e3,disableOnInteraction:!1},pagination:{el:".preConPagination",clickable:!0},slidesPerView:2,loopedSlides:4,spaceBetween:10,loopAdditionalSlides:2})}function toggleHome(e){gotoLink("/home/"+e,{key:"homeSwitch",val:e})}function calcSpWraper(){if(document.getElementsByClassName("sp-wrapper").length){var e=parseInt((window.innerWidth-30)/2.05),t=e*document.querySelectorAll(".sp").length;document.getElementsByClassName("sp-wrapper")[0].style.width=t+"px";var s=document.getElementsByClassName("sp");for(i=0;i<s.length;i++)s[i].style.width=e+"px"}}function calcMktWraper(){if(document.getElementsByClassName("mktrcmd").length){var e=parseInt((window.innerWidth-30)/2.05),t=e*document.querySelectorAll(".mkt").length;document.getElementsByClassName("mktrcmd")[0].style.width=t+"px";var s=document.getElementsByClassName("mkt");for(i=0;i<s.length;i++)s[i].style.width=e+"px"}}function showMoreDrawers(){let e,t=document.querySelector("#drawers-wrapper");e=t.classList.contains("limited-height")?"down":"up",t.classList.toggle("limited-height"),document.querySelector("#moreDrawers .fa-home-arrow-up").classList.toggle("hide"),document.querySelector("#moreDrawers .fa-home-arrow-down").classList.toggle("hide"),checkAndSendLogger(null,{sub:"mls",query:{direction:e},act:"toggle drawer"})}function addDrewersEventListener(e){e.ontouchstart=function(e){e.preventDefault(),e.changedTouches[0].pageY},e.ontouchend=function(e){e.preventDefault(),e.changedTouches[0].pageY,showMoreDrawers()},e.onclick=function(e){e.preventDefault(),showMoreDrawers()}}(app=Vue.createApp(indexHeader)).component("city-select-modal",CitySelectModal),app.component("lang-select-cover",LangSelectCover),app.component("flash-message",flashMessage),app.mixin(rmsrvMixins),app.mixin(pageDataMixins),trans.install(app,{ref:Vue.ref}),app.mount("#index-header"),document.addEventListener("DOMContentLoaded",(function(){})),document.addEventListener("DOMContentLoaded",(function(e){setTimeout((function(){swipeBanners(),document.querySelector(".newsPagination")&&swipeNews(),swipeTodos(),calcSpWraper(),swipeAssigment(),document.querySelector(".mls")&&swipePreCon(),document.querySelector("#moreDrawers")&&addDrewersEventListener(document.querySelector("#moreDrawers"))}),300)})),window.addEventListener("click",checkAndSendLogger),ONE_DAY=864e5;var app,CHECK_INVIEWPORT_GAP=82,discover={data:()=>({fixDate:!1,fixTabs:!1,page:0,waiting:!1,hasMoreProps:!0,loadMoreEdmProps:!0,loading:!1,scrollLoading:!1,propTag:{k:"ts",v:"new"},lastPropTag:{k:"ts",v:"new"},propTags:[],selectedCmty:{},selectedCmtyIdx:null,cmtys:[],cmtyProps:[],cmtyPropsObj:{},sortedDates:[],savedSearches:{},userCity:{},edmLogs:[],curFixedEdmIndex:0,curDisPlayEdmDate:"",curDate:null,curCollEdmDate:null,feedDates:[],lastScrollTop:"",scrollDirection:"",initMiddlePartheight:!1,showPriceChange:!0,dispVar:{lang:"en",edmDetail:{hasNewChange:!1,mlsMt:""}},curFixedEdmGroupIndex:0,feedTags:["All","Homes","Searches","Communities","Locations"],activeFeedTag:"All",feedTagChanged:!1,currentEdmLogProp:{},showMask:!1,watchingType:"cmty",agentList:[],upcomingList:[],completedList:[],completedAllList:[],buylist:[],showingTag:"upcoming",showingTags:["upcoming","Completed","Buylist"],timer:null,reloadTime:0,hasNewFeed:!1,updateViewStatus:!1,propList:[],buylistMore:!1,completedMore:!1}),computed:{showCmtyCard(){return"feed"!=this.propTag.k&&!(!this.selectedCmty._id||!(this.selectedCmty.avgP||this.selectedCmty.mom||this.selectedCmty.yoy))}},mounted(){this.$getTranslate(this);var e=this;bus.$on("pagedata-retrieved",(function(t){e.dispVar=Object.assign(e.dispVar,t),e.userCity=t.userCity,e.savedSearches=t.savedSearches,e.propTags=Object.assign([{k:"feed",desc:"feeds"}],t.propTags),e.cmtys=t.favCmtys;var i="";t.sessionUser&&t.sessionUser.defaultHomeTab?i=t.sessionUser.defaultHomeTab:localStorage.defaultHomeTab&&(i=localStorage.defaultHomeTab),"feed"==i?(e.propTag=e.lastPropTag={k:i},e.getEdmProps()):e.getPropsForTag(),t.isRealtor&&e.feedTags.push("Showings"),t.edmDetail.hasNewChange&&(e.hasNewFeed=!0,e.alertNewFeed()),t.isLoggedIn&&e.getAgentList()})),e.scrollElement=document.getElementById("content"),e.scrollElement.addEventListener("scroll",(function(){e.handleLoadMore()})),bus.$on("popup-match-list",(function(t){e.currentEdmLogProp=t,e.showMask=!0,e.toggleModal("MatchListPopup","open")})),bus.$on("close-watch-popup",(function(){e.closePopup()})),bus.$on("watch-prop",(function({unwatch:t,prop:i}){var s=e.cmtys.findIndex((e=>e._id==i._id));if(t)if(e.cmtys.splice(s,1),e.cmtys.length){var a=s-1<0?s-1:0;e.selectCmty(e.cmtys[a],a)}else e.selectCmty({},0,!0);else e.cmtys.splice(s,1,i)})),this.timer&&(e.selectTag(e.propTag,1),e.clearTimer()),e.reloadTimer(),this.appStateCallback=t=>{t&&"active"==t.nextAppState?(RMSrv.action("pushToken"),e.reloadTime=(new Date).getTime(),e.selectTag(e.propTag,1),e.getEdmLastUpdataTs(),RMSrv.setCookie&&RMSrv.setCookie(),e.reloadTimer()):e.clearTimer()},RMSrv.onAppStateChange&&RMSrv.onAppStateChange(this.appStateCallback)},watch:{fixTabs(e){e&&"feed"!=this.propTag.k&&this.scrollBar()}},methods:{openDetail:function(e){var t=/^RM/.test(e.id)?e.id:e._id;openPopup(`/1.5/prop/detail/inapp?id=${t}`,this.$_("RealMaster"))},showAgentWesite(e){var t=`/1.5/wesite/${e._id}?inFrame=1`;RMSrv.openTBrowser(t)},selectShowingTag(e){this.showingTag=e,document.querySelector("#home-showing").scrollIntoView(!0),"Buylist"==e&&calcMktWraper()},getAgentList(){var e=this;fetchData("/1.5/crm/linkedAgents",{body:{}},(function(t,i){e.loading=!1,1==i.ok&&i.list.length>0&&(e.agentList=i.list,e.getShowingList(),e.getBuyList())}))},crmOpenShowing(e){if(e._id){var t="/1.5/showing/detail/share?d=/1.5/index&showingId="+e._id+"&isPopup=1&lang="+this.dispVar.lang;openPopup(t,"",!0)}},getDay:e=>["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"][new Date(e.replace(/-/g,"/").split(" ")[0]).getDay()],formatHM:function(e){return e?(e.getHours()<10?"0"+e.getHours():e.getHours())+":"+(e.getMinutes()<10?"0"+e.getMinutes():e.getMinutes()):""},date2Num:function(e){return e?""+e.getFullYear()+(e.getMonth()+1<10?"0"+(e.getMonth()+1):e.getMonth()+1)+(e.getDate()<10?"0"+e.getDate():e.getDate()):""},getShowingList(){var e,t=new Date;e=t.setDate(t.getDate()-180),t=new Date(e);var i={today:this.date2Num(new Date),now:this.formatHM(new Date)},s=this;s.loading=!0,fetchData("/1.5/showing/client",{body:i},(function(e,t){s.loading=!1,t&&1==t.ok&&t.list&&t.list.list&&(s.upcomingList=t.list.list,s.upcomingList=s.formatShowingData(s.upcomingList),t.list.past&&(s.completedList=s.formatShowingData(t.list.past),s.completedList.length>5&&(s.completedAllList=s.completedList,s.completedList=s.completedList.slice(0,5),s.completedMore=!0)))}))},formatShowingData(e){var t=this;return e.forEach((e=>{e.scheduled=0,e.day=t.getDay(e.dt),e.props.forEach((t=>{t.stT&&e.scheduled++}))})),e},getBuyList(){var e=this;e.loading=!0,fetchData("/1.5/showing/buylist/client",{body:{limit:6}},(function(t,i){e.loading=!1,i&&1==i.ok&&i.list&&(i.list=i.list||[],i.list.forEach((e=>{e.realtor=e.realtor||{}})),e.buylist=i.list,e.buylistMore=i.more)}))},goToBuyList(e){var t="/1.5/showing/buylist/client?d=/home/<USER>";e&&e.showing&&e.showing._id&&(t+="&id="+e.showing._id),gotoLink(t,{key:"buylist",val:"discover"})},showAllCompletedList(){this.completedList=this.completedAllList,this.completedMore=!1},alertNewFeed(){var e=this;setTimeout((()=>{e.hasNewFeed&&(e.hasNewFeed=!1,e.updateViewStatus=!0)}),3e4)},clearTimer(){this.timer&&(clearInterval(this.timer),this.timer=null)},reloadTimer(){var e=this;e.clearTimer(),this.timer=setInterval((()=>{var t=(new Date).getTime();t-e.reloadTime<9e5||(e.reloadTime=t,e.selectTag(e.propTag,1),e.getEdmLastUpdataTs(),RMSrv.setCookie&&RMSrv.setCookie())}),9e5)},clickMask(){this.showMask=!1,this.toggleModal("MatchListPopup","close")},toggleModal(e,t){toggleModal(e,t)},selectFeedTag(e,t){t||(this.activeFeedTag=e,trackEventOnGoogle("homeDiscover","feed",e),document.querySelector("#discover-container").scrollIntoView(!0)),this.handleFeedTagClick(),this.getEdmProps()},handleFeedTagClick:function(){this.feedTagChanged=!0,this.curCollEdmDate=null,this.loadMoreEdmProps=!0,this.feedDates=[],this.fixDate=!1,this.fixTabs=!1,this.edmLogs=[]},editSubscribeCmty(){this.showMask=!0,bus.$emit("watch-item-change",{item:this.selectedCmty}),toggleModal("watchingModal","open")},closePopup(){this.showMask=!1,toggleModal("watchingModal","close")},isInViewport(e){var t=document.getElementById(e);if(!t)return!1;var{top:i,left:s,width:a,height:n}=t.getBoundingClientRect();return i<window.pageYOffset+window.innerHeight&&s<window.pageXOffset+window.innerWidth&&i+n-80>window.pageYOffset&&s+a>window.pageXOffset},isGroupInViewport(e){var t=document.getElementById(e);if(!t)return!1;var{top:i,height:s}=t.getBoundingClientRect();return i<(this.curFixedEdmIndex>0&&this.scrollDirection>0?133:92)&&i+s-80>window.pageYOffset},getScrollDirection(){var e=document.getElementById("content"),t=e.pageYOffset||e.scrollTop;t>this.lastScrollTop?this.scrollDirection=1:this.scrollDirection=0,this.lastScrollTop=t,this.fixedDate()},getTranslate:function(e){return TRANSLATES[e]||e},checkEdmPropsExist:e=>e&&Object.keys(e).length>0,showSeeAllBtn:e=>e&&Object.keys(e).length>5,calcGroupName(e){return e.chgd?this.$_("Updates of saved and watched"):e.new||e.off?e.isFavCmty?this.$_("Saved community updates"):this.$_("Saved search updates"):""},fixedDate(){var e=this.cmtyProps,t=93;if("feed"==this.propTag.k&&(e=this.edmLogs,t=150),e.length){var i=document.querySelector("#scrollToFixed").getBoundingClientRect(),s=document.querySelector(".propsContainer").getBoundingClientRect();if(this.fixTabs=i.top<=44,this.fixDate=s.top<=t,this.fixDate){var a=e[this.curFixedEdmIndex].id;if(this.scrollDirection){if(!this.isInViewport(a))return this.curFixedEdmIndex+=1,void(this.curFixedEdmGroupIndex=0);this.curDisPlayEdmDate=a,this.propTag.k}else{if(this.curFixedEdmIndex>0){var n=(o=e[this.curFixedEdmIndex-1]).id;if(this.isInViewport(n))return this.curDisPlayEdmDate=n,this.curFixedEdmIndex-=1,void("feed"==this.propTag.k&&o.edm&&(this.curFixedEdmGroupIndex=o.edm.length-1))}if("feed"==this.propTag.k)var o=e[this.curFixedEdmIndex]}}}},formatUTC2Locale(e){if(!e)return"";return new Date(e).toLocaleDateString("en-CA",{year:"numeric",month:"2-digit",day:"2-digit"})},getEdmLastUpdataTs:function(){var e=this;fetchData("/1.5/community/edmupdatets",{body:{}},(function(t,i){1==i.ok&&i.edmLog&&(e.dispVar.edmDetail=Object.assign(e.dispVar.edmDetail,i.edmLog),i.edmLog.hasNewChange&&(e.hasNewFeed=!0,e.alertNewFeed()))}))},getEdmProps:function(){if(this.loadMoreEdmProps&&!this.loading){this.loading=!0;var e,t=this.curCollEdmDate;t&&(this.feedDates.push(t),e=this.feedDates);var i={date:e,tag:this.activeFeedTag,updateViewStatus:this.updateViewStatus},s=this;fetchData("/1.5/community/edmProps",{body:i},(function(e,t){s.loading=!1,s.updateViewStatus=!1,1==t.ok&&t.edmLog?(s.processEdmLog(t.edmLog),s.feedTagChanged&&(s.edmLogs=[],s.feedTagChanged=!1),s.edmLogs=s.edmLogs.concat(t.edmLog),s.curDate=t.edmLog.id,s.curCollEdmDate=t.edmLog.edmId):s.loadMoreEdmProps=!1,setTimeout((()=>{var e=document.getElementById(s.curDate);if(e){var t=document.getElementById("discover-container").getBoundingClientRect(),i=e.getBoundingClientRect();t.bottom>i.bottom&&s.getEdmProps()}}),10)}))}},processEdmLog:function(e){for(var t=0;t<e.edm.length;t++){var i=e.edm[t];i.new&&(i.showAllSale=!1),i.off&&(i.showAllSold=!1)}},buildPropListLink:function({search:e,sold:t,cmty:i}){var s="list",a="";for(var[n,o]of Object.entries(e))o&&(t&&"dom"==n&&o>=0&&(o=-90),"bbox"==n&&(s="map"),a+=`&${n}=${o}`);return t&&(a+="&soldOnly=true&dom=-90"),i&&(a+=`&cmty=${i}`),`/1.5/mapSearch?mode=${s}${a}`},selectCmty:function(e,t,i){document.querySelector("#discover-container").scrollIntoView(!0),this.selectedCmty._id!=e._id&&(trackEventOnGoogle("homeDiscover","cmty",e._id),this.handleCmtyTagClick(),i?(this.selectedCmty={},this.selectedCmtyIdx=null):(this.selectedCmty=e,this.selectedCmtyIdx=t,this.getCmtyDetail()),this.getPropsForTag())},selectTag(e,t){this.propTag.k==e.k||t||(this.propTag=e),t&&!this.fixTabs||document.querySelector("#discover-container").scrollIntoView(!0),this.scrollBar(),this.handleCmtyTagClick(),"feed"==e.k?("alert"==e.from&&(this.updateViewStatus=!0,this.hasNewFeed=!1),this.selectFeedTag("All",t)):(this.getPropsForTag(),trackEventOnGoogle("homeDiscover","tag",e.k))},handleCmtyTagClick:function(){this.curFixedEdmIndex=0,this.curDisPlayEdmDate="",this.fixDate=!1,this.fixTabs=!1,this.sortedDates=[],this.page=0,this.cmtyProps=[],this.cmtyPropsObj={},this.hasMoreProps=!0},getCmtyDetail:function(){var e=this.selectedCmty._id;if(e){var t={id:e};getStandardFunctionCaller()("findCmtyDetail",t,((e,t)=>{e?bus.$emit("flash-message",e.toString()):t&&!t.msg&&(this.selectedCmty=t)}))}},getPropsForTag:function(){if(!this.hasMoreProps)return;var e={page:this.page,tsDiff:2592e6,isAllowedVipUser:!1,ptype:"r",label:1,no_mfee:!1,soldOnly:!0,oh:!1,saletp:"Sale",sort:"auto-ts",src:"mls",skipFav:1};"feed"!=this.propTag.k&&(e[this.propTag.k]=this.propTag.v,"ts"!=this.propTag.k&&"new"!=this.propTag.v&&(e.sort="spcts-desc"),"lpChg"==this.propTag.k&&(e.sort="pcts-desc"),"soldLoss"==this.propTag.k&&(e.status="U")),this.selectedCmty._id?(e.cmty=this.selectedCmty.nm,e.city=this.selectedCmty.city_en,e.prov=this.selectedCmty.pr_en):(e.city=this.userCity.o,e.prov=this.userCity.p_ab);const t=this;fetchData("/1.5/props/search",{body:e},(function(e,i){if(e)console.log(e);else{var s=i.items;i.cnt<20||0==s.length?t.hasMoreProps=!1:t.hasMoreProps=!0,t.cmtyProps=t.getDatePropsFormat(s)}}))},getDatePropsFormat:function(e){var t=Object.assign({},this.cmtyPropsObj),i="spcts";"ts"==this.propTag.k&&"new"==this.propTag.v&&(i="ts"),"lpChg"==this.propTag.k&&(i="pcts");var s=this.sortedDates;e.forEach((e=>{var a=this.formatUTC2Locale(e[i]||e.ts||"");t[a]?t[a].push(e):(s.push(a),t[a]=[e])})),this.cmtyPropsObj=t;var a=[];return s.sort().reverse(),s.forEach((e=>{a.push({id:e,props:t[e]})})),this.sortedDates=s,a},handleLoadMore:function(){var e=this;e.getScrollDirection(),e.waiting||(e.waiting=!0,e.scrollLoading=!0,setTimeout((function(){e.waiting=!1;var t=e.scrollElement;t.scrollHeight-t.scrollTop<=t.clientHeight+260&&("feed"!=e.propTag.k?(e.page+=1,e.getPropsForTag()):e.getEdmProps()),e.checkScrollAndSendLogger(t)}),400))},getDatePropsBlockId:e=>`${e.id}`,goToTop(){this.curFixedEdmIndex=0,this.curDisPlayEdmDate="",this.fixDate=!1,document.getElementById("content").scrollTop=0},scrollBar(){setTimeout((()=>{var e=document.querySelector(".tabs-container"),t=document.querySelector(".selected");if(!e||!t)return setTimeout((()=>{this.scrollBar()}),200);var i=(window.innerWidth-t.getBoundingClientRect().width)/2,s=t.offsetLeft-i;e.scrollLeft=s<0?0:s}),20)},calcDirection:function(e,t){let i=this.propTag.k,s=this.lastPropTag.k,a=this.propTags.findIndex((e=>e.k==i)),n=this.propTags.findIndex((e=>e.k==s));return t==a?n>t?"r2l":"l2r":t==n?"out":""},getFixedDateClass(){var e=[];return this.fixDate&&e.push("fixed"),this.propTag&&e.push("hasPropTag"),"feed"==this.propTag.k&&e.push("feedTag"),e.join(" ")},activeCity(){return this.selectedCmty.nm||this.userCity.n},goToDiscover(){trackEventOnGoogle("homeDiscover","discover"),location.href="/1.5/community/filter?d=/home/<USER>"}},destroyed(){this.clearTimer(),RMSrv.offAppStateChange&&this.appStateCallback&&RMSrv.offAppStateChange(this.appStateCallback)}};(app=Vue.createApp(discover)).component("prop-item",propItem),app.component("daily-feeds",dailyFeeds),app.component("match-list-popup",MatchListPopup),app.component("watching-popup",watchingPopup),trans.install(app,{ref:Vue.ref}),app.mixin(pageDataMixins),app.mount("#discover");
