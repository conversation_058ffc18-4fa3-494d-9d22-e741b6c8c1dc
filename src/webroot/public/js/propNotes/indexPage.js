var notesInfo={props:{noteinfo:{type:Object,default:{memo:[],pic:[],avt:"",nm_zh:"",nm_en:""}},isshowicon:{type:String},propid:{type:String,default:""},dispvar:{type:Object,default:{isRealGroup:!1,isApp:!1,isNoteAdmin:!1,lang:"en"}},isshowmore:{type:Boolean,default:!1},noteindex:{type:Number,default:0}},data:()=>({showDelete:!1,showMore:!1,sizes:[],EDIT:"edit",PRAISE:"praise"}),mounted(){var t=this;this.$nextTick((()=>{t.judgeShowMore()}))},methods:{judgeShowMore(){if(this.isshowmore){if(document.querySelector(".noteinfoH"+this.noteindex).clientHeight>=76)return void(this.showMore=!0);this.showMore=!1}},deleteNote(){let t=this;setLoaderVisibility("block"),fetchData("/1.5/notes/delete",{body:{id:this.noteinfo._id,propId:this.propid}},((e,s)=>{if(setLoaderVisibility("none"),e||!s.ok){let i=e?t.$_("Error"):t.$_(s.e);return bus.$emit("flash-message",i)}s.tokenMsg&&s.tokenMsg.length&&bus.$emit("flash-message",this.$_(s.tokenMsg)),this.showDelete=!1,bus.$emit("update-note",{model:"delete",id:this.noteinfo._id})}))},editNote(){if(!this.noteinfo.uaddr||"undefined"==this.noteinfo.uaddr)return bus.$emit("flash-message",this.$_(NOTE_TIPS.NOUADDR));if(!this.propid||"undefined"==this.propid)return bus.$emit("flash-message",this.$_(NOTE_TIPS.NOPROPID));let t=this;fetchData("/1.5/notes/findListingByID",{body:{id:this.propid}},((e,s)=>{if(!s.ok)return bus.$emit("flash-message",this.$_(NOTE_TIPS.NOPROPID));let i=s.result.uaddr;if(!i||"undefined"==i)return bus.$emit("flash-message",this.$_(NOTE_TIPS.NOUADDR));let o=`/1.5/notes/editNotes?propId=${this.propid}&uaddr=${encodeURIComponent(i)}&id=${this.noteinfo._id}`;t.dispvar.isRealGroup||t.dispvar.isNoteAdmin||!s.result.unt||(o+=`&unt=${encodeURIComponent(s.result.unt)}`);RMSrv.getPageContent(o,"#callBackString",{toolbar:!1},(function(e){if(":cancel"!=e)try{let s=JSON.parse(e);if("delete"==s.type)return window.bus.$emit("update-note",{model:"delete",id:t.noteinfo._id});let i=s.note;t.noteinfo=t.noteinfo?Object.assign(t.noteinfo,i):i,i.memo||(t.noteinfo.memo=[]),i.pic||(t.noteinfo.pic=[])}catch(t){console.error(t)}}))}))},topArticle(){let t={_id:this.noteinfo._id};t.topMt=this.noteinfo.topMt?"":new Date,fetchData("/1.5/notes/update",{body:{noteDetail:t,type:this.PRAISE,propId:this.propid}},((t,e)=>{if(!e.ok)return bus.$emit("flash-message","Error");e.tokenMsg&&e.tokenMsg.length&&bus.$emit("flash-message",this.$_(e.tokenMsg)),this.noteinfo=Object.assign(this.noteinfo,e.result),!e.result.topMt&&this.noteinfo.topMt&&delete this.noteinfo.topMt,bus.$emit("update-note",{model:this.PRAISE,id:this.noteinfo._id,isPraise:this.noteinfo.topMt})}))},setUpPreview(t){var e,s,i,o=t.target;if(o){if(/^data:image/.test(o.src))return;s=o.naturalWidth,e=o.naturalHeight,o.setAttribute("data-size",s+"x"+e),i=parseInt(o.getAttribute("dataindex")),this.sizes[i]||(this.sizes[i]=s+"x"+e),o.classList.contains("detail-photo")&&e>s&&o.classList.add("vertical")}},removePicSize(t=[]){let e=[];return t.forEach((t=>{e.push(t.replace(/(\/(414|768|828)\.)/,"/"))})),e},previewPic(t){var e,s,i,o=this,n=t.target||t.srcElement;if(e=parseInt(n.getAttribute("dataindex")),s=JSON.stringify({urls:o.removePicSize(o.noteinfo.pic),index:e,sizes:o.sizes}),!LZString)return console.error("LZString is required!");i="/1.5/SV/photoScroll?data="+LZString.compressToEncodedURIComponent(s),(window.RMSrv||this.inFrame)&&RMSrv.openTBrowser(i,{nojump:!0,title:o.$_("RealMaster")})},confirmDelete(){let t=this,e=t.$_("Are you sure you want to delete this note?"),s=t.$_("Cancel"),i=t.$_("Yes");return RMSrv.dialogConfirm(e,(function(e){e+""=="2"&&t.deleteNote(),e+""=="1"&&(t.showDelete=!1)}),"",[s,i])}},template:'\n  <div id="notesInfo" v-if="noteinfo != null">\n    <div style="margin-bottom:10px;" v-if="isshowicon">\n      <img v-if="isshowicon == EDIT || dispvar.isNoteAdmin || isshowicon == \'complex\'" class="headPic" :src="noteinfo.avt || \'/img/user-icon-placeholder.png\'" @error="e => { e.target.src = \'/img/user-icon-placeholder.png\' }" referrerpolicy="same-origin">\n      <img v-else class="headPic" src="/img/user-icon-placeholder.png">\n      <div class="userInfo">\n        <div class="userNames" v-if="(isshowicon == EDIT || dispvar.isNoteAdmin) && (noteinfo.nm_en || noteinfo.nm_zh || noteinfo.nm)">{{dispvar.lang == \'en\'? (noteinfo.nm_en || noteinfo.nm) : (noteinfo.nm_zh || noteinfo.nm)}}</div>\n        <div v-else style="height: 21px;"></div>\n        <div class="updateTime">\n          <span v-show="isshowicon == \'praise\' && noteinfo.topMt" class="topTag">{{$_(\'TOP\')}}</span>\n          {{noteinfo.mt}}\n        </div>\n      </div>\n      <div style="float: right;margin-top: 15px;">\n        <div v-if="isshowicon == EDIT">\n          <span class="btn btn-nooutline pull-right" style="padding:0px;margin-top:-3px;" v-show="showDelete">\n            <span class="pull-right btn btn-nooutline" style="border:1px solid #fff;" @click.stop.prevent="showDelete = false">{{$_(\'Cancel\')}}</span>\n            <span class="pull-right btn btn-negative" @click.stop.prevent="confirmDelete()">{{$_(\'Delete\')}}</span>\n          </span>\n          <div v-show="!showDelete">\n            <a href="javascript:;" style="margin-right:20px;font-size: 15px;" @click="editNote">\n              <span class="sprite16-18 sprite16-5-3"></span>\n              <span style="margin-left: 5px;vertical-align: top;">{{$_(\'Edit\')}}</span>\n            </a>\n            <span class="sprite16-18 sprite16-4-8" style="color:rgba(17,17,17,.6)" @click="showDelete = true"></span>\n          </div>\n        </div>\n        <span v-if="isshowicon == PRAISE && dispvar.isNoteAdmin" :class="noteinfo.topMt? \'fa fa-rmliked\':\'fa fa-rmlike\'" :style="noteinfo.topMt? \'color:#428bca\' : \'color:#111111\'" @click="topArticle()"></span>\n      </div>\n    </div>\n    <div>\n      <div :class="\'noteinfoH\'+ noteindex" :class="showMore && isshowmore? \'fold\':\'\'">\n        <div v-for="(item,index) in noteinfo.memo" style="padding-bottom:10px;">\n          <span v-show="item.tag !== \'noTag\'" class="tagTitle fontSize15">[{{item.tag}}]&nbsp;</span>\n          <span class="tagNotes fontSize15">{{item.m}}</span>\n        </div>\n      </div>\n      <div v-if="isshowmore && showMore" style="color:#428bca;font-size:12px" @click="showMore = false">{{$_(\'More\')}}</div>\n    </div>\n    <div class="pic-table marTop5" v-show="noteinfo.pic && noteinfo.pic.length>0">\n      <img class="image" v-for="(src,$index) in noteinfo.pic" :src="src" :dataindex="$index" @click="previewPic($event)" @load="setUpPreview" referrerpolicy="same-origin">\n    </div>\n    <div style="display:none">{{$_(\'Edit\')}}{{$_(\'Delete\')}}{{$_(\'TOP\')}}{{$_(\'More\')}}{{$_(\'Yes\')}}</div>\n  </div>\n  '},tagInfo={props:{notestag:{type:Array,default:()=>[]},editflag:{type:String,default:""},isapp:{type:Boolean,default:!0}},data:()=>({tagText:[]}),mounted(){this.tagText=decodeJsonString2Obj("TAGTEXT","array")},methods:{reset(){bus.$emit("noteTag-list",!1)},chooseTag(t){if("uaddrTag"!=this.editflag){if("editTag"==this.editflag&&this.notestag.includes(t))return window.bus.$emit("flash-message",this.$_("Tag already exists"));bus.$emit("noteTag-list",t)}else{let e=this.notestag.indexOf(t);-1!==e?this.notestag.splice(e,1):this.notestag.push(t)}}},template:'\n  <div id="tagInfo">\n    <flash-message></flash-message>\n    <div :class="[\'backdrop\', { webShow: !isapp }]" @click=\'reset()\'></div>\n    <div :class="[\'tagSelect\', { webShow: !isapp }]">\n      <div v-for="(tag,row) in tagText">\n        <button v-for="(item,col) in tag" class="tagText" :class="notestag.includes(item)?\'choose\':\'noChoose\'" @click="chooseTag(item)">{{item}}</button>\n        <hr>\n      </div>\n    </div>\n  </div>\n  '},propItem={props:{prop:{type:Object,default:function(){return{pnSrc:[]}}},loading:{type:Boolean},lang:{type:String},tag:{type:String,default:""},cantClick:{type:Boolean,default:!1}},mounted(){},computed:{showBlur:function(){return!("sold"!=this.tag&&"fast"!=this.tag||!this.prop.login)}},methods:{openDetail:function(t){if(!this.cantClick){var e=/^RM/.test(t.id)?t.id:t._id;openPopup(`/1.5/prop/detail/inapp?id=${e}`,this.$_("RealMaster"))}},getPropDate:t=>"U"==t.status_en?t.sldd:t.mt?t.mt.substr(0,10):"",formatPrice:t=>"number"==typeof t?"$"+(t=Math.ceil(t)).toString().replace(/\B(?=(\d{3})+(?!\d))/g,","):t,popupSrcList(){window.bus.$emit("popup-match-list",this.prop)},cntSrc(t){if(!t)return 0;var e=0;return t.forEach((t=>{e+=t.v.length})),e},formateSrc(t){if(!t||"string"==typeof t)return t;var e=[];return t.forEach((t=>{t.nm?e.push(t.nm):e.push(t)})),e.join(",")},showLpInDelisted(t){return"Delisted"==t.saleTpTag_en?this.formatPrice(t.lp||t.lpr):this.formatPrice(t.sp||t.lp||t.lpr)}},template:'\n    <div class="prop" :class="loading?\'loading\':\'\'" @click="openDetail(prop)" data-sub="open detail" :data-id="prop._id">\n      <div class="detail">\n        <div class="addr one-line" :class=\'{blur:showBlur}\'>\n          <span v-if="prop.addr">{{prop.unt}} {{prop.addr}}</span>\n        </div>\n        <div class="prov">{{prop.city}}, {{prop.prov}}</div>\n        <div class="bdrms" :class=\'{blur:showBlur}\'>\n          <span v-if="prop.bdrms"><span class="fa fa-rmbed"></span> {{prop.bdrms}}{{prop.br_plus?\'+\'+prop.br_plus:\'\'}}</span>\n          <span v-if="prop.rmbthrm || prop.tbthrms || prop.bthrms"><span class="fa fa-rmbath"></span> {{prop.rmbthrm || prop.tbthrms || prop.bthrms}}</span>\n          <span v-if="prop.rmgr||prop.tgr||prop.gr"><span class="fa fa-rmcar"></span> {{prop.rmgr||prop.tgr||prop.gr}}</span>\n        </div>\n        <div v-if="showBlur" class=\'prov\'>{{$_(\'Please login to see this listing!\')}}</div>\n        <div v-else>\n          <div v-if="prop.lp == \'$0\'" class="price">{{$_(\'To Be Negotiated\')}}</div>\n          <div v-else-if="tag == \'loss\'" class="price">\n            <span class=\'black\'>-{{formatPrice(prop.lsp - prop.sp)}}</span>\n            <span v-if="prop.lspDifPct && (tag == \'loss\')" class="price-change">{{Math.round(prop.lspDifPct * 10000)/100}}%</span>\n          </div>\n          <div v-else class="price">\n            <span>{{showLpInDelisted(prop)}}</span>\n            <span v-if="prop.pc && (prop.status == \'A\') && prop.pcts" class="price-change">\n              <span :class="prop.pc > 0?\'plus\':\'mins\'">{{prop.pc>0?\'+\':\'-\'}}</span>\n              <span>{{formatPrice(Math.abs(prop.pc))}}</span>\n            </span>\n            <span v-if="prop.lstStr && prop.lst == \'Sc\'" class="price-change">{{prop.lstStr}}</span>\n            <span v-if="(prop.sp && prop.priceValStrRed) && (tag != \'loss\')" class="price-change sold">{{formatPrice(prop.lp || prop.lpr)}}</span>\n          </div>\n        </div>\n        <div v-if="prop.ohdate" class="oh"><span class="fa fa-rmhistory"></span>{{$_(\'Open House\')}}: {{prop.ohdate}}</div>\n      </div>\n      <div class="img" :class=\'{blur:showBlur}\'>\n        <img class="lazy" :src="prop.thumbUrl || \'/img/no-photo.png\'" onerror="this.src=\'/img/no-photo.png\'" referrerpolicy="same-origin" />\n        <div class="tag" :class="prop.tagColor">\n          <span v-if="prop.ytvid || prop.vurlcn"><span class="fa fa-youtube-play"></span></span>\n          <span v-if="prop.ltp == \'exlisting\'" class="ltp">{{$_(\'Exclusive\')}}</span>\n          <span v-if="prop.ltp == \'assignment\'" class="ltp">{{$_(\'Assignment\')}}</span>\n          <span v-if="prop.ltp == \'rent\'" class="ltp">{{$_(\'Rental\')}}</span>\n          <span v-if="prop.type" class="type">{{prop.type}}</span>\n          <span>{{prop.saleTpTag || prop.lstStr}}</span>\n        </div>\n        <div v-if="prop.sldDom >= 0" class="dom">{{prop.sldDom}} {{$_(\'days\')}}</div>\n        \x3c!--div v-else class="dom">{{getPropDate(prop)}}</div--\x3e\n      </div>\n      <div v-if="prop.pnSrc && prop.pnSrc.length>0" class=\'match\' @click.stop=\'popupSrcList(prop)\' data-sub="feeds pnsrc" :data-id="prop._id">\n        <span class="fa fa-radar"></span>\n        <span class="pnSrc" v-if="src = prop.pnSrc[0]">\n        <span class=\'name\'>{{src.transK}} </span>\n          {{formateSrc(src.v)}}\n        </span>\n        <span class="count">{{cntSrc(prop.pnSrc)}}</span>\n        <span class="fa fa-caret-down"></span>\n      </div>\n    </div>\n  '},propNotes={data:()=>({uaddr:decodeURIComponent(vars.uaddr),addr:decodeURIComponent(vars.addr),propId:vars.propId,isBuilding:vars.isBuilding,showMyNote:!0,showAddBtn:!1,tagShow:!1,noteTags:[],noteDetail:null,noteList:[],propList:[],complexNotes:[],showAllNotes:!1,datas:["isRealGroup","isApp","isNoteAdmin","lang"],dispVar:{isRealGroup:!1,isApp:!1,isNoteAdmin:!1,lang:"en"},page:0,scrollElement:"",waiting:!1,hasMoreProps:!1,loading:!1,lat:vars.lat,lng:vars.lng,totalCount:0,complexTags:[],unfoldAllNotes:!0,showComplexNotes:!0,addrInfo:[]}),mounted(){var t=this;t.$getTranslate(t),t.getPageData(this.datas,{},!0),bus.$on("pagedata-retrieved",(function(e){t.dispVar=Object.assign(t.dispVar,e),t.getNoteDetail(),t.getNotesList(),t.getComplesNotesList()})),bus.$on("noteTag-list",(e=>{if(e){let s=this.noteTags.indexOf(e);-1!==s?t.noteTags.splice(s,1):t.noteTags.push(e),t.getNotesList()}t.tagShow=!1})),bus.$on("update-note",(e=>{if(e&&("delete"==e.model&&(this.noteDetail=null,this.propList=[],this.showAddBtn=!0),"praise"==e.model)){let s=t.noteList.findIndex((t=>t._id==e.id)),i=t.noteList.splice(s,1);if(e.isPraise)return t.noteList.unshift(i[0]),void(t.tagShow=!1);if(0==t.noteList.length)t.noteList.push(i[0]);else for(let e=0;e<t.noteList.length;e++)if(!t.noteList[e].topMt){t.noteList.splice(e,0,i[0]);break}}t.tagShow=!1}))},methods:{getNotesList(t){let e=this;setLoaderVisibility("block"),fetchData("/1.5/notes/listByUaddr",{body:{uaddr:this.uaddr,tagArray:this.noteTags,page:this.page}},((t,s)=>{if(setLoaderVisibility("none"),t||!s.ok){let i=t?e.$_("Error"):e.$_(s.e);return bus.$emit("flash-message",i)}let i=s.result;this.totalCount=i.totalCount,e.noteList=i.noteList,0!=this.noteList.length&&(this.showAllNotes=!0)}))},getNoteDetail(){setLoaderVisibility("block");let t=this;fetchData("/1.5/notes/detail",{body:{uaddr:this.uaddr,showList:!0}},((e,s)=>{if(setLoaderVisibility("none"),e||!s.ok){t.showAddBtn=!1;let i=e?t.$_("Error"):t.$_(s.e);return bus.$emit("flash-message",i)}let i=s.result;this.noteDetail=i.noteDetail,i.noteDetail&&i.noteDetail.propDetail&&(t.propList=i.noteDetail.propDetail),null!=this.noteDetail?t.showAddBtn=!1:t.showAddBtn=!0}))},getComplesNotesList(){setLoaderVisibility("block");let t=this;fetchData("/1.5/notes/getCondoComplexNotes",{body:{uaddr:this.uaddr}},((e,s)=>{if(setLoaderVisibility("none"),e||!s.ok){this.showAddBtn=!1;let i=e?t.$_("Error"):t.$_(s.e);return bus.$emit("flash-message",i)}t.complexNotes=s.result.complexNotes,t.complexTags=s.result.complexTags,t.addrInfo=s.result.addrInfo}))},unfold(t){this[t]=!this[t]},addNote(){let t=this;return this.uaddr&&"undefined"!=this.uaddr?this.propId&&"undefined"!=this.propId?(setLoaderVisibility("block"),void fetchData("/1.5/notes/findListingByID",{body:{id:this.propId}},((e,s)=>{if(setLoaderVisibility("none"),e||!s.ok)return bus.$emit("flash-message",this.$_(NOTE_TIPS.NOPROPID));let i=s.result.uaddr;if(!i||"undefined"==i)return bus.$emit("flash-message",NOTE_TIPS.NOUADDR);let o=`/1.5/notes/editNotes?propId=${this.propId}&uaddr=${encodeURIComponent(i)}`;RMSrv.getPageContent(o,"#callBackString",{toolbar:!1},(function(e){":cancel"!=e&&t.getNoteDetail()}))}))):bus.$emit("flash-message",this.$_(NOTE_TIPS.NOPROPID)):bus.$emit("flash-message",this.$_(NOTE_TIPS.NOUADDR))},closeNotes(){let t={totalCount:this.totalCount,noteDetail:this.noteDetail};window.rmCall(":ctx:"+JSON.stringify(t))},gotoSelectUaddr(){let t=this,e=`/1.5/notes/condoComplex?uaddr=${encodeURIComponent(this.uaddr)}&addr=${encodeURIComponent(this.addr)}&lng=${this.lng}&lat=${this.lat}`;RMSrv.getPageContent(e,"#callBackString",{toolbar:!1},(function(e){if(":cancel"!=e)try{let s=JSON.parse(e);if(s.isChangeAddr)return void t.getComplesNotesList();t.complexTags=s.complexTags,t.addrInfo=s.selAddrArry}catch(t){console.error(t)}}))}}};initUrlVars();var app=Vue.createApp(propNotes);trans.install(app,{ref:Vue.ref}),app.mixin(pageDataMixins),app.component("notes-info",notesInfo),app.component("tag-info",tagInfo),app.component("prop-item",propItem),app.component("flash-message",flashMessage),app.mount("#propNotes");
