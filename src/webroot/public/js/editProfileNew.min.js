"use strict";var appProf;(appProf=angular.module("appProfile",[])).controller("ctrlEditProfile",["$scope","$http",function(e,r){var t,o,n,a;return e.submitting=!1,e.loading=!1,e.template=vars.template,e.tp=vars.tp,e.formData={},e.showChangeEmail=!1,e.verified=vars.verified,e.profileStars=vars.profileStars,a=function(e){return"#0"===(""+e).substr(-2)?(""+e).slice(0,-1).replace(/\D/g,""):(""+e).replace(/\D/g,"")},n=function(){return r.post("/1.5/user/userInfo.json",{}).success((function(r,t,o,n){return e.message=r.message,r.user?(r.user.mbl=parseInt(a(r.user.mbl)),e.formData=r.user):RMSrv.dialogAlert("Error")})).error((function(r,t,o,n){return e.message=r.message,alert("Error when getting user profile")}))},t=function(){return delete localStorage.userTmpProfile,delete localStorage.userTmpProfileTs},(o=function(){var r,o,a;if(!localStorage.userTmpProfile||!localStorage.userTmpProfileTs)return n();try{return o=JSON.parse(localStorage.userTmpProfile),a=localStorage.userTmpProfileTs,new Date-new Date(a)<3e5?e.formData=o:t(),n()}catch(e){return r=e,n(),console.log(r)}})(),e.reloadPage=function(){return o()},e.setBool=function(r,t){return e.formData[r]=t},e.setSel=function(r,t){var o,n;return null==(o=e.formData)[r]&&(o[r]=[]),(n=e.formData[r].indexOf(t))>-1?e.formData[r].splice(n,1):e.formData[r].push(t)},e.toggleModal=function(e){return toggleModal(e)},e.openVerifyPopup=function(r){var t;return t="/1.5/settings/verify?d=/1.5/settings/editProfile&verify="+r,e.selectEmailAction=!1,e.loading=!0,setTimeout((function(){return e.loading=!1,e.$apply()}),1e3),RMSrv.getPageContentIframe(t,"#callBackString",{transparent:!0},(function(r){var t,o,n,a;if(":cancel"!==r)try{if("phone"===(n=JSON.parse(r)).delete)e.formData.mbl="",e.verified.mbl="",e.verified.mblV=!1;else for(o in n)a=n[o],"mbl"!==o&&"eml"!==o||(e.formData[o]=a),e.verified[o]=a;return e.$apply()}catch(e){return t=e,console.error(t)}}))},e.safeJump=function(r){return delete e.formData.avt,delete e.formData.qrcd,delete e.formData.grpqrcd,localStorage.userTmpProfile=JSON.stringify(e.formData),localStorage.userTmpProfileTs=new Date,window.location=r},e.goBack=function(){return vars.d?window.location=vars.d:window.location="/1.5/settings"},e.submitForm=function(o){var n,a;if(!0!==e.loading)return e.submitting=!0,e.loading=!0,t(),a="/1.5/settings/editProfile","other"===e.tp&&(a="/1.5/settings/editOtherProfile"),(n=r.post(a,e.formData)).success((function(r){return e.submitting=!1,e.loading=!1,e.message=r.message,r.success?(flashMessage(r.message),e.profileStars=r.profileStars):RMSrv.dialogAlert(r.message||"Error")})),n.error((function(r){return e.submitting=!1,e.loading=!1,e.message=r.message,alert("Error when saving user profile")}))},e.emlActions=function(){return e.selectEmailAction=!0},e.promptedDeleteProfile=function(){var t,o,n,a;return a=vars.strDeleteTip,o=vars.strCancle,n=vars.strConfirm,t=function(t){var o;if(!0!==e.loading&&t+""=="2")return e.loading=!0,"/1.5/settings/deleteUser",(o=r.post("/1.5/settings/deleteUser")).success((function(r){return e.loading=!1,e.message=r.e,r.ok?window.location="/1.5/user/logout":RMSrv.dialogAlert(r.e||"Error")})),o.error((function(r){return e.loading=!1,e.message=r.e,alert("Error when delete user profile")}))},RMSrv.dialogConfirm(a,t,"",[o,n])},e.btnClicked=function(t){var o;if(null==t&&(t={}),"unfollow"===t.fld){if(!0===e.loading)return;return e.loading=!0,"/1.5/settings/unfollow",(o=r.post("/1.5/settings/unfollow")).success((function(r){var t;return e.loading=!1,e.message=r.e,r.ok?((t=document.querySelector(".unfollow")).parentNode.removeChild(t),flashMessage(r.message)):RMSrv.dialogAlert(r.e||"Error")})),o.error((function(r){return e.loading=!1,e.message=r.e,alert("Error when delete user following")}))}}}]);
