var propSelTable={props:{favProps:{type:Array,default:()=>[]},showPropTable:{type:Boolean,default:!1},action:{type:String,default:""},propImgHeight:{type:Number,default:0},from:{type:String,default:""},currentGrp:{default:function(){return{idx:"0",val:{v:"Default"}}}},grps:{type:Array,default:()=>[]}},data:()=>({selectedProp:[],strings:{searchID:{key:"Search",ctx:""},alreadyExists:{key:"The current property already exists",ctx:""},saveFailed:{key:"Failed",ctx:""},noResults:{key:"No Results",ctx:""},showing:{key:"+ Showing",ctx:""},share:{key:"Share",ctx:""},cma:{key:"CMA",ctx:""},rm:{key:"RealMaster",ctx:""},NO_SELECTION:{key:"No Selection",ctx:""}}}),mounted(){if(!window.bus)return console.error("global bus required!");window.bus.$on("reset-sel-prop",(()=>{this.selectedProp=[]}))},methods:{reset(){window.bus.$emit("reset")},selectProp(s){var r=this,e=r.selectedProp.indexOf(s),t=r.favProps.find((r=>r._id==s));e>=0?(r.selectedProp.splice(e,1),t.selected=!1):(r.selectedProp.push(s),t.selected=!0),r.$forceUpdate()},formatPrice:s=>"number"==typeof s?"$"+s.toString().replace(/\B(?=(\d{3})+(?!\d))/g,","):s,parseSqft:s=>/\-/.test(s)?s:("number"==typeof s&&(s=""+s),/\./.test(s)?s.split(".")[0]:s),getPropdetail(s,r){var e=this;fetchData("/1.5/props/detail",{body:{_id:r,useThumb:!0}},(function(r,t){if(r||t.e)return RMSrv.dialogAlert(r||t.e);if(t.detail){let r=t.detail;r.fav=!0,r.thumbUrl||(r.thumbUrl=r.picUrls[0]),r.selected=!0,r.favGrp?r.favGrp.push(s):r.favGrp=[s],e.favProps.unshift(r)}else window.bus.$emit("flash-message",e.$_(e.strings.noResults.key,e.strings.noResults.ctx))}))},openAutoCompleteSearch(){var s=this;RMSrv.getPageContent("/1.5/autoCompleteSearch?isPopup=1&isMLS=1","#callBackString",{hide:!1,title:s.$_(s.strings.searchID.key,s.strings.searchID.ctx)},(function(r){if(":cancel"!=r){var e=JSON.parse(r).id;s.favProps.find((s=>s._id==e))?window.bus.$emit("flash-message",s.$_(s.strings.alreadyExists.key,s.strings.alreadyExists.ctx)):(s.selectedProp.push(e),window.bus.$emit("add-fav",{grp:s.currentGrp.idx,prop:{_id:e},grps:s.grps}),s.getPropdetail(s.currentGrp.idx,e))}}))},commitSelected(){var s=this;if(s.selectedProp.length<1)return window.bus.$emit("flash-message",s.$_(s.strings.NO_SELECTION.key,s.strings.NO_SELECTION.ctx));window.bus.$emit("commit-selected",s.selectedProp)},saveCMAForWeb(){if(this.selectedProp.length<1)return window.bus.$emit("flash-message",this.$_(this.strings.NO_SELECTION.key,this.strings.NO_SELECTION.ctx));fetchData("/1.5/saveCMA/saveCMAIds",{body:{ids:this.selectedProp}},((s,r)=>{if(!r.ok)return bus.$emit("flash-message",this.$_(this.strings.saveFailed.key,this.strings.saveFailed.ctx));bus.$emit("flash-message",r.msg)}))},dotdate(s,r,e="."){if(!s)return"";"number"==typeof s&&(s=(s+="").slice(0,4)+"/"+s.slice(4,6)+"/"+s.slice(6,8)),"string"!=typeof s||/\d+Z/.test(s)||(s+=" EST");var t=r?"年":e,p=r?"月":e,n=r?"日":"";if(/^2\d{3}-\d{1,2}-\d{1,2}/.test(s)&&!/\d+Z/.test(s)){var i=s.split(" ")[0].split("-");return i[0]+t+i[1]+p+i[2]+n}var a=new Date(s);return!a||isNaN(a.getTime())?s:a.getFullYear()+t+(a.getMonth()+1)+p+a.getDate()+n}},template:'\n    <div class="prop-table" v-if=\'showPropTable\'>\n      <div v-for="prop in favProps" :key="prop._id">\n        <div class="prop-info" @click=selectProp(prop._id) :class=\'{selected:prop.selected}\'>\n          <span class="stp" :class="prop.tagColor">\n            <span>{{prop.saleTpTag || prop.lstStr}}</span>\n          </span>\n          <span class="sort-number" v-if="selectedProp.indexOf(prop._id) >-1">\n            {{selectedProp.indexOf(prop._id)+1}}\n          </span>\n          <span class=\'table-image\'>\n            <img\n              :src="prop.thumbUrl || \'/img/noPic.png\'"\n              style="background-image: url(\'/img/noPic.png\');"\n              :style="{\'height\':propImgHeight+\'px\'}"\n              @error="e => { e.target.src = \'/img/noPic.png\'}"\n              referrerpolicy="same-origin">\n            <span class=\'dom\' v-if="!prop.login && prop.dom">{{prop.dom}} {{$_(\'days\')}}</span>\n            <span class="date stp" :class="prop.tagColor" v-show="(prop.tagColor == \'red\') && (prop.spcts||prop.mt||prop.ts) ">&nbsp;{{dotdate(prop.spcts||prop.mt||prop.ts)}}</span>\n          </span>\n          <div class="addr one-line" v-if="!prop.login">\n            <p v-show="prop.addr && prop.addr">{{prop.unt}} {{prop.addr}}</p>\n            <p v-show="(!prop.addr) && prop.cmty"> {{prop.cmty}}</p>\n            <p>{{prop.city}}, {{prop.prov}}</p>\n          </div>\n          <div class="addr one-line" v-if="prop.login"><span v-show="prop.addr">{{prop.addr}}, {{prop.city}}</span><span\n            v-show="!prop.addr">{{prop.city}}, {{prop.prov}}</span>\n          </div>\n          <p class="bdrms" v-if="!prop.login">\n            <span v-if="prop.bdrms"><span class="fa fa-rmbed"></span> {{prop.bdrms}}{{prop.br_plus?\'+\'+prop.br_plus:\'\'}}</span>\n            <span v-if="prop.rmbthrm || prop.tbthrms || prop.bthrms"><span class="fa fa-rmbath"></span> {{prop.rmbthrm || prop.tbthrms || prop.bthrms}}</span>\n            <span v-if="prop.rmgr||prop.tgr||prop.gr"><span class="fa fa-rmcar"></span> {{prop.rmgr||prop.tgr||prop.gr}}</span>\n          </p>\n          <p class="price" v-if="!prop.login">\n            <span>{{formatPrice(prop.sp || prop.lp || prop.lpr)}}</span>\n            <span v-if="prop.sp" class="price-change sold">{{formatPrice(prop.lp || prop.lpr)}}</span>\n          </p>\n          <p v-if="!prop.login && (prop.sqft || (prop.sqft1&&prop.sqft2) || prop.rmSqft)">\n              <span v-if="prop.sqft">{{parseSqft(prop.sqft)}}</span>\n              <span v-else-if="prop.sqft1&&prop.sqft2">{{prop.sqft1}}-{{prop.sqft2}}</span>\n              <span v-if="prop.rmSqft && prop.login">\n                <span v-if="prop.sqft && /-/.test(prop.sqft) && (prop.sqft!=prop.rmSqft)">&nbsp;({{parseSqft(prop.rmSqft)}})</span>\n                <span v-if=\'!prop.sqft\'>{{parseSqft(prop.rmSqft)}} ({{$_(\'Estimated\')}})</span>\n              </span>\n              <span>&nbsp;{{$_(\'ft&sup2;\')}}</span>\n            </span>\n          </p>\n          <p v-if="!prop.login && prop.front_ft">\n            {{prop.front_ft}}&nbsp;* {{prop.depth}}{{prop.lotsz_code}} {{prop.irreg}}\n          </p>\n        </div>\n      </div>\n      <div>\n        <div class="prop-info add-new-prop" @click="openAutoCompleteSearch()">\n          <span class="plus-icon" :style="{\'height\':propImgHeight+\'px\',\'line-height\':propImgHeight+\'px\'}">\n            <span class="fa fa-plus"></span>\n          </span>\n          <p class="addr">{{$_(\'Add prop by ID\')}}</p>\n        </div>\n      </div>\n    </div>\n    <div class="btn-cell bar bar-tab" v-show="showPropTable" style="display:flex;align-items: center;">\n      <a @click="commitSelected()" class="btn btn-tab btn-half btn-sharp btn-fill btn-negative">\n        <span v-if="action.length">{{$_(strings[action].key)}}&nbsp;</span>\n        <span class="badge" style="color:white">{{selectedProp.length}}</span>\n      </a>\n      <a @click="saveCMAForWeb()" class="btn btn-tab btn-half btn-sharp btn-fill btn-primary" style="color:white" v-if="action == \'cma\'">{{$_(\'Save for Web\')}}</a>\n      <a @click="reset()" class="btn btn-tab btn-half btn-sharp btn-fill length">{{$_(\'Cancel\')}}</a>\n    </div>\n  '},propItem={props:{prop:{type:Object},rcmdHeight:{type:Number},index:{type:Number},formPage:{type:String,default:""},showFav:{type:Boolean,default:!0},create:{type:Boolean,default:!1},dispVar:{default:function(){return{lang:"zh-cn",isNoteAdmin:!1,isRealGroup:!1}}}},data:()=>({noteMemo:""}),mounted(){if(!(bus=window.bus))return console.error("global bus required!")},computed:{computedSaletp:function(){let s=this.prop.saletp;return s?Array.isArray(s)?s.join(" "):s:this.prop.lpunt},computedBgImg:function(){return this.prop.thumbUrl||"/img/noPic.png"},computedShowInfo(){return"similar"==this.formPage&&(!!this.prop.dist||(!!(this.prop.sqft||this.prop.sqft1&&this.prop.sqft2||this.prop.rmSqft)||(!!this.prop.front_ft||!(!this.prop.weight||!this.dispVar.isAdmin))))}},watch:{prop:{handler(s){this.prop.noteMemo&&("noTag"==this.prop.noteMemo[0].tag?this.noteMemo=this.prop.noteMemo[0].m:this.noteMemo=`[${this.prop.noteMemo[0].tag}]${this.prop.noteMemo[0].m}`)},immediate:!0,deep:!0}},methods:{showAgentWesite(s){var r=`/1.5/wesite/${s._id}?inFrame=1`;RMSrv.openTBrowser(r)},getStatus:function(s){return"U"==s.status?"sold":"sale"},openDetail:function(s){var r="/1.5/prop/detail/inapp?id="+(/^RM/.test(s.id)?s.id:s._id);this.formPage&&(r+=`&formPage=${this.formPage}`),openPopup(r,this.$_("RealMaster"))},openShowing:s=>window.bus.$emit("open-showing",s),getPropDate:s=>"U"==s.status_en?s.sldd:s.mt.substr(0,10),soldOrLeased:function(s){return"Sold"==s.saleTpTag_en||["Sld","Lsd","Pnd","Cld"].includes(s.lst)},saletpIsSale:function(s){return!s.saletp_en||!!/sale/.test(s.saletp_en.toString().toLowerCase())},isTop:function(s){return"A"==s.status&&new Date(s.topTs)>=new Date},propSid:function(s){return s.isProj?"":s.sid?s.sid:/^RM/.test(s.id)?s.id:s._id?s._id.substr(3):""},rmgrStr:function(s){return s.rmgr||s.tgr||s.gr},rmbdrmStr:function(s){if(s.rmbdrm)return s.rmbdrm;let r=s.bdrms||s.tbdrms;return r+=s.br_plus?"+ "+s.br_plus:"",r},rmbthrmStr:function(s){return s.rmbthrm||s.tbthrms||s.bthrms},formatPrice:s=>"number"==typeof s?"$"+s.toString().replace(/\B(?=(\d{3})+(?!\d))/g,","):s,isInGrp(s){return(this.prop.favGrp||[]).indexOf(parseInt(s))>-1},toggleFav(s){window.bus.$emit("toggleFav",s)},computedVideoUrl:function(){return this.dispVar.isCip?this.prop.vurlcn:this.prop.ytvid?"https://www.youtube.com/watch?v="+this.prop.ytvid:null},isPropUnavailable:function(){return(!this.prop.id||"RM"!=this.prop.id.substr(0,2))&&"A"!==this.prop.status_en},calcShowingName:s=>s.showing?s.showing.dt:"",remove(){window.bus.$emit("remove",this.prop)},clearNoteInfo(){delete this.prop.noteMemo,delete this.prop.noteId,this.noteMemo="",this.prop.noteCount--},goToEditNote(){if(!this.prop.uaddr||"undefined"==this.prop.uaddr)return bus.$emit("flash-message",this.$_(NOTE_TIPS.NOUADDR));if(!this.prop._id||"undefined"==this.prop._id)return bus.$emit("flash-message",this.$_(NOTE_TIPS.NOPROPID));let s=this;fetchData("/1.5/notes/findListingByID",{body:{id:this.prop._id}},((r,e)=>{if(!e.ok)return bus.$emit("flash-message",this.$_(NOTE_TIPS.NOPROPID));let t=e.result.uaddr;if(!t||"undefined"==t)return bus.$emit("flash-message",this.$_(NOTE_TIPS.NOUADDR));let p=`/1.5/notes/editNotes?propId=${this.prop._id}&uaddr=${encodeURIComponent(t)}`;this.prop.noteId&&(p+=`&id=${this.prop.noteId}`),s.dispVar.isRealGroup||s.dispVar.isNoteAdmin||!e.result.unt||(p+=`&unt=${encodeURIComponent(e.result.unt)}`);let n={toolbar:!1};s.dispVar&&s.dispVar.isApp&&RMSrv.getPageContent(p,"#callBackString",n,(function(r){if(":cancel"!=r)try{let e=JSON.parse(r);if("delete"==e.type)return void s.clearNoteInfo();let t=e.note;s.prop.noteMemo||(s.prop.noteCount+=1),t.memo?s.prop.noteMemo=t.memo:s.prop.noteMemo=[{tag:"noTag",m:" "}],s.prop.noteId=t._id}catch(s){console.error(s)}}))}))},goToNoteList(){let s=this;if(!this.prop.uaddr)return bus.$emit("flash-message",this.$_(NOTE_TIPS.NOUADDR));if(!this.prop._id)return bus.$emit("flash-message",this.$_(NOTE_TIPS.NOPROPID));let r=`/1.5/notes?propId=${this.prop._id}&uaddr=${encodeURIComponent(this.prop.uaddr)}&addr=${encodeURIComponent(this.prop.addr)}&lat=${this.prop.lat}&lng=${this.prop.lng}&isBuilding=${this.prop.isBuilding}`,e={toolbar:!1};s.dispVar&&s.dispVar.isApp&&RMSrv.getPageContent(r,"#callBackString",e,(function(r){if(":cancel"!=r)try{let e=JSON.parse(r).noteDetail;e&&s.prop.noteMemo?s.prop.noteMemo=e.memo?e.memo:[{tag:"noTag",m:" "}]:e&&!s.prop.noteMemo?(s.prop.noteMemo=e.memo?e.memo:[{tag:"noTag",m:" "}],s.prop.noteId=e._id,s.prop.noteCount+=1):!e&&s.prop.noteMemo&&s.clearNoteInfo()}catch(s){console.error(s)}}))},parseSqft:s=>/\-/.test(s)?s:("number"==typeof s&&(s=""+s),/\./.test(s)?s.split(".")[0]:s),dotdate(s,r,e="."){if(!s)return"";"number"==typeof s&&(s=(s+="").slice(0,4)+"/"+s.slice(4,6)+"/"+s.slice(6,8)),"string"!=typeof s||/\d+Z/.test(s)||(s+=" EST");var t=r?"年":e,p=r?"月":e,n=r?"日":"";if(/^2\d{3}-\d{1,2}-\d{1,2}/.test(s)&&!/\d+Z/.test(s)){var i=s.split(" ")[0].split("-");return i[0]+t+i[1]+p+i[2]+n}var a=new Date(s);return!a||isNaN(a.getTime())?s:a.getFullYear()+t+(a.getMonth()+1)+p+a.getDate()+n}},template:'\n<div class="prop" @click="openDetail(prop)">\n  <div class="img" :style="{ \'height\':rcmdHeight+\'px\'}">\n    <img :src="computedBgImg"\n      style="background-image: url(\'/img/noPic.png\');background-size: 100% 100%;width:100%;"\n      :style="{\'height\':rcmdHeight+\'px\'}"\n      @error="e => { e.target.src = \'/img/noPic.png\'}"\n      referrerpolicy="same-origin">\n    <div class="on-img-top">\n      <span class="top pull-left" v-if="prop.isTop && prop.marketRmProp">{{$_(\'TOP\')}}</span>\n      <span class="tp" v-show="prop.type">{{prop.type}}</span>\n      <span class="pull-right fav fa" :class="{\'fa-heart-o\':!prop.fav, \'fa-heart\':prop.fav}"\n        @click.stop.prevent="toggleFav(prop)" v-show="!prop.login && !prop.isProj" v-if=\'showFav\'></span>\n    </div>\n  </div>\n  <div class="price" :class="{\'blur\':prop.login}">\n    <span class="val" v-if=\'prop.priceValStrRed\'>{{prop.priceValStrRed}}</span>\n    <span class="val" v-else-if=\'prop.askingPriceStr\'>{{prop.askingPriceStr}}</span>\n    <span class="desc" v-if="prop.priceValStrRedDesc" :class="{\'through\':soldOrLeased}">{{soldOrLeased?prop.askingPriceStr:prop.priceValStrRedDesc}}</span>\n    <span class="desc" v-if="prop.lstStr && (prop.tagColor != \'red\')">({{prop.lstStr}})</span>\n    <div class="displayFlex maxWidth">\n      <span class="stp" v-if="prop.saleTpTag && prop.saleTpTag_en !=\'Delisted\'" :class="prop.tagColor">\n        <span>{{prop.saleTpTag}}</span>\n        <span v-if="(prop.tagColor == \'red\'|| prop.tagColor == \'green\') && (prop.spcts||prop.mt||prop.ts) && !prop.marketRmProp">&nbsp;{{dotdate(prop.spcts||prop.mt||prop.ts)}}</span>\n      </span>\n      <span class="stp green" v-show="[\'exlisting\',\'assignment\',\'rent\'].indexOf(prop.ltp) > -1 && !prop.marketRmProp">\n        <span v-show="prop.ltp == \'exlisting\'">{{$_(\'Exclusive\')}}</span>\n        <span v-show="prop.ltp == \'assignment\'">{{$_(\'Assignment\')}}</span>\n        <span v-show="prop.ltp == \'rent\' && !prop.cmstn">{{$_(\'Landlord Rental\')}}</span>\n        <span v-show="prop.ltp == \'rent\' && prop.cmstn">{{$_(\'Exclusive Rental\')}}</span>\n      </span>\n      <span class="dist" v-show="prop.dist && (formPage != \'similar\')" >{{prop.dist}}</span>\n      <span class="stp" v-show="prop.isAgreementVerified && !prop.marketRmProp">\n        <span class="fa fa-check"></span>\n      </span>\n      <span class="stp vid" v-show="computedVideoUrl()">\n        <span class="fa fa-youtube-play"></span>{{$_(\'Video\')}}\n      </span>\n      <span class="stp oh" v-if="prop.hasOh">{{$_(\'Open House\')}}</span>\n      <div class="stp viewTrusted" v-if="prop.marketRmProp"><span class="fa fa-check-circle trustedCir"></span>{{$_(prop.marketRmProp)}}</div>\n    </div>\n  </div>\n  <div class="addr one-line" v-if="!prop.login"><span v-show="prop.daddr !== \'N\'"><span v-show="prop.addr">{{prop.unt}}\n        {{prop.addr}},</span> {{prop.city}}, {{prop.prov}}</span><span v-show="prop.daddr == \'N\'"><span\n        v-show="prop.cmty">{{prop.cmty}},</span>{{prop.city}}, {{prop.prov}}</span>\n  </div>\n  <div class="addr one-line" v-if="prop.login"><span v-show="prop.daddr == \'Y\'">{{prop.addr}}, {{prop.city}}</span><span\n      v-show="prop.daddr !== \'Y\'">{{prop.city}}, {{prop.prov}}</span></div>\n  <div class="bdrms one-line" v-if="!prop.login">\n    <span class="rmbed" v-show="prop.rmbdrm != null || prop.bdrms != null || prop.tbdrms != null">\n      <span class="fa fa-rmbed">\n      </span>\n      <b class="num">{{rmbdrmStr(prop)}}</b>\n      <span v-if="prop.bdrms_diff!= null" class=\'diff\'>\n        <span :class="{red: prop.bdrms_diff < 0, green:prop.bdrms_diff > 0,blue:prop.bdrms_diff == 0}">\n          <span class="nopadding" :class="{\'fa-caret-up\': prop.bdrms_diff > 0, \'fa-caret-down\':prop.bdrms_diff < 0,\'fa-check-circle\':prop.bdrms_diff == 0 }"></span>\n          <span v-show=\'prop.bdrms_diff != 0\'>&nbsp;{{Math.abs(prop.bdrms_diff)}}</span>\n        </span>\n      </span>\n    </span>\n    <span v-show="prop.rmbthrm != null || prop.tbthrms != null || prop.bthrms != null">\n      <span class="fa fa-rmbath">\n      </span>\n      <b class="num">{{rmbthrmStr(prop)}}</b>\n      <span v-if="prop.bthrms_diff!= null" class=\'diff\'>\n        <span :class="{red: prop.bthrms_diff < 0, green:prop.bthrms_diff > 0,blue:prop.bthrms_diff == 0}">\n          <span class="nopadding" :class="{\'fa-caret-up\': prop.bthrms_diff > 0, \'fa-caret-down\':prop.bthrms_diff < 0,\'fa-check-circle\':prop.bthrms_diff == 0}"></span>\n          <span v-show=\'prop.bthrms_diff != 0\'>&nbsp;{{Math.abs(prop.bthrms_diff)}}</span>\n        </span>\n      </span>\n    </span>\n    <span v-show="prop.rmgr != null || prop.gr != null || prop.tgr != null">\n      <span class="fa fa-rmcar"></span>\n      <b class="num">{{rmgrStr(prop)}}</b>\n      <span v-if="prop.gr_diff!= null" class=\'diff\'>\n        <span :class="{red: prop.gr_diff < 0, green:prop.gr_diff > 0,blue:prop.gr_diff == 0}">\n          <span class="nopadding" :class="{\'fa-caret-up\': prop.gr_diff > 0, \'fa-caret-down\':prop.gr_diff < 0,\'fa-check-circle\':prop.gr_diff == 0}"></span>\n          <span v-show=\'prop.gr_diff != 0\'>&nbsp;{{Math.abs(prop.gr_diff)}}</span>\n        </span>\n      </span>\n    </span>\n    <span v-if="prop.marketRmProp && prop.sqft && (formPage == \'truthAssignList\')">\n      <span class="fa fa-area"></span>\n      <b class="num">{{prop.sqft}}</b>\n    </span>\n    <span v-show="prop.isAgreementVerified && !prop.marketRmProp">\n      <span class="fa fa-check-circle"></span>\n      <b class="num">{{$_(\'Verified\')}}</b>\n    </span>\n    <span v-if="(prop.private && (formPage == \'truthAssignList\'))">\n      <span class="num" style="color:#e03131">{{$_(\'Hide to the public\')}}</span>\n    </span>\n    <span class="sid">{{propSid(prop)}}</span>\n  </div>\n  <div class="bdrms" v-if="prop.login">{{$_(\'Please login to see this listing\')}}</div>\n  <div class=\'showing-info\' v-if="formPage == \'buylist\'">\n    <span class="left">\n      <span v-show=\'dispVar.isRealtor && create\'>\n        <span v-if=\'prop.clnt && prop.clnt.nm\'>{{prop.clnt.nm}}</span>\n      </span>\n      <span v-if=\'!create && prop.realtor && prop.realtor.fnm\' class=\'agent\' @click.stop=\'showAgentWesite(prop.realtor)\'>\n        <img :src="prop.realtor.avt || prop.realtor.wxavt || \'/img/logo.png\'" @error="prop.realtor.avt = \'/img/logo.png\'" referrerpolicy="same-origin">\n        <span class="d">{{prop.realtor.fnm}}</span>\n      </span>\n      <span class="showing-name" @click.stop=\'openShowing(prop)\' :class=\'{"pull-right":!create}\'>\n        <span class="link">{{$_(\'Showing\')}}</span>\n        <span>({{calcShowingName(prop)}})</span>\n      </span>\n    </span>\n    <span class="right" v-show=\'create\'>\n      <span class="pull-right delbtns btn btn-nooutline" v-show="prop.del">\n        <span class="cancle pull-right btn btn-nooutline" @click.stop.prevent="prop.del = false" >{{ $_(\'Cancel\')}}</span>\n        <span class="delete pull-right btn btn-negative" @click.stop.prevent="remove()" >{{ $_(\'Delete\')}}</span>\n      </span>\n      <span class="pull-right sprite16-18 sprite16-4-8" v-show="!prop.del" @click.stop.prevent="prop.del = true"></span>\n    </span>\n  </div>\n  <div v-if=\'computedShowInfo\' class=\'size bdrms\'>\n    <span v-if="prop.dist">{{$_(\'Distance\')}}\n      <span class=\'val pull-right\' >{{prop.dist}}{{$_(\'m\')}}</span>\n    </span>\n    <div v-if="prop.sqft || (prop.sqft1&&prop.sqft2) || prop.rmSqft">\n      {{$_(\'Size\')}}\n      <span class=\'val pull-right\'>\n        <span v-if="prop.sqft">{{parseSqft(prop.sqft)}}</span>\n        <span v-else-if="prop.sqft1&&prop.sqft2">{{prop.sqft1}}-{{prop.sqft2}}</span>\n        <span v-if="prop.rmSqft && dispVar.isLoggedIn">\n          <span v-if="prop.sqft && /-/.test(prop.sqft) && (prop.sqft!=prop.rmSqft)">&nbsp;({{parseSqft(prop.rmSqft)}})</span>\n          <span v-if=\'!prop.sqft\'>{{parseSqft(prop.rmSqft)}} ({{$_(\'Estimated\')}})</span>\n        </span>\n        <span>&nbsp;{{$_(\'ft&sup2;\')}}</span>\n        <span v-if="prop.sqft_diff!= null" class=\'diff\'>\n          <span :class="{red: prop.sqft_diff < 0, green:prop.sqft_diff > 0,blue:prop.sqft_diff == 0}">\n            <span class="nopadding" :class="{\'fa-caret-up\': prop.sqft_diff > 0, \'fa-caret-down\':prop.sqft_diff < 0,\'fa-check-circle\':prop.sqft_diff == 0}"></span>\n            <span v-if=\'prop.sqft_diff_abs && prop.sqft_diff_abs != 0\'>&nbsp;{{prop.sqft_diff_abs}} {{$_(\'ft&sup2;\')}}</span>\n          </span>\n        </span>\n      </span>\n    </div>\n    <div v-if="prop.front_ft">\n      {{$_(\'Lot\')}}\n      <span class=\'val pull-right\'>\n        {{prop.front_ft}}\n        <span v-if="prop.front_ft_diff!= null">(\n          <span :class="{red: prop.front_ft_diff < 0, green:prop.front_ft_diff > 0,blue:prop.front_ft_diff == 0}">\n            <span class="nopadding" :class="{\'fa-caret-up\': prop.front_ft_diff > 0, \'fa-caret-down\':prop.front_ft_diff < 0,\'fa-check-circle\':prop.front_ft_diff == 0}"></span>\n            <span v-if=\'prop.front_ft_diff != 0\'>&nbsp;{{Math.abs(prop.front_ft_diff)}}</span>\n          </span>)\n        </span>\n        &nbsp;* {{prop.depth}}\n        <span v-if="prop.depth_diff!= null">(\n          <span :class="{red: prop.depth_diff < 0, green:prop.depth_diff > 0,blue:prop.depth_diff == 0}">\n            <span class="nopadding" :class="{\'fa-caret-up\': prop.depth_diff > 0, \'fa-caret-down\':prop.depth_diff < 0,\'fa-check-circle\':prop.depth_diff == 0}"></span>\n            <span v-if=\'prop.depth_diff != 0\'>&nbsp;{{Math.abs(prop.depth_diff)}}</span>\n          </span>)\n        </span>\n        {{prop.lotsz_code}} {{prop.irreg}}\n      </span>\n    </div>\n    <div v-if="prop.weight && dispVar.isDevGroup">\n      {{$_(\'Weight\')}}\n      <span class=\'val pull-right\' v-if=\'prop.weight.wTotal\'>\n        {{prop.weight.wTotal}}\n      </span>\n    </div>\n  </div>\n  <div class="bdrms" v-if="prop.login">{{$_(\'Please login to see this listing\')}}</div>\n  <div class="noteInsave" v-if="prop.noteMemo || prop.noteCount>=0">\n    <div class="noteInfo" :style="(dispVar.isNoteAdmin || dispVar.isRealGroup)? \'justify-content:normal\':\'justify-content: space-between\'">\n      <input v-model="noteMemo" class="noteMemo" readonly="readonly" :placeholder="$_(\'Add a note\')" @click.stop="goToEditNote()"></input>\n      <span v-if="dispVar.isNoteAdmin || dispVar.isRealGroup" class="fa fa-rmmemo noteIcon" @click.stop="goToNoteList()"></span>\n      <span v-else class="fa fa-rmmemo noteIcon" :style="prop.noteCount? \'color: #428bca\':\'color:#777\'" @click.stop="goToEditNote()"></span>\n      <span v-if="dispVar.isNoteAdmin || dispVar.isRealGroup" @click.stop="goToNoteList()">{{prop.noteMemo?1:0}}/{{prop.noteCount}}</span>\n    </div>\n  </div>\n</div>\n  '},shareDialog={props:{option:{type:Object},noAdvance:{type:Boolean},height:{type:Number},wDl:{type:Boolean},wSign:{type:Boolean},wComment:{type:Boolean,default:!0},dispVar:{type:Object,default:function(){return{isRealtor:!1,isVipRealtor:!1,lang:"zh-cn"}}},noSign:{type:Boolean},noLang:{type:Boolean},prop:{type:Object,required:!0,default:function(){return{}}},showComment:{type:Boolean,default:function(){return!1}},noFlyer:{type:Boolean,default:function(){return!1}}},data:()=>({wSign2:!0,wDl2:!0,wCommentCheck:!0}),watch:{},mounted(){this.wCommentCheck=this.wComment,0==this.wSign&&(this.wSign2=!1)},computed:{isProj:function(){var s=this.prop;return!(!s.deposit_m&&!s.closingDate)},computedVersion:function(){return this.$parent.isNewerVer(this.dispVar.coreVer,"5.6.0")},showPromo:{cache:!1,get(){return this.dispVar.isRealtor&&!/^RM/.test(this.prop.id)}},showSignature:{cache:!1,get(){return this.dispVar.isRealtor}},wDlDisable:{cache:!1,get(){return!this.dispVar||!(!this.dispVar.isRealtor||this.dispVar.isVipRealtor)}},wSignDisable:function(){return!this.dispVar}},methods:{getTranslate:function(s){return TRANSLATES[s]||s},checkIsAllowed(s){if(!this.dispVar.shareLinks)return!1;var r=this.dispVar.shareLinks.l||[],e=this.dispVar.shareLinks.v||[],t=r.indexOf(s),p=this.dispVar.isVipRealtor||0===e[t];return t>-1&&p},rmShare(s,r){RMSrv.share(s,r)},rmCustWechatShare(){if(!this.computedVersion)return this.confirmUpgrade(this.dispVar.lang);var s="/1.5/htmltoimg/templatelist?id=",r=this.prop._id;/^RM/.test(this.prop.id)&&(r=this.prop.id),s+=r,RMSrv.openTBrowser(s,{title:this._("Choose Template")})},rmsrvDownloadImage(){var s,r="http://img.realmaster.com/mls/1/668/********.jpg";(s=document.querySelector("#share-image"))&&(r=s.textContent),RMSrv.downloadImage(r,{},(function(s,r){let e=s||r;RMSrv.dialogAlert(e)}))},toggleDrop(s){window.bus.$emit("toggle-drop",s)},cancelPromoteModal(){RMSrv.share("hide")},promote(s){if(!this.checkIsAllowed(s))return this.confirmVip(this.dispVar.lang);var r="/1.5/promote/mylisting?ml_num="+this.prop.sid+"&to="+s;this.$parent&&this.$parent.toggleDrop&&this.$parent.toggleDrop(),RMSrv.share("hide"),this.$parent.closeAndRedirect?this.$parent.closeAndRedirect(r):window.location=r},createWePage(s){if(!this.checkIsAllowed(s))return this.confirmVip(this.dispVar.lang);var r="/1.5/wecard/edit/"+(this.prop._id||this.prop.sid)+"?shSty=";"vt"==s||"blog"==s?r+=s:"mylisting"==s&&(r="/1.5/promote/mylisting?ml_num="+this.prop._id),this.$parent.closeAndRedirect?this.$parent.closeAndRedirect(r):window.location=r}},template:'\n<div id="shareDialog">\n  <div class="backdrop" id="backdrop" style="display:none"></div>\n  <nav class="menu slide-menu-bottom smb-md" :style="{height:height}">\n      <div class="first-row" :class="{visitor:!dispVar.isRealtor}">\n          <div @click="rmCustWechatShare()" v-show="dispVar.isRealtor && !isProj && !noFlyer"><span class="sprite50-45 sprite50-5-1"></span>\n              <div class="inline">{{$_(\'Wechat Flyer\')}}</div>\n          </div>\n          <div @click="rmShare(\'wechat-moment\')"><span class="sprite50-45 sprite50-5-3"></span>\n              <div class="inline">{{$_(\'Wechat Moment\')}}</div>\n          </div>\n          <div @click="rmShare(\'wechat-friend\')"><span class="sprite50-45 sprite50-5-2"></span>\n              <div class="inline">{{$_(\'Wechat Friend\')}}</div>\n          </div>\n          <div @click="rmShare(\'facebook-feed\')"><span class="sprite50-45 sprite50-5-4"></span>\n              <div class="inline">{{$_(\'Facebook\')}}</div>\n          </div>\n          <div @click="rmShare(\'qr-code\')"><span class="sprite50-45 sprite50-6-1"></span>\n              <div class="inline">{{$_(\'QR-Code\')}}</div>\n          </div>\n          <div @click="rmShare(\'other\')"><span class="sprite50-45 sprite50-5-5"></span>\n              <div class="inline">{{$_(\'More\')}}</div>\n          </div>\n      </div>\n      <div class="split" v-show="dispVar.isRealtor && !dispVar.listShareMode">\n          <div class="left inline"></div>\n          <div class="text inline"><span>{{$_(\'Advanced Features\')}}</span></div>\n          <div class="right inline"></div>\n      </div>\n      <div class="second-row" v-show="(dispVar.isRealtor || dispVar.isDevGroup) && !dispVar.listShareMode && !noAdvance">\n          <div id="shareToNews" v-show="dispVar.publishNews && dispVar.shareLinks.l.indexOf(\'news\')>-1"><span class="sprite50-45 sprite50-6-3"></span>\n              <div class="inline">{{$_(\'News\')}}</div>\n          </div>\n          <div @click="promote(\'58\')" ngClick="promote(\'58\', formData); toggleModal(\'savePromoteModal\')" v-show="dispVar.shareLinks && dispVar.shareLinks.l.indexOf(\'58\')>-1 && prop.pcls !== \'b\' && prop.src == \'TRB\'"><span class="sprite50-45 sprite50-4-4"></span>\n              <div class="inline">58.com</div>\n          </div>\n          <div @click="promote(\'market\')" ngClick="promote(\'market\', formData); toggleModal(\'savePromoteModal\')" v-show="dispVar.shareLinks && dispVar.shareLinks.l.indexOf(\'market\')>-1 && prop.pcls !== \'b\' && prop.src == \'TRB\'"><span class="sprite50-45 sprite50-4-2"></span>\n              <div class="inline">{{$_(\'Listing Market\')}}</div>\n          </div>\n          <div @click="createWePage(\'vt\')" v-show="dispVar.shareLinks && dispVar.shareLinks.l.indexOf(\'vt\')>-1 && prop.src == \'TRB\'"><span class="sprite50-45 sprite50-4-3"></span>\n              <div class="inline">{{$_(\'WePage Flyer\')}}</div>\n          </div>\n          <div @click="createWePage(\'blog\')" v-show="dispVar.shareLinks && dispVar.shareLinks.l.indexOf(\'blog\')>-1 && prop.src == \'TRB\'"><span class="sprite50-45 sprite50-4-5"></span>\n              <div class="inline">{{$_(\'WePage Blog\')}}</div>\n          </div>\n      </div>\n      <div class="cancel">\n          <div class="promoWrapper" v-show="!noSign">\n              <div class="inline" id="id_with_sign_wrapper" v-show="showSignature"><label id="id_with_sign"><input type="checkbox" v-model="wSign2" checked="true" :class="{disabled:wSignDisable}"/> {{$_(\'Signature\')}}</label></div>\n              <div class="inline" id="id_with_dl_wrapper" v-show="showPromo"><label id="id_with_dl" v-show="dispVar.isVipUser"><input type="checkbox" v-model="wDl2" checked="true" :class="{disabled:wDlDisable}" :disabled="wDlDisable"/>{{$_(\'Promo\')}}</label></div>\n              <div class="inline" v-show="showComment"><label id="id_with_cm"><input type="checkbox" v-model="wCommentCheck"/>{{$_(\'With Comments\')}}</label></div>\n          </div>\n          <div class="lang-selectors-wrapper">\n              <div class="segmented-control lang-selectors">\n              <a class="control-item lang-selector" id="id_share_lang_en" onclick="RMSrv.share(\'lang-en\');" href="javascript:;" v-if="dispVar.lang != \'en\'">En</a>\n              <a class="control-item lang-selector" id="id_share_lang_zh" onclick="RMSrv.share(\'lang-zh-cn\');" href="javascript:;" v-if="!(dispVar.lang == \'zh\'||dispVar.lang == \'zh-cn\')">Zh</a>\n              <a class="control-item lang-selector" id="id_share_lang_kr" onclick="RMSrv.share(\'lang-kr\');" href="javascript:;" v-if="dispVar.lang != \'kr\'">Kr</a>\n              <a class="control-item lang-selector active" id="id_share_lang_cur" onclick="RMSrv.share(\'lang-cur\');" href="javascript:;">\n                <span v-show="dispVar.lang == \'zh\'">繁</span>\n                <span v-show="dispVar.lang == \'zh-cn\'">中</span>\n                <span v-show="dispVar.lang == \'kr\'">한</span>\n                <span v-show="dispVar.lang !== \'zh\' && dispVar.lang !== \'zh-cn\' && dispVar.lang !== \'kr\'">En</span>\n              </a></div>\n          </div><a class="cancel-btn" href="javascript:;" @click="cancelPromoteModal()">{{$_(\'Cancel\')}}</a></div>\n  </nav>\n  <div class="pic" id="id_share_qrcode">\n      <div id="id_share_qrcode_holder"></div><br/>\n      <div style="border-bottom: 2px solid #F0EEEE; margin:10px 15px 10px 15px;"></div><button class="btn btn-block btn-long" onclick="RMSrv.share(\'qr-code-close\');" style="border:1px none;">{{$_(\'Close\')}}</button></div>\n    <div class="hide" style="display:none">{{$_(\'Available only for Premium VIP user! Upgrade and get more advanced features.\')}} {{$_(\'See More\')}} {{$_(\'Later\')}}\n  </div>\n</div>\n    '},listingShareDesc={computed:{propSqft:function(){let s=this.prop||{};if(!s.sqft||/-/.test(s.sqft))return s.sqft;let r=parseInt(s.sqft);return isNaN(r)?s.sqft:r}},props:{isApp:{type:Boolean,default:!1},prop:{type:Object,default:function(){return{lp:0}}}},methods:{getTranslate:function(s){return TRANSLATES[s]||s},isArray:s=>Array.isArray(s),isRMProp:s=>/^RM/.test(s.id),getDesc:s=>s?s.length>70?s.substr(0,70)+"...":s:""},template:"\n  <div>\n  <span id=\"share-title-en\"><span v-if=\"!isApp\">RealMaster •  </span><span v-if=\"prop.ltp=='assignment'\">Assignment\n      • </span><span v-if=\"prop.ltp=='exlisting'\">Exclusive • </span><span\n      v-if=\"prop.ltp=='rent' && !prop.cmstn\">Landlord Rent • </span><span\n      v-if=\"prop.ltp=='rent' && prop.cmstn\">Rent • </span><span> {{(prop.lp || prop.lpr)}}\n      • <span v-if=\"prop.daddr !== 'N'\">{{prop.addr}} {{prop.unt||''}}, </span>{{prop.city_en || prop.city}}\n      {{prop.prov_en || prop.prop}}</span>\n  </span>\n  <span id=\"share-desc-en\">\n    {{isRMProp(prop) ? prop.id +', ' : (prop.sid || prop._id)+', '}}\n    {{ isArray(prop.ptp) ? prop.ptp[0] : (prop.ptype2_en?prop.ptype2_en.join(' '):'') }}\n    {{prop.pstyl_en}}\n    {{propSqft?', '+propSqft+' Sqft, ':''}}<span v-if=\"prop.bcf != 'b'\">Bedroom: {{prop.rmbdrm||prop.tbdrms||prop.bdrms}}, Kitchen:\n      {{prop.kch}}, Bathroom: {{prop.rmbthrm||prop.tbthrms||prop.bthrms}}, Parking:\n      {{prop.rmgr||prop.tgr||prop.gr}}</span>{{getDesc(prop.m)}}\n  </span>\n  <span id=\"share-title\"><span v-if=\"!isApp\">{{$_('RealMaster') }} • </span><span v-if=\"prop.ltp=='assignment'\">\n      {{$_('Assignment') }} • </span><span v-if=\"prop.ltp=='exlisting'\"> {{$_('Exclusive','realtor sale')}} • </span><span\n      v-if=\"prop.ltp=='rent' && !prop.cmstn\"> {{$_('Landlord Rent')}} • </span><span\n      v-if=\"prop.ltp=='rent' && prop.cmstn\"> {{$_('Rent','share-title rent')}} • </span><span>\n      {{(prop.lp || prop.lpr)}} • <span v-if=\"prop.daddr !== 'N'\">{{prop.addr}}\n        {{prop.unt||''}}, </span>{{prop.city}} {{prop.prov}}</span>\n      </span>\n  <span id=\"share-desc\">\n    {{isRMProp(prop) ? prop.id +', ' : (prop.sid || prop._id)+', '}}\n    {{ isArray(prop.ptp) ? prop.ptp[3] : (prop.ptype2?prop.ptype2.join(' '):'') }}\n    {{propSqft ? ', '+propSqft+' '+$_('Sqf','property')+', ':''}}<span v-if=\"prop.bcf != 'b'\">{{$_('Bedroom')}}:\n      {{prop.rmbdrm||prop.tbdrms||prop.bdrms}}, {{$_('Kitchen')}}: {{prop.kch}}, {{$_('Bathroom')}}: {{prop.rmbthrm||prop.tbthrms||prop.bthrms}},\n      {{$_('Parking')}}: {{prop.rmgr||prop.tgr||prop.gr}}</span>{{getDesc(prop.m || prop.m_zh)}}\n    </span>\n</div>\n  "},propFavActions={props:{dispVar:{type:Object,default:function(){return{lang:"zh-cn",allowedEditGrpName:!1,isLoggedIn:!1}}}},components:{},data:()=>({grp:null,grpName:"",cntr:1,MAX_GROUPS:5,grps:[],isArchived:!1,grpsSelect:!1,grpsEdit:!1,prop:{fav:!1,favGrp:[]},inputGrpName:"",grpsOptions:!1,mode:"new",showCrm:!1,clnt:null,strings:{clearTip:{key:"Are you sure you want to clear this folder?",ctx:""},deleteTip:{key:"Are you sure you want to delete this folder?",ctx:""},cancel:{key:"Cancel",ctx:""},delete:{key:"Delete",ctx:""},clear:{key:"Clear",ctx:""}},folderSort:"time"}),mounted(){if(!(r=window.bus))return console.error("global bus required!");var s=this,r=window.bus;r.$on("toggleFav",(function(r){s.dispVar.isLoggedIn?(s.grpsSelect=!0,s.prop=r,s.grpsEdit=!1,s.grps.length||s.getGroups({mode:"get"}),s.toggleGrpSelect()):location.href="/1.5/user/login"})),r.$on("editGroups",(function({grp:r,grpInfo:e}){s.grp=r,s.grpName=e.v,e.clnt&&e.clnt._id&&(s.clnt=e.clnt),s.grpsOptions=!s.grpsOptions})),r.$on("choosed-crm",(function(r){s.showCrm=!1,s.clnt=r,s.inputGrpName=s.inputGrpName.length?s.inputGrpName:r.nm})),r.$on("close-crm",(function(r){s.showCrm=!1})),r.$on("add-fav",(function(r){r.grps&&(s.grps=r.grps),s.addFav(r)})),r.$on("is-archived",(function(r){s.isArchived=r})),s.sortByMtWithoutDefault=window.sortByMtWithoutDefault},methods:{clientName:function(){return this.clnt&&this.clnt.nm?this.clnt.nm:""},showClientFn(){if(!this.dispVar.isVipRealtor){return(this.confirmVip||this.$parent.confirmVip)(this.dispVar.lang)}if(!this.clnt||!this.clnt.nm){this.showCrm=!0;var s={hide:!1,title:this.$_("Contacts","contactCrm")},r=RMSrv.appendDomain("/1.5/crm?noBar=1&isPopup=1&owner=1");RMSrv.getPageContent(r,"#callBackString",s,(function(s){if(":cancel"!=s)try{var r=JSON.parse(s);window.bus.$emit("choosed-crm",r)}catch(s){console.error(s)}else console.log("canceled")}))}},isInGrp(s){return(this.prop.favGrp||[]).indexOf(parseInt(s))>-1},editGrpName(){this.inputGrpName=this.grpName,this.addNewGrp(),this.mode="edit"},confirmDelOrClear(s){let r=this,e=`${s}Tip`,t=this.strings[s],p=this.strings[e],n=this.$_(p.key,p.ctx);var i=this.$_(this.strings.cancel.key,this.strings.cancel.ctx),a=this.$_(t.key,t.ctx);return RMSrv.dialogConfirm(n,(function(e){e+""=="2"&&("clear"==s?r.clearGrpFavs():"delete"==s&&r.removeGrp())}),"",[i,a])},clearGrpFavs(s={}){null!=this.grp&&this.getGroups({mode:"clear",grp:this.grp,noEmit:s.noEmit})},removeGrp(){this.getGroups({mode:"delete",grp:this.grp})},addNewGrp(){var s=this;if(!this.dispVar.allowedEditGrpName){return(s.confirmVip||s.$parent.confirmVip)(s.dispVar.lang)}this.grpsEdit=!0,this.grpsSelect=!1,this.grpsOptions=!1},reset(){this.grpsEdit=!1,this.grpsSelect=!1,this.grpsOptions=!1,window.bus.$emit("reset")},addGrpName(s){if(this.inputGrpName){var r={nm:this.inputGrpName};this.clnt&&this.clnt._id&&(r.clnt=this.clnt._id,r.cNm=this.clnt.nm),"edit"==this.mode?(r.mode="put",r.grp=this.grp):r.mode="set",this.getGroups(r)}},toggleGrpSelect(){this.grpsSelect=!0,this.grpsEdit=!1},parseGrps(s={}){var r=[];for(let e in s)"cntr"!==e&&r.push({idx:e,val:s[e],mt:s[e]?s[e].mt:-1});return this.sortByMtWithoutDefault(r)},getGroups(s){var r=this;r.reset(),trackEventOnGoogle("saves","properties",s.mode+"Group");fetchData("/1.5/props/propGroups",{body:s},(function(e,t){return e||t.err?RMSrv.dialogAlert(e||t.err):(r.inputGrpName="",t.grps&&(r.grps=r.parseGrps(t.grps),window.bus.$emit("prop-fav-retrieved",t.grps)),"set"!=s.mode&&"get"!=s.mode||r.toggleGrpSelect(),"set"==s.mode?r.selectGrp(r.grps[1]):((t.grps||"clear"==s.mode||"delete"==s.mode)&&(s.grps=t.grps,window.bus.$emit("afterFavAction",s)),void(r.clnt=null)))}))},selectGrp(s){var r=parseInt(s.idx)||0;this.addFav({prop:this.prop,grp:r})},addFav(s){var r=this,e="favour",t=s.prop;if(t.fav&&this.isInGrp(s.grp)&&(e="unfavor"),!r.loading){trackEventOnGoogle("saves","properties",e);var p={grp:s.grp,id:t._id,topTs:t.topTs,mode:e,src:t.src};fetchData("/1.5/props/favProp",{body:p},(function(p,n){if(p||n.err)return RMSrv.dialogAlert(p||n.err);var i="favour"==e;r.prop.favGrp=r.prop.favGrp||[],"favour"==e?(r.prop.favGrp||(r.prop.favGrp=[]),r.prop.favGrp.push(s.grp),r.grps.forEach((r=>{r&&r.idx==s.grp&&(r.mt=new Date)})),r.grps=r.sortByMtWithoutDefault(r.grps)):(r.prop.favGrp.splice(r.prop.favGrp.indexOf(s.grp),1),i=r.prop.favGrp.length),t.fav=i,r.grpsSelect=!1,window.bus.$emit("prop-fav-changed",{prop:t,grps:r.grps}),window.bus.$emit("flash-message",n.msg)}))}},archive(s){var r=this;if(!r.loading){trackEventOnGoogle("archive","folder");var e={grp:this.grp,flag:s};fetchData("/1.5/propFav/archive",{body:e},(function(s,e){if(s||e.e)return RMSrv.dialogAlert(s||e.e);r.isArchived=!r.isArchived,window.bus.$emit("flash-message",e.msg),r.reset(),window.bus.$emit("refresh-archive-folder",{grp:r.grp,isArchived:r.isArchived})}))}},resetClnt(){this.clnt=null,this.inputGrpName=this.inputGrpName.split(" ")[0]},gotoSaves(){var s="/1.5/saves/properties?grp=0";RMSrv.closeAndRedirectRoot?RMSrv.closeAndRedirectRoot(RMSrv.appendDomain(s)):window.location=s},sortFolder(s){this.grps=this.sortByMtWithoutDefault(this.grps,s),this.folderSort=s}},template:'\n  <div>\n  <div class="backdrop" :class=\'{show:(grpsOptions || grpsSelect ||grpsEdit),lowIndex:showCrm}\' @click=\'reset()\'></div>\n  <div class="modal modal-60pc" id="grpSelect" :class=\'{active:grpsSelect}\'>\n    <header class="bar bar-nav">{{$_(\'Save to\')}}\n      <span class="pull-right link" style="padding-right:0" @click="gotoSaves()">{{$_(\'All Saved\')}}</span>\n    </header>\n    <div class="folderSort" style="top: 44px;">\n      <span id="createBtn" @click="addNewGrp()">\n        <span class="fa icon icon-plus"></span>\n        <span>{{$_(\'Create New Folder\')}}</span>\n      </span>\n      <span class="sort">\n        <span :class="{select:folderSort == \'time\'}" @click="sortFolder(\'time\')">{{$_(\'Time\')}}<span class="fa fa-long-arrow-down"></span></span>\n        <span :class="{select:folderSort == \'name\'}" @click="sortFolder(\'name\')">{{$_(\'Name\')}}<span class="fa fa-long-arrow-up"></span></span>\n      </span>\n    </div>\n    <div class="content" style="padding-top: 97px;">\n      <ul class="table-view">\n        <li class="table-view-cell" v-for="g in grps" @click="selectGrp(g)">\n          <span class="fa fa-star" v-show="g.idx == \'0\'"></span>\n          <span class="fa" v-show="g.idx !== \'0\'"></span>\n          <span class="group-name">{{g.val.v}}</span>\n          <span class="pull-right fa" :class="{\'fa-heart-o\':!isInGrp(g.idx), \'fa-heart\':isInGrp(g.idx)}"></span>\n        </li>\n      </ul>\n    </div>\n  </div>\n  <div class="modal" id="grpEdit" :class="{active:grpsEdit,lowIndex:showCrm}">\n      <div class=\'bar bar-nav\'>{{$_(\'Edit Folder\')}}\n      </div>\n      <div class="addClient" @click.stop="showClientFn()" v-if="dispVar.isRealtor && mode != \'new\'"><span\n          class="sprite16-18 sprite16-3-6"></span>\n          <span class="editClientName">{{clientName() ||  $_("Choose a client")}}\n            <span class="lang" v-if="clientName() && clnt.lang">({{ $_(clnt.lang,\'lang\')}})</span>\n            <span class="lang" v-if="clientName() && !clnt.lang">(En)</span>\n          </span>\n        <span class="fa fa-rmclose" style="color:#aaa;padding:10px" v-show="clnt && clnt.nm " @click.stop="resetClnt()"></span>\n      </div>\n      <div class="bar bar-standard bar-header-secondary"><input v-model="inputGrpName" :placeholder="$_(\'Input folder name\')"/></div>\n      <div class="btn-cell bar bar-tab">\n      <a @click="addGrpName()" class="btn btn-tab btn-half btn-sharp btn-fill btn-negative">{{$_(\'Save\')}}</a>\n      <a @click="reset()" class="btn btn-tab btn-half btn-sharp btn-fill length">{{$_(\'Cancel\')}}</a>\n    </div>\n  </div>\n  <div class="modal" id="grpOpts" :class="{active:grpsOptions}">\n      <div class="content">\n          <ul class="table-view">\n              <li class="table-view-cell" @click="archive(true)" v-if=\'!isArchived && (grp != 0)\'><span>{{$_(\'Archive Folder\')}}</span></li>\n              <li class="table-view-cell" @click="archive(false)" v-if=\'isArchived && (grp != 0)\'><span>{{$_(\'Unarchive Folder\')}}</span></li>\n              <li class="table-view-cell" @click="editGrpName()"><span>{{$_(\'Edit Folder Name\')}}</span></li>\n              <li class="table-view-cell" @click="confirmDelOrClear(\'clear\')"><span>{{$_(\'Clear Folder\')}}</span></li>\n              <li class="table-view-cell" @click="confirmDelOrClear(\'delete\')"><span>{{$_(\'Delete Folder\')}}</span></li>\n          </ul>\n      </div>\n  </div>\n  <div style="display:none"><span v-for="(v,k) of strings">{{ $_(v.key, v.ctx)}}</span></div>\n</div>\n  '},similar={data:()=>({page:0,waiting:!1,hasMoreProps:!1,loading:!1,rcmdHeight:0,propImgHeight:0,maxLog:100,similarProps:[],simiLimit:50,buildLimit:20,showFavGroups:!1,prop:{fav:!1,favGrp:[]},isBuilding:!1,propId:"",propType:"",curSaleType:"Sold",curProp:{},lastSaleType:"",saleTypes:[{list:"SoldList",key:"Sold",val:"Sold"},{list:"LeasedList",key:"Leased",val:"Leased"},{list:"SaleList",key:"Sale",val:"For Sale"},{list:"RentList",key:"Rent",val:"For Rent"}],fromPage:"similar",SoldList:[],LeasedList:[],SaleList:[],RentList:[],dispVar:{lang:"zh-cn",isCip:!1,isLoggedIn:!1,isDevGroup:!1,isRealGroup:!1,sessionUser:{},allowedShareSignProp:!1,listShareMode:!0,isApp:!1},datas:["lang","isCip","isLoggedIn","isVipRealtor","isAdmin","isRealtor","isDevGroup","isRealGroup","allowedEditGrpName","sessionUser","shareUID","allowedShareSignProp","shareAvt","isVipUser","isApp"],filterCondit:[],selectedCondit:"",strings:{ERR_RETRY:"You are visiting too frequently in a short time period. Wait and retry later.",SCROLL_BOTTOM:"No more data"},showPropTable:!1,action:"",actionIsCMA:!1,favProps:[],currentGrp:{idx:"0",val:{v:"Default"}},grps:[],selectedProp:[],filterTime:[{k:8,v:8},{k:12,v:12},{k:18,v:18},{k:24,v:24}],wDl:!0,wSign:!0,shareList:[],SIM_OFFSET:250}),beforeMounted(){},mounted(){this.$getTranslate(this);var s=this;if(!(bus=window.bus))return console.error("global bus required!");function r(s){const[r,e]=s;var t=[{k:"",v:"Any"}];for(let s="filterCondit"==r?0:1;s<e+1;s++)t.push({k:s+"",v:s+""}),"filterCondit"==r&&0==s||t.push({k:s+"+n",v:s+"+•"});return t}vars.building&&(this.isBuilding=!0),vars.id&&(this.propId=vars.id),vars.type&&(this.propType=vars.type),s.getSimilarProps(),s.scrollElement=document.getElementById("similar"),s.scrollElement.addEventListener("scroll",s.listScrolled),s.rcmdHeight=parseInt(window.innerWidth/1.6),s.propImgHeight=parseInt((window.innerWidth-48)/1.6/2),s.getPageData(s.datas,{},!0),bus.$on("pagedata-retrieved",(function(r){s.dispVar=Object.assign(s.dispVar,r)})),bus.$on("reset",(()=>{this.reset()})),bus.$on("prop-fav-changed",(({prop:r})=>{this.prop=r,s.actionIsCMA&&(s.actionIsCMA=!1,s.action="cma",this.getFavPropList())})),bus.$on("commit-selected",(r=>{s.selectedProp=r,s.commitSelected()})),window.loadLazyImg&&loadLazyImg(),s.isBuilding?function(){for(let e of[["filterCondit",5]])s[e[0]]=r(e)}():(s.filterCondit=s.filterTime,s.selectedCondit=s.filterTime[0].k)},computed:{sessionUserContact:function(){var s,r=[];if(!this.wSign)return"";s=this.dispVar.sessionUser||{};var e="";return(e="en"==this.dispVar.lang?s.nm_en:s.nm_zh)?r.push(e):r.push(s.nm),s.mbl&&r.push(s.mbl),r.push(s.eml),r=r.join(", ")},shareImage:function(){return this.prop.thumbUrl?this.prop.thumbUrl:"/img/create_exlisting.png"},mShareImage:function(){return this.prop.thumbUrl?this.prop.thumbUrl:this.dispVar.shareAvt},mShareData:function(){var s=this.shareList,r=(this.prop,this.dispVar.lang),e=`id=${s.join(",")}&tp=listingList&lang=${r}&share=1`;return this.dispVar.allowedShareSignProp?(this.wDl&&(e+="&wDl=1"),this.wSign&&(e+="&aid="+this.dispVar.shareUID)):(e+="&wDl=1",e+="&uid="+this.dispVar.shareUID),e},shareData:function(){var s,r=this.prop,e=this.dispVar.lang;s=this.isRMProp(r)?r.id:r._id||r.sid;var t=this.dispVar.shareUID,p=`id=${s}&tp=listing&lang=${e}`;return this.isRMProp(r)&&(t&&!function(s){return(s.adrltr?s.adrltr._id:null)==s.uid}(r)||(t=r.uid)),this.dispVar.allowedShareSignProp?(this.wDl&&(p+="&wDl=1"),this.wSign&&(p+="&aid="+this.dispVar.shareUID)):(p+="&wDl=1",this.dispVar.isLoggedIn&&(p+="&uid="+t)),this.isRMProp(r)&&(/wDl=1/.test(p)||(p+="&wDl=1"),p+="&wSign=1"),p}},methods:{isRMProp:s=>/^RM/.test(s.id),selecteBd(s){this.selectedCondit=s.k,this.page=0,this.getSimilarProps(),document.getElementById("similar").scrollTop=0},calcDirection:function(s,r){let e=this.curSaleType,t=this.lastSaleType,p=this.saleTypes.findIndex((s=>s.key==e)),n=this.saleTypes.findIndex((s=>s.key==t));return r==p?n>r?"r2l":"l2r":r==n?"out":""},selectType(s){s&&(this.lastSaleType=this.curSaleType,this.curSaleType=s.key,s.list,document.getElementById("similar").scrollTop=0),this.lastSaleType!=this.curSaleType&&(this.page=0,this.getSimilarProps())},savePropList(s){this.curProp=s.origProp;let r=s.items;this.isBuilding?s.items.length>=this.buildLimit?this.hasMoreProps=!0:this.hasMoreProps=!1:(r.length>0&&r.forEach((s=>{s=this.compareDifference(s)})),s.items.length>=this.simiLimit?this.hasMoreProps=!0:this.hasMoreProps=!1),this[this.curSaleType+"List"]=this[this.curSaleType+"List"].concat(r),this.similarProps=this[this.curSaleType+"List"]},reset:function(){var s=this;s.grpsOptions=!1,s.showFavGroups=!1,s.showPropTable=!1,s.action="",s.selectedProp=[],window.bus.$emit("reset-sel-prop")},toggleFav(s){this.showFavGroups=!0,this.prop=s},listScrolled:function(){var s=this;!s.waiting&&s.hasMoreProps&&(s.waiting=!0,s.loading=!0,setTimeout((function(){s.waiting=!1;var r=s.scrollElement;r.scrollHeight-r.scrollTop<=r.clientHeight+260&&s.hasMoreProps?(s.page+=1,s.getSimilarProps()):s.loading=!1}),400))},getSimilarProps:function(){var s=this,r="/1.5/similar",e={page:this.page,limit:this.simiLimit,id:this.propId,type:this.propType,month:this.selectedCondit};if(e.saleDesc=this.curSaleType,/sold|sale/i.test(this.curSaleType)?e.saletp="Sale":e.saletp="Lease",this.isBuilding)r+="/building",e.bdrms=this.selectedCondit,e.limit=this.buildLimit,delete e.month;else{let r=this.page*this.simiLimit;if(r>this.SIM_OFFSET)return window.bus.$emit("flash-message",s.$_(s.strings.SCROLL_BOTTOM));e.offset=r}0==this.page&&(this.SoldList=[],this.LeasedList=[],this.SaleList=[],this.RentList=[]),setLoaderVisibility("block"),fetchData(r,{body:e},(function(r,e){return setLoaderVisibility("none"),r&&403==r.status?RMSrv.dialogAlert(s.$_(s.strings.ERR_RETRY)):r||e.err?RMSrv.dialogAlert(r||e.err):void s.savePropList(e)}))},compareDifference(s){var r=this;return"number"==typeof s.bdrms&&"number"==typeof r.curProp.bdrms&&(s.bdrms_diff=s.bdrms-r.curProp.bdrms),"number"==typeof s.bthrms&&"number"==typeof r.curProp.bthrms&&(s.bthrms_diff=s.bthrms-r.curProp.bthrms),"number"==typeof s.gr&&"number"==typeof r.curProp.gr&&(s.gr_diff=s.gr-r.curProp.gr),s.lotsz_code==r.curProp.lotsz_code&&"number"==typeof s.depth&&"number"==typeof s.front_ft&&"number"==typeof r.curProp.depth&&"number"==typeof r.curProp.front_ft&&(s.front_ft_diff=Math.round(s.front_ft-r.curProp.front_ft),s.depth_diff=Math.round(s.depth-r.curProp.depth),s.size_diff=parseInt(s.front_ft*s.depth-r.curProp.front_ft*r.curProp.depth)),"number"==typeof s.sqft&&"number"==typeof r.curProp.sqft?(s.sqft_diff=parseInt(s.sqft-r.curProp.sqft),s.sqft_diff_abs=Math.abs(parseInt(s.sqft-r.curProp.sqft))):/\-/.test(s.sqft)&&/\-/.test(r.curProp.sqft)?s.sqft_diff=parseInt(s.sqft1+s.sqft2-r.curProp.sqft1-r.curProp.sqft2)/2:"number"==typeof s.sqft1&&"number"==typeof s.sqft2&&"number"==typeof r.curProp.sqft?s.sqft_diff=parseInt(s.sqft2-r.curProp.sqft):"number"==typeof s.sqft&&"number"==typeof r.curProp.sqft1&&"number"==typeof r.curProp.sqft2?s.sqft_diff=parseInt(s.sqft-r.curProp.sqft2):"number"==typeof s.sqft1&&"number"==typeof s.sqft2&&"number"==typeof r.curProp.sqft1&&"number"==typeof r.curProp.sqft2?s.sqft_diff=(parseInt(s.sqft1-r.curProp.sqft1)+parseInt(s.sqft2-r.curProp.sqft2))/2:"number"==typeof s.rmSqft&&"number"==typeof r.curProp.rmSqft?(s.rmSqft_diff=parseInt(s.rmSqft-r.curProp.rmSqft),s.rmSqft_diff_abs=Math.abs(parseInt(s.rmSqft-r.curProp.rmSqft))):/\-/.test(s.rmSqft)&&/\-/.test(r.curProp.rmSqft)?s.rmSqft_diff=parseInt(s.rmSqft1+s.rmSqft2-r.curProp.rmSqft1-r.curProp.rmSqft2)/2:"number"==typeof s.rmSqft1&&"number"==typeof s.rmSqft2&&"number"==typeof r.curProp.rmSqft?s.rmSqft_diff=parseInt(s.rmSqft2-r.curProp.rmSqft):"number"==typeof s.rmSqft&&"number"==typeof r.curProp.rmSqft1&&"number"==typeof r.curProp.rmSqft2?s.rmSqft_diff=parseInt(s.rmSqft-r.curProp.rmSqft2):"number"==typeof s.rmSqft1&&"number"==typeof s.rmSqft2&&"number"==typeof r.curProp.rmSqft1&&"number"==typeof r.curProp.rmSqft2&&(s.rmSqft_diff=(parseInt(s.rmSqft1-r.curProp.rmSqft1)+parseInt(s.rmSqft2-r.curProp.rmSqft2))/2),s.st&&s.st==r.curProp.st&&s.prov==r.curProp.prov&&s.city==r.curProp.city&&(s.sameStreet=!0),(vars.fromMls||r.curProp.mlsid)&&!s.sameStreet&&s.cmty&&s.cmty==r.curProp.cmty&&s.prov==r.curProp.prov&&s.city==r.curProp.city&&(s.sameCmty=!0),s},rmbdrmStr:function(s){if(s.rmbdrm)return s.rmbdrm;let r=s.bdrms||s.tbdrms;return r+=s.br_plus?"+ "+s.br_plus:"",r},rmbthrmStr:function(s){return s.rmbthrm||s.tbthrms||s.bthrms},parseSqft:s=>/\-/.test(s)?s:("number"==typeof s&&(s=""+s),/\./.test(s)?s.split(".")[0]:s),goBack(){vars.d?document.location.href=vars.d:window.history.back()},getFavPropList(){let s=this;fetchData("/1.5/propFav/getGrpAndFavProps",{body:{id:s.propId}},(function(r,e){if(e?.ok)s.favProps=e.items,s.currentGrp=e.currentGrp,s.grps=e.grps,s.showPropTable=!0;else{if("Not Found"!=e?.msg)return RMSrv.dialogAlert(r||e.e);s.actionIsCMA=!0,window.bus.$emit("toggleFav",s.curProp)}}))},popupSelect(s){var r=this;r.action==s?r.reset():(r.action=s,r.getFavPropList())},confirmVip:function(s,r){r=r||"Available only for Premium VIP user! Upgrade and get more advanced features.";var e=this.$_(r),t=this.$_("Later"),p=this.$_("SeeMore");return RMSrv.dialogConfirm(e,(function(s){s+""=="2"&&RMSrv.showInBrowser("https://www.realmaster.ca/membership")}),"VIP",[t,p])},goCMA(){let s=`/1.5/htmltoimg/dottemplate?lang=${this.dispVar.lang}&showSign=true&&selModel=diff&nm=comparetable&ids=`;var r=this.selectedProp;r&&("string"!=typeof r&&(r=r.join(",")),s+=r);let e={title:this.$_("RealMaster")};openContent(s,e)},commitSelected(){var s=this;if(s.showPropTable=!1,"cma"==s.action)this.goCMA();else if(1==s.selectedProp.length){for(var r of s.favProps)r._id==s.selectedProp[0]&&(this.prop=r);RMSrv.showSMB("show")}else RMSrv.showSMB("show","m-share-");s.shareList=s.selectedProp,window.bus.$emit("reset-sel-prop"),s.selectedProp=[],s.action="",s.favProps.forEach((s=>{s.selected=!1}))},closeSimilar(){window.rmCall(":ctx:"+JSON.stringify({fav:1}))},propCMAOrShareSel(s){this.action=s;this.favProps=[].concat([this.curProp],this.similarProps),this.showPropTable=!0}}};initUrlVars();var app=Vue.createApp(similar);app.component("prop-item",propItem),app.component("prop-fav-actions",propFavActions),app.component("prop-sel-table",propSelTable),app.component("flash-message",flashMessage),app.component("share-dialog",shareDialog),app.component("listing-share-desc",listingShareDesc),app.mixin(pageDataMixins),trans.install(app,{ref:Vue.ref}),app.mount("#similar");
