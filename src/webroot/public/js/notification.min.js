"use strict";var appProf;(appProf=angular.module("appProfile",[])).controller("ctrlNotifications",["$scope","$http",function(e,r){var t,n,o;return e.loading=!0,e.formData=vars.noteOpt||{},e.hasPn=!0,e.showDiagnosing=!1,e.curStatus=vars.curStatus,e.showBackdrop=vars.showBackdrop,e.emailStatusList=vars.emailStatus,e.hasGoogleService=!0,e.canRefresh=!1,e.refresh=!1,e.is63NewerVer=isNewVersion(vars.coreVer,"6.3.1"),e.is65NewerVer=isNewVersion(vars.coreVer,"6.4.0"),e.getEmailStatus=function(){return e.emailStatus=e.emailStatusList[e.curStatus]},e.getEmailStatus(),e.openVerifyPopup=function(){return"/1.5/settings/verify?d=/1.5/settings/editProfile&verify=v",e.loading=!0,setTimeout((function(){return e.loading=!1,e.$apply()}),1e3),RMSrv.getPageContentIframe("/1.5/settings/verify?d=/1.5/settings/editProfile&verify=v","#callBackString",{transparent:!0},(function(r){var t;if(":cancel"!==r)try{return!0===JSON.parse(r).emlV&&(e.curStatus="unsubscribe",e.showBackdrop=!1,e.getEmailStatus()),e.$apply()}catch(e){return t=e,console.error(t)}}))},vars.showverify&&e.openVerifyPopup(),e.linkTo=function(r){return RMSrv.getPageContent(r,"#callBackString",{toolbar:!1,hide:!1},(function(r){var t;if(":cancel"===r);else try{return e.$apply()}catch(e){return t=e,console.log(t)}}))},null!=RMSrv.action&&RMSrv.action("pushToken"),n=function(){return["authorized","granted"].indexOf(arguments.length>0&&void 0!==arguments[0]?arguments[0]:"")>-1},t=function(){return setTimeout((function(){e.loading=!1,e.$apply()}),10)},e.checkNotification=function(){return e.refresh=!1,RMSrv.permissions("notification","check",-1,(function(r){return e.hasPn=n(r),t()}))},RMSrv.hasGoogleService&&RMSrv.hasGoogleService((function(r){return e.hasGoogleService=r,!0===r?(e.checkNotification(),e.is63NewerVer&&RMSrv.onAppStateChange?RMSrv.onAppStateChange((function(r){return e.loading=!0,e.checkNotification()})):(e.canRefresh=!0,t())):(e.hasPn=!1,t())})),RMSrv.whenReg((function(){return t()})),setTimeout((function(){return t()}),3e3),e.openSetting=function(){return e.refresh=!0,RMSrv.openSettings()},e.goBack=function(){return goBack2({d:vars.d,isPopup:vars.isPopup})},e.turnOffAll=function(){var t,n,o;if(!0!==e.post){for(t in e.post=!0,n={colseAll:1},e.formData)/^edm/.test(t)&&(e.formData[t]=1);return(o=r.post("/1.5/settings/subscription",n)).success((function(r){return e.message=r.message,r.ok?e.post=!1:RMSrv.dialogAlert("Error")})),o.error((function(r){return e.message=r.message,alert("Error when turn off all")}))}},o=function(t,n,o){var s,i,a;if(!0!==e.post)return e.post=!0,(s={})[n]=o=1===o?0:1,e.formData[n]=o,a="/1.5/settings/subscription","mobile"===t&&(a="/1.5/settings/notification"),(i=r.post(a,s)).success((function(r){return e.message=r.message,r.ok?e.post=!1:RMSrv.dialogAlert("Error")})),i.error((function(r){return e.message=r.message,alert("Error when saving pref")}))},e.showModel=function(r){return"email"!==r?isNewVersion(vars.coreVer,"6.1.3")?(e.showDiagnosing=!0,window.checkHasGooglePlay?checkHasGooglePlay():console.error("checkHasGooglePlay not exist")):e.showAppUpgrade=!0:"unverified"===e.curStatus?e.openVerifyPopup():void 0},e.emailAction=function(t,n){var o,s,i;if("mobile"===n||"verify"===t)return e.showModel(n);if("set"===t)o="resubscribe";else{if("unset"!==t)return;o="unsubscribe"}return(s={}).action=t,"/1.5/settings/receving",(i=r.post("/1.5/settings/receving",s)).success((function(r){return r.ok?(flashMessage(r.message),e.curStatus=o,"resubscribe"===o?e.showBackdrop=!0:"unsubscribe"===o&&(e.showBackdrop=!1),e.getEmailStatus()):RMSrv.dialogAlert("Error")})),i.error((function(r){return e.message=r.err,alert("Error when saving pref")}))},window.onload=function(){var r;return r=vars.option,Object.getOwnPropertyNames(r).forEach((function(t){return r[t].flds.forEach((function(r,n){var s,i;if(r.tag&&(i="#".concat(r.tag),s=document.querySelector(i)))return s.addEventListener("click",(function(n){return o(t,r.tag,e.formData[r.tag])}))}))}))}}]);
