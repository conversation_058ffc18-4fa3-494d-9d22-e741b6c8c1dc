"use strict";var app,autocomplete,fillInAddress,initAutocomplete,listingCtrl,setMarker;listingCtrl=function(e,t,r,o,n){var a,i,s,l,d,p,u,c,m,f,g,v,h,y,M,D,w,S,b,A,k;for(v=["addr","st_num","st","city","cmty","prov","cnty","lat","lng"],h="picUrls",e.disabledInsert=!1,e.type=vars.type,e.isAssignAdmin=vars.isAssignAdmin,e.hideBackdrop=!0,e.isArray=angular.isArray,e.formData={},e.userData={},e.userForm={src:"myListingNonRealtor"},f=0,g=(D=["nm","eml","mbl"]).length;f<g;f++)c=D[f],vars["userInfo"+c]&&(e.userForm[c]="mbl"===c?parseInt(vars["userInfo"+c]):vars["userInfo"+c]);return e.user={vip:1},e.minDate=new Date,e.ml_num=vars.ml_num,e.roomNumbers=[1,2,3,4,5,6,7,8,9,10,11,12],e.hasMore=!1,e.wSign=!0,e.wDl=!0,e.tmpData={},e.curBrkg={},e.selectedProp=[],e.toShare=1,e.contactList=[],e.contactInfo={nm:"",mbl:"",id:null},e.isAddContact=!1,e.isShowDelete=!1,e.allListing=[],e.showSearchBar=!1,e.searchId="",e.verified=vars.verified,e.resetSearch=function(){return e.showSearchBar=!1,e.searchId="",e.mylistings=e.allListing,r((function(){return e.$apply()}))},e.$watch("searchId",(function(t,r){var o;if(""!==r&&""===t)return e.resetSearch();if(null!=t&&t!==r&&t.length>2){if(e.loading)return;return clearTimeout(o),setTimeout((function(){return e.loading=!0,e.mylistings=[],S(t)}),500)}})),S=function(o){return t.post("/1.5/promote/search",{search:e.searchId,skip:e.mylistings.length}).success((function(t){if(!t.ok)return console.log("Err:del "+t.err);e.loading=!1,e.mylistings=t.list,r((function(){return e.$apply()}))})).error((function(e){return RMSrv.dialogAlert(e.toString())}))},e.openOwnerInfo=function(e){var t,r;return r="/1.5/wesite/".concat(e,"?inFrame=1"),t={hide:!1,title:"Owner Info"},RMSrv.getPageContent(r,"#callBackString",t,(function(e){}))},e.openVerifyPopup=function(t,o){var n;if(!o||!0!==e.verified[o]){if(!t)if(!1===e.verified.emlV)t="v";else{if(!1!==e.verified.mblV)return;t="p"}return n="/1.5/settings/verify?d=/1.5/settings/editProfile&verify="+t,e.loading=!0,setTimeout((function(){return e.loading=!1,e.$apply()}),1e3),RMSrv.getPageContentIframe(n,"#callBackString",{transparent:!0},(function(t){var o,n,a,i;if(":cancel"!==t)try{if("phone"===(a=JSON.parse(t)).delete)e.userData.mbl="",e.verified.mbl="",e.verified.mblV=!1;else for(n in a)i=a[n],"eml"===n?e.userData[n]=i:"mbl"===n&&(e.userData[n]=Number(i)),e.verified[n]=i;return r((function(){return e.$apply()}))}catch(e){return o=e,console.error(o)}}))}},vars.isRealtor||(e.wSign=!1),"showSMB"===vars.action&&(toggleModal("newListingModal"),e.hideBackdrop=!1),m=function(e){var t;if(6!==e.length)return!1;for(c=t=0;t<=5;c=++t)if(c%2==0){if(!e[c]||!e[c].match(/[a-z]/i))return!1}else if(!e[c]||!e[c].match(/[0-9]/i))return!1;return!0},w=function(t){var r,o,n;for(r=0,o=v.length;r<o;r++)c=v[r],e[t][c]="";if(e[t].zip="",null!=(n=e[t])?n.subCity:void 0)return e[t].subCity=""},s=function(e){var t,r,o,n;if(e){for(n="",t=0,r=(o=["addr","city","prov","zip","cnty"]).length;t<r;t++)e[c=o[t]]&&(n+=e[c]||"","prov"===c?e.zip?n+=" ":n+=",":n+="cnty"===c?" ":", ");return n}},u=function(r){var o;return o=function(t){return e.err=t.err,flashMessage("server-error")},t.post("/1.5/promote/userInfo.json?to="+r,{},{timeout:5e3}).success((function(t){var r,n,a;return r=function(e){return"#0"===(""+e).substr(-2)?(""+e).slice(0,-1).replace(/\D/g,""):(""+e).replace(/\D/g,"")},t.ok?(Array.isArray(t.user.eml)&&(t.user.eml=t.user.eml[0]),t.user.mbl&&(t.user.mbl=parseInt(r(t.user.mbl))),e.userData=t.user,e.original=angular.copy(t.user),e.dataHasChanged=!1,e.userData.tl?void 0:((a=(n=(e.userData.ln||"")+(e.userData.fn||"")+" "+(e.userData.cpny||"")+" "+(e.userData.cpny_pstn||""))+" "+(e.userData.itr||"")).length<64&&(e.userData.tl=a),e.userData.tl=n.substr(0,64),e.dataHasChanged=!0)):o(t)})).error((function(e){return o(e)}))},function(){if(e.favCities)return null;t.post("/1.5/props/cities.json",{}).success((function(t){var r,o,n,a;for(o=0,n=(a=t.fc).length;o<n;o++)(r=a[o]).k=r.o+"_"+r.p;return e.favCities=t.fc,e.extCities=t.ec})).error((function(t){return e.message=t.message,RMSrv.dialogAlert("Error when getting city list!")}))},A=function(t){var o,n,a,i;if(t.id)for(o=0,n=(i=e.mylistings).length;o<n;o++)if((a=i[o]).id===t.id)return"market"!==t.to||t.val||(t.val={adok:1},e.isAssignAdmin&&(t.val.rmProp="")),a[t.to]=t.val,null==a.tl58&&(a.tl58=e.curProp.tl58),null==a.addr58&&(a.addr58=e.curProp.addr58),e.curProp&&(e.curProp=a),void r((function(){return e.$apply()}))},e.signUp=function(){var r;if(r=function(t,r){if(t&&(e.nmErr=!0),r)return e.emlErr=!0},e.emlErr=!1,e.nmErr=!1,!e.userForm)return r(1,1);if(e.userForm.eml||r(0,1),e.userForm.nm||r(1,0),e.userForm.nm&&e.userForm.eml&&!e.sending){if(!isValidEmail(e.userForm.eml))return r(0,1);e.sending=!0,e.userForm.formid="system",e.userForm.tp="rmlisting",e.userForm.url=document.URL,t.post("/1.5/form/forminput",e.userForm).success((function(t){var r;return e.sending=!1,r=document.querySelector("#signUpSuccess"),t.ok?(document.querySelector("#signUpForm").style.display="none",flashMessage("sendSuccess")):r.textContent=t.err,r.style.display="block"})).error((function(){return e.sending=!1,RMSrv.dialogAlert("Server Error")}))}},e.toggleModal=function(e,t){return toggleModal(e,t)},e.saveAndPromote=function(t){return e.submitForm(t,!0)},e.goBack=function(){return vars.d?window.location=vars.d:window.history.back()},e.showInBrowser=function(e){return RMSrv.showInBrowser(e)},e.dateConfirm=function(){if(e.rangeMode){if(!e.dateFrom||!e.dateTo)return;delete e.formData[e.fldTobeUpdated],e.formData[e.fldTobeUpdated+"f"]=e.dateFrom,e.formData[e.fldTobeUpdated+"t"]=e.dateTo}else{if(!e.date)return;e.formData[e.fldTobeUpdated]=new Date(e.date),delete e.formData[e.fldTobeUpdated+"f"],delete e.formData[e.fldTobeUpdated+"t"]}return e.toggleModal("datePickerModal"),e.hideBackdrop=!0,e.propForm.$dirty=!0,null},e.highLightPromoteIcon=function(e,t,r){var o,n,a,i;return"wecard"===t?r?e.rcmd:!e.rcmd:"market"!==t||"mlslisting"!==(o=e.ltp)&&"mlsrent"!==o?"To Be Promoted"===(a=null!=(i=e[t])?i.st:void 0)||"Promo Pending"===a||"Published"===a||"Promoted"===a||"Waiting Approval"===a?r:!r:"Promoted"===(null!=e&&null!=(n=e.market)?n.st:void 0)&&r},a=function(){var t,r;for(t=0,r=v.length;t<r;t++)if(c=v[t],e.formData[c]&&e.tmpData[c]&&"string"==typeof e.formData[c]&&0!==e.formData[c].localeCompare(e.tmpData[c]))return!0;return e.formData.zip!==e.tmpData.zip},e.closeAddrModal=function(){var t,r;if(a()||e.inputZipRet!==e.tmpData.zip){for(t=0,r=v.length;t<r;t++)c=v[t],e.formData[c]=e.tmpData[c];e.tmpData.subCity&&e.tmpData.subCity.length&&(e.formData.subCity=e.tmpData.subCity),!e.tmpData.subCity&&e.formData.subCity&&delete e.formData.subCity,e.formData.zip=e.inputZipRet,e.propDataHasChanged=!0,e.propForm.$dirty=!0}if(e.hideBackdrop=!0,RMSrv)return RMSrv.clearCache()},e.showDate=function(t,r){var o;return"tba"===t||"immed"===t?t===e.formData[r]:!("date"!==t||!e.formData[r]||"tba"===(o=e.formData[r])||"immed"===o)||!("range"!==t||!e.formData[r+"f"])},e.height=function(e){return e=e[0]||e,isNaN(e.offsetHeight)?e.document.documentElement.clientHeight:e.offsetHeight},e.popAddrModal=function(){var t,r,o,n,a;return r={hide:!1,title:"Select Address"},a="/1.5/map/searchLocation?1=1",o=e.formData.lat,n=e.formData.lng,o&&n&&(a=a+"&lat="+o+"&lng="+n),(t=s(e.formData))&&(a=a+"&addr="+t),a=RMSrv.appendDomain(a),RMSrv.getPageContent(a,"#callBackString",r,(function(t){var r,o;if(":cancel"===t);else try{return o=JSON.parse(t),w("tmpData"),e.inputAddrRet=o.address,e.inputZipRet=o.zip,e.tmpData=o,e.$apply(),e.closeAddrModal()}catch(e){return r=e,console.log(r)}}))},0,e._generateMap=function(t){var r,o,n,a,i;if(document.getElementById("id_d_map").style.height=window.innerHeight-210+"px",43.7182412,-79.378058,t,n=void 0,a=void 0,void 0,o=void 0,r=e.inputAddr||e.inputSearchAddr||"Toronto, ON, CA",i={zoom:12,center:new google.maps.LatLng(43.7182412,-79.378058),mapTypeControl:!1,mapTypeControlOptions:{style:google.maps.MapTypeControlStyle.DROPDOWN_MENU},navigationControl:!1,mapTypeId:google.maps.MapTypeId.ROADMAP},window.map||(n=new google.maps.Map(document.getElementById("id_d_map"),i),window.map=n),o=new google.maps.Geocoder)return o.geocode({address:r},(function(e,t){var i;return i=function(e){return o.geocode({latLng:e},(function(e){var t;return e&&e.length>0?((t=angular.element(document.getElementById("realtorUtilPage")).scope()).inputAddr=e[0].formatted_address,t._processRetAddr(e[0]),t.$apply()):console.log("Cannot determine address at this location.")}))},t!==google.maps.GeocoderStatus.OK?RMSrv.dialogAlert("Geocode was not successful for the following reason: "+t):t===google.maps.GeocoderStatus.ZERO_RESULTS?RMSrv.dialogAlert("No results found"):(window.map.setCenter(e[0].geometry.location),window.marker?void 0:(window.marker=a=new google.maps.Marker({position:e[0].geometry.location,map:n,draggable:!0,animation:google.maps.Animation.DROP,title:r,optimized:!1}),a.addListener("click",(function(){return null!==a.getAnimation()?a.setAnimation(null):a.setAnimation(google.maps.Animation.BOUNCE)})),google.maps.event.addListener(a,"dragend",(function(){return i(a.getPosition())}))))}))},e._processRetAddr=function(t){var r,o,n,a,i,l,d,p,u,m,f,g,v;if(null!=t?t.address_components:void 0){for(c=o=0,l=t.address_components.length-1;0<=l?o<=l:o>=l;c=0<=l?++o:--o)if("postal_code"===(v=t.address_components[c].types[0])){e.fullAddr=!0,e.gotFullAddrFromSearch=!0;break}for(w("tmpData"),e[r="tmpData"].lat="function"==typeof(null!=(d=t.geometry)&&null!=(p=d.location)?p.lat:void 0)?t.geometry.location.lat():t.geometry.location.lat,e[r].lng="function"==typeof(null!=(u=t.geometry)&&null!=(m=u.location)?m.lng:void 0)?t.geometry.location.lng():t.geometry.location.lng,c=a=0,f=t.address_components.length-1;0<=f?a<=f:a>=f;c=0<=f?++a:--a)v=t.address_components[c].types[0],i=t.address_components[c].short_name,n=t.address_components[c].long_name,"street_number"===v?(e[r].addr=i+" ",e[r].st_num=i||""):"route"===v?(e[r].addr+=i,e[r].st=i):"neighborhood"===v?e[r].cmty=i:"locality"===v?e[r].city=i:"administrative_area_level_2"===v||("administrative_area_level_1"===v?e[r].prov=n:"country"===v?e[r].cnty=n:"postal_code"===v||"postal_code_prefix"===v?(e.zipRet3=i.substr(0,3),e.inputZipRet=i.replace(/\s+/g,""),e[r].zip=i.replace(/\s+/g,"")):console.log(v));return e[r].st_num||(g=e.extratStNum(e.inputAddr))&&(e[r].st_num=g,e[r].addr=g+" "+e[r].addr),e.searchAddrRet=s(e.tmpData),null}},e.extratStNum=function(e){return/^\d/.test(e)?e.split(" ")[0]:""},e.searchAddr=function(){var r,o;return r=e.inputAddr||e.inputSearchAddr||"Toronto, ON, CA",e.fullAddr=!1,e.inputZipRet="",e.gotFullAddrFromSearch=!1,"string"==typeof r&&r.toLowerCase().indexOf("ca")<0&&(r+="&components=country:ca"),o="?",vars.gmap_geocode.indexOf("?")>0&&(o="&"),t.get(vars.gmap_geocode+o+"language=en&address="+r).success((function(t){var r,o;return"OK"===t.status?t.results.length?(o=t.results,console.log(o[0]),r=o[0].geometry.location,setMarker(r),e._processRetAddr(o[0])):RMSrv.dialogAlert("No results found"):RMSrv.dialogAlert("Geocode was not successful for the following reason: "+status)})).error((function(e){return console.log(e)}))},e.showImgSelect=function(){return insertImage({url:"/1.5/img/insert"},(function(t){var r,o,n,a,i,s;if(":cancel"!==t)try{if((s=JSON.parse(t)).e||s.err||"alert"===s.tp)return;if(s.userFiles&&!e.userFiles&&(e.userFiles=s.userFiles),0!==s.picUrls.length)for(e.propForm.$dirty=!0,i=s.picUrls,n=0,a=i.length;n<a;n++)o=i[n],-1===e.picUrls.indexOf(o)&&(e.picUrls.push(o),e.$apply())}catch(e){r=e,console.error(r)}else console.log("canceled")}))},e.setCnum=function(t,r){return void 0===e.formData[t]?(e.formData[t]=1,null):(e.formData[t]=parseInt(e.formData[t])||0,0===e.formData[t]&&-1===r?null:(e.formData[t]+=r,e.propForm.$dirty=!0))},e.setBool=function(t,r){return 0!==e.formData[t]&&1!==e.formData[t]||e.formData[t]!==r?(e.formData[t]=r,e.propForm.$dirty=!0):e.formData[t]=void 0},e.save=function(){if(e.toggleModal("savePromoteModal","close"),e.propDataHasChanged=!1,e.changeSaved=!0,e.canSave)return"Active"===e.formData.status?e.toggleModal("savePromoteModal","open"):e.hideBackdrop=!0},e.cancelSaveListing=function(){if(toggleModal("saveTipModal"),e.hideBackdrop=!0,e.changeSaved=!0,e.tip_save_change)return toggleModal("editListingModal","close"),e.tip_save_change=!1},e.cancelPromoteModal=function(){return toggleModal("savePromoteModal"),e.hideBackdrop=!0,e.changeSaved=!0},e.showPromote=function(t){return t&&"Active"===e.formData.status?(e.tip_save_ready=!0,toggleModal("savePromoteModal"),e.hideBackdrop=!1):(flashMessage("tip_save_err"),e.showError=!0,null)},e.getInvalidInput=function(){var t,r,o,n;for(t=0,r=(o=["bdrms","bthrms","kch","gr","lp","mfee","tax"]).length;t<r;t++)if(c=o[t],null!=e.propForm[c]&&e.propForm[c].$dirty){if(null===(n=e.formData[c]))continue;if("mfee"!==c&&"tax"!==c&&null==n)return c;if(!/\d+/.test(n))return c}return null},e.deleteProp=function(o){var n,a,i,s;if(o.id)return s=vars.str_confirm||"Confirm",a=vars.vipLaterStr||"Later",i=vars.str_yes||"Yes",n=function(n){var a;if(n+""=="2")return a={id:o.id,_id:o._id},t.post("/1.5/promote/api",{cmd:"pmt-p-delete",data:a}).success((function(t){return t.ok?(e.formData.id&&e.promptSaveModal(),flashMessage("deleted"),vars.isPopup?window.rmCall(":ctx::cancel"):(e.loadListings(e.type,0),void r((function(){return e.$apply()})))):console.log("Err:del "+t.err)})).error((function(e){return RMSrv.dialogAlert(e.toString())}))},RMSrv.dialogConfirm(s,n,"",[a,i])},e.submitForm=function(o,n){var a,i,s,l,d,p;return o||(e.showError=!0),!1===o&&!0===n?flashMessage("publish_draft_err"):(e.hideBackdrop=!1,e.tip_save_err=!1,e.tip_save_ready=!1,e.tip_save_change=!1,e.tip_save_inactive=!1,e.tip_save_other=!1,(a=e.getInvalidInput())?(e.hideBackdrop=!0,e.showError=!0,i="",document.querySelector("input[name="+a+"]")&&(d=document.querySelector("div[field-name="+a+"]"))&&(i=d.textContent+" "),s=document.querySelector("#fm-integers-only").innerText,document.querySelector("#fm-integers-only2 .flash-message-inner").textContent=i+s,flashMessage("integers-only2")):(e.formData.rvkRsn&&(e.formData.rvkRsn=""),e.canSave=!0,"Active"===(p=e.formData.status)?e.tip_save_ready=!0:"Inactive"===p?e.tip_save_inactive=!0:e.tip_save_other=!0,e.hideBackdrop=!0,e.picUrls&&(e.formData.pic=proplib.convert_rm_imgs(e,e.picUrls,"set"),e.formData.pho=null!=(l=e.picUrls)?l.length:void 0),e.formData.ptp&&(e.formData.pstyl=e.formData.ptp[1]),e.formData.addr&&(e.formData.addr58=e.formData.addr),"epl"===e.formData.rtp&&(e.formData.rgdr=null),e.changeSaved=!0,e.formData.isValid=o,t.post("/1.5/promote/api",{cmd:"pmt-p-save",data:{prop:e.formData}},{timeout:5e3}).success((function(t){if(!t.ok)return RMSrv.dialogAlert("Err:save "+t.err);t.id&&(e.formData.id=t.id),e.prop=e.formData,flashMessage("saved"),e.loadListings(e.type,0),r((function(){if(e.changeSaved=!0,n)return e.promote("market",e.prop)}),100),vars.promoteAfterSave&&(e.promote(vars.to,e.formData),vars.promoteAfterSave=0),r((function(){return e.$apply()}))})).error((function(e){return RMSrv.dialogAlert(e.toString())})),e.propStatus=e.formData.status,e.loadListings(e.type,0)))},e.dialogAlertVip=function(){var e,t,r,o;return o=vars.vipTipStr||"Available only for Premium VIP user! Upgrade and get more advanced features.",t=vars.vipLaterStr||"Later",r=vars.vipSeeStr||"See More",e=function(e){if("https://www.realmaster.ca/membership",e+""=="2")return RMSrv.showInBrowser("https://www.realmaster.ca/membership")},RMSrv.dialogConfirm(o,e,"VIP",[t,r])},e.promote=function(r,o){var a,i,s,d,p,c,m,f;if(o.id){if(!1===o.isValid)return flashMessage("publish_draft_err"),void(e.showError=!0);if("wecard"!==r&&"rent"!==o.ltp&&!vars.isVipUser)return e.dialogAlertVip();if("wecard"===r)return o.rcmd=!o.rcmd,console.log("posting to server"),s={id:o.id,rcmd:o.rcmd},void t.post("/1.5/promote/api",{cmd:"pmt-p-rcmd",data:s}).success((function(e){if(!e.ok)return RMSrv.dialogAlert("Err:get status "+e.err)})).error((function(e){return RMSrv.dialogAlert(e.toString())}));if("Active"!==o.status){if("market"!==r||"Promoted"!==o.market.st)return flashMessage("status-not-active"),null;console.log("user can revoke")}return e.hideBackdrop=!0,e.promoteID=o.id,null==o.addr58&&(o.addr58=o.addr),a=(null!=(p=e.template)&&null!=(c=p.pd[1])?c.fldV:void 0)||vars.str_bdrms||"",i=(null!=(m=e.template)&&null!=(f=m.pd[2])?f.fldV:void 0)||vars.str_bthrms||"",d=Array.isArray(e.formData.ptp)?e.formData.ptp[3]:o.ptpT||o.ptp||"",o[58]&&o.tl?o.tl58=o.tl:o.tl58=(o.cityT||o.city||"")+" "+(d||"")+" "+(a||"")+(o.bdrms||"")+"+"+i+(o.bthrms||"")+" "+n("currency")(o.lp,"$",0),e.curProp=o,"weshare"===r?null:(e.toggleModal("promoteModal"),e.promoteTo!==r&&u(r),e.promoteTo=r,RMSrv.share("hide"),l())}},e.publish=function(){var r,o;if(e.promoteTo&&e.promoteID){if("Active"!==e.formData.status&&!e.curProp)return flashMessage("status-not-active"),null;if(e.curProp.rvkRsn)return flashMessage("revoke-not-change"),null;if((r={}).to=e.promoteTo,"market"===r.to&&("mlslisting"===(o=e.curProp.ltp)||"mlsrent"===o))return flashMessage("no-longer-supported");if(e.showError=!1,"uhouzz"===r.to&&!1===e.promoteUhouzz)return RMSrv.dialogAlert("Not allowed"),null;if(e.dataHasChanged&&(r.user=e.userData),"market"===r.to&&e.commi){if(!e.curProp.market||!e.curProp.market.cmstn)return e.showError=!0,flashMessage("cmstn-req"),null;r.cmstn=e.curProp.market.cmstn,r.rmProp=e.curProp.market.rmProp,r.sortOrder=e.curProp.market.sortOrder,r.agrmntImg=e.curProp.market.agrmntImg,r.adok=e.curProp.market.adok,r.isV=e.curProp.market.isV,e.curProp.market.nm&&(r.nm=e.curProp.market.nm),e.curProp.market.mbl&&(r.mbl=e.curProp.market.mbl)}return"58"===r.to&&(r.tl=e.curProp.tl58,r.addr58=e.curProp.addr58),r.id=e.promoteID,r.marketPromoTs=e.curProp.marketPromoTs,t.post("/1.5/promote/api",{cmd:"pmt-p-pub",data:r},{timeout:5e3}).success((function(t){var r,o,n,a;if(!t.ok)return t.unverify?(a=t.err,o=vars.str_cancel||"Cancel",n=vars.str_verify||"Verify",r=function(t){if(t+""=="2")return e.openVerifyPopup()},RMSrv.dialogConfirm(a,r,"",[o,n])):RMSrv.dialogAlert(""+t.err);toggleModal("promoteModal"),e.hideBackdrop=!0,toggleModal("editListingModal","close"),flashMessage("promoted"),"58"===t.to&&e.loadListings(vars.type,0),A(t)})).error((function(e){return RMSrv.dialogAlert(e.toString())}))}},e.revoke=function(){var r;if(e.promoteTo&&e.promoteID)return e.curProp[e.promoteTo]?((r={}).to=e.promoteTo,r.id=e.promoteID,t.post("/1.5/promote/api",{cmd:"pmt-p-revoke",data:r}).success((function(e){if(!e.ok)return console.log("Err:revoke "+e.err);toggleModal("promoteModal"),toggleModal("editListingModal","close"),flashMessage("revoked"),A(e)})).error((function(e){return RMSrv.dialogAlert(e.toString())}))):flashMessage("revoke_fail")},e.uploadAgrmntImg=function(){return insertImage({url:"/1.5/img/insert?uploadOne=1&imgSize=xl"},(function(t){var r,o;if(":cancel"!==t)try{(o=JSON.parse(t)).userFiles&&!e.userFiles&&(e.userFiles=o.userFiles),o.picUrls.length>0&&(e.curProp.market.agrmntImg=o.picUrls[0]),e.$apply()}catch(e){r=e,console.error(r)}else console.log("canceled")}))},e.deleteAgrmntImg=function(){return e.curProp.market.agrmntImg="",e.curProp.market.isV=!1},l=function(){var r;if((r={id:e.promoteID,to:e.promoteTo}).id&&r.to)return t.post("/1.5/promote/api",{cmd:"pmt-p-status",data:r}).success((function(e){if(!e.ok)return RMSrv.dialogAlert("Err:get status "+e.err);A(e)})).error((function(e){return RMSrv.dialogAlert(e.toString())}))},p=function(o){return t.post("/1.5/promote/api",{cmd:"pmt-p-tpl",data:o},{timeout:5e3}).success((function(t){var o,n,a,i,s,l,d,p,u,m;if(1===t.ok){for(e.template=t.tpl,a=function(e,t){var r,o;if(e){for(c=r=0,o=e.length-1;0<=o?r<=o:r>=o;c=0<=o?++r:--r)if(e[c][2]===t)return c;return 0}},o=t.tpl.pd[0].dbv,u=t.tpl.pd[0].v,c=s=0,p=o.length-1;0<=p?s<=p:s>=p;c=0<=p?++s:--s)n=o[c][0]+" "+o[c][1],m=u[c][0]+" "+u[c][1],o[c].push(n),o[c].push(m);return e.ptpList=t.tpl.pd[0],e.formData.ptp&&(i=a(e.ptpList.dbv,e.formData.ptp[2])),e.formData.ptp=e.ptpList.dbv[i],e.formData.exp=new Date((new Date).valueOf()+7776e6),d=document.querySelector("#picUrlsList"),l={animation:150,ghostClass:"sort-placeholder",delay:100,delayOnTouchOnly:!0,touchStartThreshold:10,onUpdate:function(t){var r,o,n;return o=t.newIndex,n=t.oldIndex,r=e.picUrls.splice(n,1),e.picUrls.splice(o,0,r[0]),e.$apply()}},e.sortable=new Sortable(d,l),r((function(){return e.$apply()}))}return RMSrv.dialogAlert("Err:getting template"+t.err)})).error((function(e){return RMSrv.dialogAlert(e.toString())}))},e.createListing=function(t){return w("tmpData"),e.toggleModal("newListingModal","close"),e.hideBackdrop=!0,"assignment"!==t&&"exlisting"!==t||vars.isRealtor?(e.toggleModal("editListingModal"),e.showError=!1,e.picUrls=[],e.inputAddrRet=void 0,e.inputZipRet=void 0,e.inputAddr=void 0,e.fullAddr=!1,e.formData={ltp:t,status:"Active"},e.prop=e.formData,e.propStatus="Active",p(t)):e.toggleModal("signupModal")},e.pickDate=function(t){return e.toggleModal("datePickerModal"),e.fldTobeUpdated=t,e.hideBackdrop=!1,e.date=e.minDate,"exp"===e.fldTobeUpdated?(e.dateMode=!0,e.rangeMode=!1,void(e.dateOnly=!0)):(e.dateMode=!1,e.rangeMode=!1,e.dateOnly=!1,e.formData[t]&&"immed"!==e.formData[t]&&"tba"!==e.formData[t]&&(e.dateMode=!0),e.formData[t+"f"]?(e.rangeMode=!0,e.range="from",e.dateFrom=e.formData[t+"f"],e.dateTo=e.formData[t+"t"]):void 0)},e.setDate=function(t){if("tba"===t||"immed"===t){if("exp"===e.fldTobeUpdated)return;return e.dateMode=!1,e.rangeMode=!1,e.hideBackdrop=!0,e.formData[e.fldTobeUpdated]=t,e.range="",toggleModal("datePickerModal"),delete e.formData[e.fldTobeUpdated+"f"],delete e.formData[e.fldTobeUpdated+"t"],e.dateFrom=void 0,e.dateTo=void 0,e.rangeMode=!1,e.propForm.$dirty=!0}return"date"===t?(e.dateMode=!0,e.rangeMode=!1):"exp"!==e.fldTobeUpdated?(e.range="from",e.dateMode=!1,e.rangeMode=!0):void 0},e.loadListings=function(o,n){var a;return e.loading=!0,e.searchId.length>0?S():(e.type=o,(a={type:o}).skip=n||0,t.post("/1.5/promote/api",{cmd:"pmt-p-lst",data:a}).success((function(t){var o,n,a,i,s,l,d,p,u,m,f;if(n=function(t){var r,o,n,a;if(t){for(a=[],r=0,o=(n=t.l).length;r<o;r++)(c=n[r]).pic?(c.pic.ml_num=c.sid||c.ml_num,a.push(c.img=proplib.convert_rm_imgs(e,c.pic,"reset")[0])):a.push(void 0);return a}},1!==t.ok)return console.log(t.err);if("market"===t.type){for(o="marketListings",f={},a=0,i=(p=t.ul).length;a<i;a++)f[(m=p[a])._id]=m;for(l=0,s=(u=t.l).length;l<s;l++)m=f[(d=u[l]).uid],d.fn=m.fn,d.ln=m.ln,d.avt=m.avt,d.cpny=m.cpny,d.mbl=m.mbl,d.tel=m.tel}else o="mylistings";n(t),0===t.skip?e[o]=t.l:e[o]?e[o]=e[o].concat(t.l):e[o]=t.l,e.allListing=e[o],e.hasMore=t.l.length>=20,e.loading=!1,document.getElementById("busy-icon").style.display="none",r((function(){return e.$apply()}))})).error((function(e){return RMSrv.dialogAlert(e.toString())})))},e.loadMore=function(){var t,r;if(e.hasMore&&!e.loading)return t="market"===e.type?"marketListings":"mylistings",r=e[t].length,e.loadListings(e.type,r)},e.getContactList=function(){return t.post("/1.5/rmlisting/contactList",{}).success((function(t){var r,o,n;if(t.ok){for(e.contactList=t.result,r=0,o=(n=e.contactList).length;r<o;r++)n[r].isShowDelete=!1;toggleModal("selectContactModal")}else flashMessage("server-error")})).error((function(e){return RMSrv.dialogAlert(e.toString())}))},e.saveContact=function(){var r;if(e.contactInfo.nm&&e.contactInfo.mbl)return r={nm:e.contactInfo.nm,mbl:e.contactInfo.mbl},e.contactInfo.id&&(r.id=e.contactInfo.id),t.post("/1.5/rmlisting/saveContact",r).success((function(t){var o;t.ok?(e.contactInfo.id?(o=e.contactList.findIndex((function(t){return t._id.toString()===e.contactInfo.id.toString()})),e.contactList[o].nm=e.contactInfo.nm,e.contactList[o].mbl=e.contactInfo.mbl):(r._id=t._id,r.isShowDelete=!1,e.contactList.unshift(r)),e.contactInfo={nm:"",mbl:"",id:null},e.isAddContact=!1,flashMessage("saved")):flashMessage("server-error")})).error((function(e){return RMSrv.dialogAlert(e.toString())}));flashMessage("conten_empty")},e.cancelSave=function(){return e.contactInfo={nm:"",mbl:"",id:null},e.isAddContact=!1},e.deleteContact=function(r,o){return t.post("/1.5/rmlisting/deleteContact",{id:r}).success((function(t){t.ok?(e.contactList.splice(o,1),flashMessage("deleted")):flashMessage("server-error")})).error((function(e){return RMSrv.dialogAlert(e.toString())}))},e.selectContact=function(t,r){return e.curProp.market.nm=t,e.curProp.market.mbl=r,toggleModal("selectContactModal","close")},e.editContact=function(t,r,o){return e.contactInfo.nm=r,e.contactInfo.mbl=o,e.contactInfo.id=t,e.isAddContact=!0},e.confirmDeletion=function(t){return e.contactList[t].isShowDelete=!0},e.cancelDelete=function(t){return e.contactList[t].isShowDelete=!1},e.deleteSel=function(){return delete e.curProp.market.nm,delete e.curProp.market.mbl},e.showAllImg=function(){if(e.hasMoreImg)return e.hasMoreImg=!1,e.userFiles.pl=e.allImgs},e.getRecentUserFiles=function(t){var r,o,n,a,i;if(o={},40,(null!=t?t.count:void 0)>40){for(r in e.hasMoreImg=!0,e.allImgs=t.pl,a=t.count-40,n=t.pl)(i=n[r]).i>a&&(o[r]=i);t.pl=o}return t},e.getUserFiles=function(r){e.userFiles&&!r||(e.picS3RmConfirm=!1,t.get("/1.5/userFiles.json",{}).success((function(t){return e.userFiles=e.getRecentUserFiles(t),e.selected=[]})).error((function(t){return e.message=t.message,RMSrv.dialogAlert("Error when getting city list!")})))},e.checkPromotResult=function(t,r){var o;"market"!==t&&(null!=(o=r[e.promoteTo])?o.url:void 0)&&RMSrv.showInBrowser(r[e.promoteTo].url)},e.selectImg=function(t,r){var o;return(o=e.selected.indexOf(r))>=0?(t.target.classList.remove("selected"),e.selected.splice(o,1)):(t.target.classList.add("selected"),e.selected.push(r))},e.toggleRemovePic=function(){return e.picRmConfirm=!e.picRmConfirm},e.removePic=function(t){var r;return"agrmntImg"===h?(e.curProp.market.agrmntImg="",e.toggleModal("imgPreviewModal","close")):(r=e.picUrls.indexOf(t))<0?void 0:(e.picUrls.splice(r,1),e.toggleModal("imgPreviewModal","close"),e.propForm.$dirty=!0)},e.removePicS3=function(){var r;if((r={}).fldr=e.userFiles.fldr,r.files=e.selected,r.files.length>0)return r.files.length>9?RMSrv.dialogAlert("You can select up to 9 images at a time!"):void t.post("/1.5/deleteFiles",r).success((function(t){return 1===t.ok?e.getUserFiles(!0):RMSrv.dialogAlert(t.err)})).error((function(t){return e.err=t.err,RMSrv.dialogAlert("sever error!, please try again later")}))},e.editMyListing=function(o){return e.inputAddrRet=void 0,toggleModal("editListingModal"),t.post("/1.5/rmprop/detail",{rmid:o},{timeout:5e3}).success((function(t){var o,n,a,i;if(t.ok){for(null!=(i=t.prop.pic)&&(i.ml_num=t.prop.sid||t.prop.ml_num),e.picUrls=proplib.convert_rm_imgs(e,t.prop.pic,"reset"),t.prop.ts&&(o=(new Date-new Date(t.prop.ts))/864e5,t.prop.dom=parseInt(o)),e.hideBackdrop=!0,e.showError=!1,p(t.prop.ltp),t.prop.ptp=[t.prop.ptp,t.prop.pstyl,t.prop.ptp+" "+t.prop.pstyl],e.propStatus=t.prop.status,e.formData=t.prop,n=0,a=v.length;n<a;n++)c=v[n],e.tmpData[c]=t.prop[c];return e.tmpData.zip=t.prop.zip,e.changeSaved=!0,e.inputZipRet=t.prop.zip?t.prop.zip.replace(/\s+/g,""):"",e.inputSearchAddr=e.inputAddrRet=s(t.prop),e.prop=t.prop,e.showError=!0,r((function(){return e.propForm.$setPristine(),e.propDataHasChanged=!1,e.$apply()}),10)}return RMSrv.dialogAlert(t.err)})).error((function(e){return RMSrv.dialogAlert(e.err)}))},e.showSMB=function(){if("Inactive"!==e.formData.status)return e.propDataHasChanged&&e.propForm.$dirty&&!e.changeSaved?(flashMessage("share_save_err"),void(e.showError=!0)):e.propForm.$valid?RMSrv.share("show"):(flashMessage("share_draft_err"),void(e.showError=!0))},e.insert=function(){var t,r,o,n,a;if(e.userFiles||e.getUserFiles(),r=function(){return toggleModal("imgSelectModal"),e.hideBackdrop=!0,e.propForm.$dirty=!0},e.picUrls||(e.picUrls=[]),e.imgInputURL)return-1===e.picUrls.indexOf(n)&&e.picUrls.push(e.imgInputURL),r();if(e.selected&&e.selected.length>0){for(t=e.userFiles,a=e.selected,n=void 0,void 0,c=0;c<=a.length-1;)n=t.base+"/"+t.pl[a[c]].nm,a[c],-1===e.picUrls.indexOf(n)&&e.picUrls.push(n),c++;return r()}return(o=document.querySelector("#imgInputFiles").files)&&o.length>0?(e.propForm.$dirty=!0,y(o)):console.log("no files")},e.previewPic=function(t,r){return h=r||"picUrls",e.toggleModal("imgPreviewModal"),e.hideBackdrop=!0,e.currentPic="",e.currentPic=t,e.picRmConfirm=!1},e.promptSaveModal=function(){return e.propDataHasChanged&&e.propForm.$dirty&&!e.changeSaved?(toggleModal("saveTipModal","open"),e.tip_save_change=!0,e.hideBackdrop=!1):(toggleModal("editListingModal","close"),e.hideBackdrop=!0),e.sortable.destroy()},e.titleFocus=function(t){return e.editingTitle=t},y=function(e){var t;return t=void 0,c=void 0,e&&"undefined"!=typeof FileReader?(c=0,(t=function(r){var o;return o=void 0,c<e.length?(o=e[c++],M(o,(function(e){return t(e)}))):(r||flashMessage("img-inserted"),toggleModal("imgSelectModal"))})()):RMSrv.dialogAlert("Unsuppored browser. Can't process files.")},d=function(r,o){var n,a;return n={},a=b(r.name,r.type),n.ext=a[1]||"jpg",r.ext=n.ext,n.w=r.width,n.h=r.height,n.s=r.size,e.loading=!0,e.disabledInsert=!0,t.post("/1.5/rmSign",n).success((function(t){return e.disabledInsert=!1,t.key?(window.rmConfig=t,o?o():void 0):t.e?RMSrv.dialogAlert(t.e):flashMessage("server-error")})).error((function(t){return e.disabledInsert=!1,e.message=t.message,flashMessage("server-error")}))},function(r,o,n){var a;return(a={}).ext="jpg",a.w=r.width,a.h=r.height,a.s=r.size,a.t=n?1:0,e.loading=!0,t.post("/1.5/s3sign",a).success((function(e){return e.key?(window.s3config=e,o?o():void 0):flashMessage("server-error")})).error((function(t){return e.message=t.message,flashMessage("server-error")}))},k=function(r,o,n){var a,i,s,l,d;return i=new FormData,e.disabledInsert=!0,l={type:"image/jpeg"},s=r,i.append("key",rmConfig.key),i.append("signature",rmConfig.signature),l.fileNames=rmConfig.fileNames.join(","),l.ext=r.ext||"jpg",i.append("date",rmConfig.date),i.append("backgroundS3",!0),i.append("contentType",rmConfig.contentType),i.append("file",s),d=rmConfig.credential,0,a=function(r){return e.disabledInsert=!1,e.message=null!=r?r.message:void 0,r.e?RMSrv.dialogAlert(r.e):flashMessage("server-error"),n(r),t.post("/1.5/uploadFail",{}).success((function(){}))},o={transformRequest:angular.identity,headers:{"Content-Type":void 0}},t.post(d,i,o).success((function(r){return e.disabledInsert=!1,r.ok?(e.loading=!1,l.t=r.hasThumb,l.w=r.width,l.h=r.height,l.s=r.size,t.post("/1.5/uploadSuccess",l,{type:"post"}),e.picUrls.push(r.sUrl),n(null)):a(r)})).error((function(t){return e.loading=!1,a(t)}))},function(r,o){var n,a,i,s;return a=function(r){return e.message=r.message,flashMessage("server-error"),t.post("/1.5/uploadFail",{}).success((function(){}))},s=o?r.blob2:r.blob,(i=new FormData).append("file",s),n={transformRequest:angular.identity,headers:{"Content-Type":void 0}},t.post("/file/uploadImg",i,n).success((function(t){if(e.loading=!1,console.log(null!=t),!o)return e.picUrls.push(t.sUrl)})).error((function(e){return a(e)}))},function(r,o){var n,a,i,s,l,d,p,u;return a=function(r){return e.message=null!=r?r.message:void 0,flashMessage("server-error"),t.post("/1.5/uploadFail",{}).success((function(){}))},s=void 0,l=void 0,d=void 0,u=void 0,o?(s=r.blob2,l=window.s3config.thumbKey,d=window.s3config.thumbPolicy,u=window.s3config.thumbSignature):(s=r.blob,l=window.s3config.key,d=window.s3config.policy,u=window.s3config.signature),(i=new FormData).append("acl","public-read"),i.append("key",l),i.append("x-amz-server-side-encryption","AES256"),i.append("x-amz-meta-uuid","14365123651274"),i.append("x-amz-meta-tag",""),i.append("Content-Type",window.s3config.contentType),i.append("policy",d),i.append("x-amz-credential",window.s3config.credential),i.append("x-amz-date",window.s3config.date),i.append("x-amz-signature",u),i.append("x-amz-algorithm","AWS4-HMAC-SHA256"),i.append("file",s,l),p=vars.s3protocol+"://"+vars.s3bucket+".s3.amazonaws.com/",n={transformRequest:angular.identity,headers:{"Content-Type":void 0}},t.post(p,i,n).success((function(t){if(e.loading=!1,console.log(null!=t),!o)return e.picUrls.push("http://"+window.s3config.s3bucket+"/"+window.s3config.key)})).error((function(e){return a(e)}))},M=function(e,t){var r;return r=void 0,/image/i.test(e.type)?((r=new FileReader).onload=function(r){var o;return(o=new Image).onload=function(){if(!(e.size>vars.maxImageSize))return d(e,(function(){return k(e,{},t)}));RMSrv.dialogAlert(vars.tooLargeStr||"Too Large")},o.src=r.target.result},r.readAsDataURL(e)):(RMSrv.dialogAlert("Unsupported file extension for: "+e.name+". Please try other files."),t())},b=function(e,t){var r;return void 0,(r=e.lastIndexOf("."))>0?[e.substr(0,r),e.substr(r+1).toLowerCase()]:[e,"."+t.substr(t.lastIndexOf("/")).toLowerCase()]},i=function(e){var t,r,o,n,a;for(void 0,r=void 0,void 0,c=void 0,n=void 0,void 0,r=e.split(",")[0].indexOf("base64")>=0?atob(e.split(",")[1]):unescape(e.split(",")[1]),a=e.split(",")[0].split(":")[1].split(";")[0],t=new ArrayBuffer(r.length),n=new Uint8Array(t),c=0;c<r.length;)n[c]=r.charCodeAt(c),c++;return o=new DataView(t),new Blob([o],{type:a})},function(e,t){var r,o,n,a,s,l,d,p,u,c,m,f,g;return 1e3,1e3,680,680,c=128,10,r=void 0,o=void 0,void 0,void 0,void 0,a=void 0,s=void 0,l=void 0,d=void 0,p=void 0,void 0,void 0,f=void 0,g=void 0,p=1,(e.width>1e3||e.height>1e3)&&(f=1e3/e.width,a=1e3/e.height,p=Math.min(f,a)),e.width>=e.height&&e.height>680&&(a=680/e.height)<p&&(p=a),e.width<=e.height&&e.width>680&&(f=680/e.width)<p&&(p=f),(r=document.createElement("canvas")).width=e.width*p,r.height=e.height*p,r.getContext("2d").drawImage(e,0,0,e.width,e.height,0,0,r.width,r.height),u=b(t.name,t.type),(s={name:t.name,nm:u[0],ext:u[1],origType:t.type,origSize:t.size,width:r.width,height:r.height,ratio:p}).type="image/jpeg",s.url=r.toDataURL(s.type,.8),s.blob=i(s.url),s.size=s.blob.size,s.canvas=r,(o=document.createElement("canvas")).width=m=Math.min(128,e.width),o.height=n=Math.min(c,e.height),e.width*n>e.height*m?(g=(e.width-e.height/n*m)/2,d=e.width-2*g,l=e.height):(g=0,d=e.width,l=e.width),o.getContext("2d").drawImage(e,g,0,d,l,0,0,m,n),s.url2=o.toDataURL(s.type,.7),s.blob2=i(s.url2),s.size2=s.blob2.size,s.canvas2=o,s},function(e){var t;if(e.files&&e.files[0])return(t=new FileReader).onload=function(e){return document.querySelector("#previewImg").src=e.target.result},t.readAsDataURL(e.files[0])},e.$watch("date",(function(t,r){null!=t&&t!==r&&t!==e.minDate&&(e.rangeMode?("from"===e.range?e.dateFrom=new Date(t):e.dateTo=new Date(t),e.dateFrom&&e.dateTo&&(e.formData[e.fldTobeUpdated]=void 0)):(e.formData[e.fldTobeUpdated+"f"]=void 0,e.formData[e.fldTobeUpdated+"f"]=void 0))})),e.$watch("userData",(function(t,r){t!==r&&(e.dataHasChanged=!angular.equals(e.userData,e.original),r.eml||e.dataHasChanged||(e.dataHasChanged=!1))}),!0),e.$watch("inputZipRet",(function(t,r){t&&(e.inputZipRet=t.toUpperCase(),m(t)?(e.tmpData.zip=t.toUpperCase(),e.fullAddr=!0,e.gotFullAddrFromSearch||(e.searchAddrRet=s(e.tmpData))):e.fullAddr=!1)})),e.$watch("formData",(function(t,r){var o,a,i,s,l,d;if(t&&e.formData&&!angular.equals(t,r)&&(e.propDataHasChanged=!0,s=t||{},e.changeSaved=!1,!e.editingTitle&&!(null!=(l=e.formData)?l.tllck:void 0))){for(d=vars.RealMaster+" "+(vars.ltpMap[e.formData.ltp]||"")+" ",a=0,i=(o="N"===e.formData.daddr?["lp","city","prov","zip"]:["lp","addr","unt","city","prov"]).length;a<i;a++)"lp"===(c=o[a])&&s[c]?d+=n("currency")(s.lp,"$",0):d+=s[c]||"",d+=" ";e.formData.tl=d}}),!0),e.mylistings||e.loadListings(vars.type,0),vars.id&&e.editMyListing(vars.id),e.ml_num&&"mylisting"===e.type&&t.post("/1.5/props/detail",{nt:1,slp:1,_id:"TRB"+e.ml_num},{timeout:5e3}).success((function(o){var a,i,l,d,u,m,f,g;if(o.ok){for(d=o.prop||o.detail,e.picUrls=getTrebPicUrls(d.pho,d.sid),d.ptp=[d.ptp,d.pstyl,d.ptp+" "+d.pstyl],d.ts||""===d.ts||(d.ts=new Date),(m=d.psn)&&(/imme?d/i.test(m)?d.psn="immed":/tba|tbd/i.test(m)?d.psn="tba":d.psn=m),(l=d.pets)?"Restrict"===l?d.pets=1:"N"===l&&(d.pets=0):delete d.pets,a=0,i=(f=["tbdrms","bthrms","kch","gr","lvl","mintr","tax","mfee","lp"]).length;a<i;a++)d[c=f[a]]=parseInt(d[c])||"";d.rid=d.id,delete d.id,d.m_zh&&(d.m=d.m_zh+d.m),e.formData=d,u=(null!=(g=d.stp)?g.toLowerCase():void 0)||"lease",e.formData.ltp="rent"===u||"lease"===u||"sub-lease"===u?"mlsrent":"mlslisting",p(e.formData.ltp),e.formData.status="Active",e.formData.gr=parseInt(d.gr)||0,e.inputSearchAddr=e.inputAddrRet=s(d),e.inputZipRet=d.zip?d.zip.replace(" ",""):"",e.propStatus="Active",e.hideBackdrop=!0,e.showError=!1,e.fullAddr=!0,e.toggleModal("editListingModal","open"),r((function(){return e.$apply()}),10),vars.to&&t.post("/1.5/rmprop/detail",{rmid:e.ml_num,ml:1},{timeout:5e3}).success((function(t){var r,o,a,i;if(t.ok)e.promote(vars.to,t.prop);else{for(i=vars.RealMaster+"- ",o=0,r=(a=["lp","addr","unt","city","prov"]).length;o<r;o++)"lp"===(c=a[o])&&e.formData[c]?i+=n("currency")(e.formData[c],"$",0):i+=e.formData[c]||"",i+=" ";e.formData.tl=i,e.formData.exp=new Date((new Date).valueOf()+7776e6),e.submitForm(!0),vars.promoteAfterSave=1}return toggleModal("editListingModal","close"),e.hideBackdrop=!0})).error((function(){return flashMessage("server-error")}))}else e.message=o.message,"realtor"===o.errorType?(e.prop=o.prop,e.toggleModal("needRealtor","open")):e.toggleModal("needLogin","open")})).error((function(e){RMSrv.dialogAlert("post to detail: "+e.toString())})),null},(app=angular.module("listingApp",["datePicker"])).controller("listingCtrl",["$scope","$http","$timeout","$interval","$filter",listingCtrl]),app.directive("scroll",[function(){return function(e,t,r){var o;o=document.querySelector("#realtorUtilPage > div.content"),angular.element(o).bind("scroll",(function(){var t,r;return t=e.height(document.querySelector("#realtorUtilPage > div.content"))-94,r=e.height(document.querySelector("#realtorUtilPage > div.content .content-list")),e.nextLoadPosition=r-t-10,this.scrollTop>e.nextLoadPosition&&e.loadMore(),e.$apply()}))}}]),"undefined"!=typeof RMSrv&&null!==RMSrv&&RMSrv.clearCache(),autocomplete="",initAutocomplete=function(){(autocomplete=new google.maps.places.Autocomplete(document.getElementById("inputAddr"),{types:["geocode"],components:[{country:"ca"}],componentRestrictions:{country:"ca"},language:"en"})).addListener("place_changed",fillInAddress)},fillInAddress=function(){var e,t,r,o,n,a,i;(e=autocomplete.getPlace())&&(t={lat:(null!=(r=e.geometry)&&null!=(o=r.location)?o.lat():void 0)||0,lng:(null!=(n=e.geometry)&&null!=(a=n.location)?a.lng():void 0)||0},(i=angular.element(document.getElementById("realtorUtilPage")).scope()).inputZipRet="",i.fullAddr=!1,i.gotFullAddrFromSearch=!1,setMarker(t),i._processRetAddr(e),i.$apply())},setMarker=function(e){var t,r;return new google.maps.LatLng(e[0],e[1]),null!=(t=window.map)&&t.setCenter(e),null!=(r=window.marker)?r.setPosition(e):void 0};
