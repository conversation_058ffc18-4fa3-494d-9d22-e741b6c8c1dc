var propElementScrollX={props:{prop:{type:Object,default:{}},rcmdHeight:{type:Number},formPage:{type:String,default:""},favMap:{type:Object,default:{}},dispVar:{default:function(){return{lang:"zh-cn"}}}},mounted(){if(!(bus=window.bus))return console.error("global bus required!");this.calcMktWraper()},methods:{calcMktWraper(){if(document.getElementsByClassName("mktrcmd").length){var s=2*parseInt((window.innerWidth-30)/2.05),e=s*document.querySelectorAll(".mkt").length;document.getElementsByClassName("mktrcmd")[0].style.width=e+"px";var r=document.getElementsByClassName("mkt");for(i=0;i<r.length;i++)r[i].style.width=s+"px"}},getStatus:function(s){return"U"==s.status?"sold":"sale"},getTranslate:function(s){return TRANSLATES[s]||s},openDetail:function(s){var e="/1.5/prop/detail/inapp?id="+(/^RM/.test(s.id)?s.id:s._id);this.formPage&&(e+=`&formPage=${this.formPage}`),openPopup(e,this.$_("RealMaster"))},getPropDate:s=>"U"==s.status_en?s.sldd:s.mt.substr(0,10),soldOrLeased:function(s){return"Sold"==s.saleTpTag_en||["Sld","Lsd","Pnd","Cld"].includes(s.lst)},saletpIsSale:function(s){return!s.saletp_en||!!/sale/.test(s.saletp_en.toString().toLowerCase())},isTop:function(s){return"A"==s.status&&new Date(s.topTs)>=new Date},propSid:function(s){return s.isProj?"":s.sid?s.sid:/^RM/.test(s.id)?s.id:s._id?s._id.substr(3):""},rmgrStr:function(s){return s.rmgr||s.tgr||s.gr},rmbdrmStr:function(s){if(s.rmbdrm)return s.rmbdrm;let e=s.bdrms||s.tbdrms;return e+=s.br_plus?"+ "+s.br_plus:"",e},rmbthrmStr:function(s){return s.rmbthrm||s.tbthrms||s.bthrms},formatPrice:s=>"number"==typeof s?"$"+s.toString().replace(/\B(?=(\d{3})+(?!\d))/g,","):s,isInGrp(s){return(this.prop.favGrp||[]).indexOf(parseInt(s))>-1},toggleFav(s){window.bus.$emit("toggleFav",s)},computedVideoUrl:function(){return this.dispVar.isCip?this.prop.vurlcn:this.prop.ytvid?"https://www.youtube.com/watch?v="+this.prop.ytvid:null},isPropUnavailable:function(){return(!this.prop.id||"RM"!=this.prop.id.substr(0,2))&&"A"!==this.prop.status_en},selectGroup:function(s){window.bus.$emit("selectGroup",s)},dotdate(s,e,r="."){if(!s)return"";"number"==typeof s&&(s=(s+="").slice(0,4)+"/"+s.slice(4,6)+"/"+s.slice(6,8)),"string"!=typeof s||/\d+Z/.test(s)||(s+=" EST");var t=e?"年":r,p=e?"月":r,n=e?"日":"";if(/^2\d{3}-\d{1,2}-\d{1,2}/.test(s)&&!/\d+Z/.test(s)){var a=s.split(" ")[0].split("-");return a[0]+t+a[1]+p+a[2]+n}var i=new Date(s);return!i||isNaN(i.getTime())?s:i.getFullYear()+t+(i.getMonth()+1)+p+i.getDate()+n}},watch:{},template:'\n  <div @click="openDetail(prop)" class="mkt rmProp rcmdProp">\n  <div class="img">\n    <img class="lazy" :src=\'prop.thumbUrl || "/img/noPic.png"\' :style="{ \'height\':rcmdHeight * 0.872+\'px\'}" referrerpolicy="same-origin"/>\n    <div class="tag" v-show=\'prop.type\'>\n      <span class="type">{{prop.type}}</span>\n    </div>\n    <span class="pull-right fav fa" :class="{\'fa-heart-o\':!prop.fav, \'fa-heart\':prop.fav}"\n      @click.stop.prevent="toggleFav(prop)"></span>\n    <div class="showOnImg prop">\n      <div class=\'price\' style=\'overflow: auto;\'>\n        <span class="val" v-if=\'prop.priceValStrRed\'>{{prop.priceValStrRed}}</span>\n        <span class="val" v-else-if=\'prop.askingPriceStr\'>{{prop.askingPriceStr}}</span>\n        <span class="desc" v-if="prop.priceValStrRedDesc" :class="{\'through\':soldOrLeased}">{{soldOrLeased?prop.askingPriceStr:prop.priceValStrRedDesc}}</span>\n        <span class="desc" v-if="prop.lstStr && (prop.tagColor != \'red\')">({{prop.lstStr}})</span>\n        <span class="stp" v-if="prop.saleTpTag && prop.saleTpTag_en !=\'Delisted\'" :class="prop.tagColor" style="margin-left:10px;">\n          <span>{{prop.saleTpTag}}</span>\n          <span v-if="(prop.tagColor == \'red\'|| prop.tagColor == \'green\') && (prop.spcts||prop.mt||prop.ts) ">&nbsp;{{dotdate(prop.spcts||prop.mt||prop.ts)}}</span>\n        </span>\n      </div>\n      <div class="bdrms">\n        <span v-show="prop.rmbdrm != null || prop.bdrms != null || prop.tbdrms != null"><span class="fa fa-rmbed"></span> {{rmbdrmStr(prop)}}</span>\n        <span v-show="prop.rmbthrm != null || prop.tbthrms != null || prop.bthrms != null"><span class="fa fa-rmbath"></span> {{rmbthrmStr(prop)}}</span>\n        <span v-show="prop.rmgr != null || prop.gr != null || prop.tgr != null"><span class="fa fa-rmcar"></span> {{rmgrStr(prop)}}</span>\n      </div>\n    </div>\n  </div>\n  <div class="detail">\n    <div class="prov" v-show=\'prop.addr\'>\n      <span class="fa fa-map-marker"></span> {{prop.city}}, {{prop.prov}}</div>\n    <div v-if=\'favMap[0]\'>\n      <span v-for=\'(g,i) in prop.favGrp\' v-show=\'favMap[g]\'>\n        <span v-show=\'i>0\'> &#183; </span>\n        <span class="exlinks" @click.stop=\'selectGroup(g)\'>{{favMap[g]?favMap[g].v:\'\'}}</span>\n      </span>\n    </div>\n  </div>\n</div>\n  '},checkNotification={props:{className:{type:String,default:""}},data:()=>({isOpenNotify:!0,hasGoogleService:!0,canRefresh:!1,refresh:!1,dispVar:{isEmailVerified:!0}}),mounted(){if(window.bus){var s=this;window.bus.$on("pagedata-retrieved",(function(e){if(s.dispVar=Object.assign(s.dispVar,e),e.coreVer){var r=s.$parent.isNewerVer(e.coreVer,"6.3.1");RMSrv.hasGoogleService&&RMSrv.hasGoogleService((function(e){s.hasGoogleService=e,1==e&&(s.checkNotify(),r&&RMSrv.onAppStateChange?RMSrv.onAppStateChange((e=>s.checkNotify())):s.canRefresh=!0)}))}}))}else console.error("global bus is required!")},computed:{showRefresh:function(){return this.canRefresh&&this.refresh},computedHasIssueReceivePN(){return!this.dispVar.isEmailVerified||!this.hasGoogleService||!this.isOpenNotify}},methods:{isAuthedRet:(s="")=>["authorized","granted"].indexOf(s)>-1,checkNotify(){var s=this;this.refresh=!1,RMSrv.onReady((()=>{RMSrv.permissions("notification","check",-1,(function(e){s.isOpenNotify=s.isAuthedRet(e)}))}))},openSetting(){this.refresh=!0,RMSrv.openSettings&&RMSrv.openSettings()},openVerifyPopup(){var s=this;RMSrv.getPageContentIframe("/1.5/settings/verify?verify=v","#callBackString",{transparent:!0},(function(e){if(":cancel"!=e)try{JSON.parse(e).emlV&&(s.dispVar.isEmailVerified=!0)}catch(s){console.error(s)}}))}},template:'\n<div class="check-notification" v-if=\'computedHasIssueReceivePN\' :class=\'className\'>\n  <span class="status fa fa-exclamation-circle" style="color: rgb(255, 205, 0); display: inline;"></span>\n  <span class="field">\n    <span class="field-name" v-if=\'!dispVar.isEmailVerified\'> {{$_(\'Email is not verified.\')}}\n      <span class="explain" @click="openVerifyPopup()">{{$_(\'Verify\')}}</span>\n    </span>\n    <span class="field-name" v-else-if=\'!hasGoogleService\'> {{$_(\'Install Google Play to get listing updates.\')}}\n    </span>\n    <span class="field-name" v-else> {{$_(\'Turn on notifications to get listing updates.\')}}\n      <span class="explain" @click="checkNotify()" v-if=\'showRefresh\'>{{$_("Refresh")}}</span>\n      <span class="explain" @click="openSetting()" v-else>{{$_(\'Notification Settings\')}}</span>\n    </span>\n  </span>\n</div>\n  '},propItem={props:{prop:{type:Object},rcmdHeight:{type:Number},index:{type:Number},formPage:{type:String,default:""},showFav:{type:Boolean,default:!0},create:{type:Boolean,default:!1},dispVar:{default:function(){return{lang:"zh-cn",isNoteAdmin:!1,isRealGroup:!1}}}},data:()=>({noteMemo:""}),mounted(){if(!(bus=window.bus))return console.error("global bus required!")},computed:{computedSaletp:function(){let s=this.prop.saletp;return s?Array.isArray(s)?s.join(" "):s:this.prop.lpunt},computedBgImg:function(){return this.prop.thumbUrl||"/img/noPic.png"},computedShowInfo(){return"similar"==this.formPage&&(!!this.prop.dist||(!!(this.prop.sqft||this.prop.sqft1&&this.prop.sqft2||this.prop.rmSqft)||(!!this.prop.front_ft||!(!this.prop.weight||!this.dispVar.isAdmin))))}},watch:{prop:{handler(s){this.prop.noteMemo&&("noTag"==this.prop.noteMemo[0].tag?this.noteMemo=this.prop.noteMemo[0].m:this.noteMemo=`[${this.prop.noteMemo[0].tag}]${this.prop.noteMemo[0].m}`)},immediate:!0,deep:!0}},methods:{showAgentWesite(s){var e=`/1.5/wesite/${s._id}?inFrame=1`;RMSrv.openTBrowser(e)},getStatus:function(s){return"U"==s.status?"sold":"sale"},openDetail:function(s){var e="/1.5/prop/detail/inapp?id="+(/^RM/.test(s.id)?s.id:s._id);this.formPage&&(e+=`&formPage=${this.formPage}`),openPopup(e,this.$_("RealMaster"))},openShowing:s=>window.bus.$emit("open-showing",s),getPropDate:s=>"U"==s.status_en?s.sldd:s.mt.substr(0,10),soldOrLeased:function(s){return"Sold"==s.saleTpTag_en||["Sld","Lsd","Pnd","Cld"].includes(s.lst)},saletpIsSale:function(s){return!s.saletp_en||!!/sale/.test(s.saletp_en.toString().toLowerCase())},isTop:function(s){return"A"==s.status&&new Date(s.topTs)>=new Date},propSid:function(s){return s.isProj?"":s.sid?s.sid:/^RM/.test(s.id)?s.id:s._id?s._id.substr(3):""},rmgrStr:function(s){return s.rmgr||s.tgr||s.gr},rmbdrmStr:function(s){if(s.rmbdrm)return s.rmbdrm;let e=s.bdrms||s.tbdrms;return e+=s.br_plus?"+ "+s.br_plus:"",e},rmbthrmStr:function(s){return s.rmbthrm||s.tbthrms||s.bthrms},formatPrice:s=>"number"==typeof s?"$"+s.toString().replace(/\B(?=(\d{3})+(?!\d))/g,","):s,isInGrp(s){return(this.prop.favGrp||[]).indexOf(parseInt(s))>-1},toggleFav(s){window.bus.$emit("toggleFav",s)},computedVideoUrl:function(){return this.dispVar.isCip?this.prop.vurlcn:this.prop.ytvid?"https://www.youtube.com/watch?v="+this.prop.ytvid:null},isPropUnavailable:function(){return(!this.prop.id||"RM"!=this.prop.id.substr(0,2))&&"A"!==this.prop.status_en},calcShowingName:s=>s.showing?s.showing.dt:"",remove(){window.bus.$emit("remove",this.prop)},clearNoteInfo(){delete this.prop.noteMemo,delete this.prop.noteId,this.noteMemo="",this.prop.noteCount--},goToEditNote(){if(!this.prop.uaddr||"undefined"==this.prop.uaddr)return bus.$emit("flash-message",this.$_(NOTE_TIPS.NOUADDR));if(!this.prop._id||"undefined"==this.prop._id)return bus.$emit("flash-message",this.$_(NOTE_TIPS.NOPROPID));let s=this;fetchData("/1.5/notes/findListingByID",{body:{id:this.prop._id}},((e,r)=>{if(!r.ok)return bus.$emit("flash-message",this.$_(NOTE_TIPS.NOPROPID));let t=r.result.uaddr;if(!t||"undefined"==t)return bus.$emit("flash-message",this.$_(NOTE_TIPS.NOUADDR));let p=`/1.5/notes/editNotes?propId=${this.prop._id}&uaddr=${encodeURIComponent(t)}`;this.prop.noteId&&(p+=`&id=${this.prop.noteId}`),s.dispVar.isRealGroup||s.dispVar.isNoteAdmin||!r.result.unt||(p+=`&unt=${encodeURIComponent(r.result.unt)}`);let n={toolbar:!1};s.dispVar&&s.dispVar.isApp&&RMSrv.getPageContent(p,"#callBackString",n,(function(e){if(":cancel"!=e)try{let r=JSON.parse(e);if("delete"==r.type)return void s.clearNoteInfo();let t=r.note;s.prop.noteMemo||(s.prop.noteCount+=1),t.memo?s.prop.noteMemo=t.memo:s.prop.noteMemo=[{tag:"noTag",m:" "}],s.prop.noteId=t._id}catch(s){console.error(s)}}))}))},goToNoteList(){let s=this;if(!this.prop.uaddr)return bus.$emit("flash-message",this.$_(NOTE_TIPS.NOUADDR));if(!this.prop._id)return bus.$emit("flash-message",this.$_(NOTE_TIPS.NOPROPID));let e=`/1.5/notes?propId=${this.prop._id}&uaddr=${encodeURIComponent(this.prop.uaddr)}&addr=${encodeURIComponent(this.prop.addr)}&lat=${this.prop.lat}&lng=${this.prop.lng}&isBuilding=${this.prop.isBuilding}`,r={toolbar:!1};s.dispVar&&s.dispVar.isApp&&RMSrv.getPageContent(e,"#callBackString",r,(function(e){if(":cancel"!=e)try{let r=JSON.parse(e).noteDetail;r&&s.prop.noteMemo?s.prop.noteMemo=r.memo?r.memo:[{tag:"noTag",m:" "}]:r&&!s.prop.noteMemo?(s.prop.noteMemo=r.memo?r.memo:[{tag:"noTag",m:" "}],s.prop.noteId=r._id,s.prop.noteCount+=1):!r&&s.prop.noteMemo&&s.clearNoteInfo()}catch(s){console.error(s)}}))},parseSqft:s=>/\-/.test(s)?s:("number"==typeof s&&(s=""+s),/\./.test(s)?s.split(".")[0]:s),dotdate(s,e,r="."){if(!s)return"";"number"==typeof s&&(s=(s+="").slice(0,4)+"/"+s.slice(4,6)+"/"+s.slice(6,8)),"string"!=typeof s||/\d+Z/.test(s)||(s+=" EST");var t=e?"年":r,p=e?"月":r,n=e?"日":"";if(/^2\d{3}-\d{1,2}-\d{1,2}/.test(s)&&!/\d+Z/.test(s)){var a=s.split(" ")[0].split("-");return a[0]+t+a[1]+p+a[2]+n}var i=new Date(s);return!i||isNaN(i.getTime())?s:i.getFullYear()+t+(i.getMonth()+1)+p+i.getDate()+n}},template:'\n<div class="prop" @click="openDetail(prop)">\n  <div class="img" :style="{ \'height\':rcmdHeight+\'px\'}">\n    <img :src="computedBgImg"\n      style="background-image: url(\'/img/noPic.png\');background-size: 100% 100%;width:100%;"\n      :style="{\'height\':rcmdHeight+\'px\'}"\n      @error="e => { e.target.src = \'/img/noPic.png\'}"\n      referrerpolicy="same-origin">\n    <div class="on-img-top">\n      <span class="top pull-left" v-if="prop.isTop && prop.marketRmProp">{{$_(\'TOP\')}}</span>\n      <span class="tp" v-show="prop.type">{{prop.type}}</span>\n      <span class="pull-right fav fa" :class="{\'fa-heart-o\':!prop.fav, \'fa-heart\':prop.fav}"\n        @click.stop.prevent="toggleFav(prop)" v-show="!prop.login && !prop.isProj" v-if=\'showFav\'></span>\n    </div>\n  </div>\n  <div class="price" :class="{\'blur\':prop.login}">\n    <span class="val" v-if=\'prop.priceValStrRed\'>{{prop.priceValStrRed}}</span>\n    <span class="val" v-else-if=\'prop.askingPriceStr\'>{{prop.askingPriceStr}}</span>\n    <span class="desc" v-if="prop.priceValStrRedDesc" :class="{\'through\':soldOrLeased}">{{soldOrLeased?prop.askingPriceStr:prop.priceValStrRedDesc}}</span>\n    <span class="desc" v-if="prop.lstStr && (prop.tagColor != \'red\')">({{prop.lstStr}})</span>\n    <div class="displayFlex maxWidth">\n      <span class="stp" v-if="prop.saleTpTag && prop.saleTpTag_en !=\'Delisted\'" :class="prop.tagColor">\n        <span>{{prop.saleTpTag}}</span>\n        <span v-if="(prop.tagColor == \'red\'|| prop.tagColor == \'green\') && (prop.spcts||prop.mt||prop.ts) && !prop.marketRmProp">&nbsp;{{dotdate(prop.spcts||prop.mt||prop.ts)}}</span>\n      </span>\n      <span class="stp green" v-show="[\'exlisting\',\'assignment\',\'rent\'].indexOf(prop.ltp) > -1 && !prop.marketRmProp">\n        <span v-show="prop.ltp == \'exlisting\'">{{$_(\'Exclusive\')}}</span>\n        <span v-show="prop.ltp == \'assignment\'">{{$_(\'Assignment\')}}</span>\n        <span v-show="prop.ltp == \'rent\' && !prop.cmstn">{{$_(\'Landlord Rental\')}}</span>\n        <span v-show="prop.ltp == \'rent\' && prop.cmstn">{{$_(\'Exclusive Rental\')}}</span>\n      </span>\n      <span class="dist" v-show="prop.dist && (formPage != \'similar\')" >{{prop.dist}}</span>\n      <span class="stp" v-show="prop.isAgreementVerified && !prop.marketRmProp">\n        <span class="fa fa-check"></span>\n      </span>\n      <span class="stp vid" v-show="computedVideoUrl()">\n        <span class="fa fa-youtube-play"></span>{{$_(\'Video\')}}\n      </span>\n      <span class="stp oh" v-if="prop.hasOh">{{$_(\'Open House\')}}</span>\n      <div class="stp viewTrusted" v-if="prop.marketRmProp"><span class="fa fa-check-circle trustedCir"></span>{{$_(prop.marketRmProp)}}</div>\n    </div>\n  </div>\n  <div class="addr one-line" v-if="!prop.login"><span v-show="prop.daddr !== \'N\'"><span v-show="prop.addr">{{prop.unt}}\n        {{prop.addr}},</span> {{prop.city}}, {{prop.prov}}</span><span v-show="prop.daddr == \'N\'"><span\n        v-show="prop.cmty">{{prop.cmty}},</span>{{prop.city}}, {{prop.prov}}</span>\n  </div>\n  <div class="addr one-line" v-if="prop.login"><span v-show="prop.daddr == \'Y\'">{{prop.addr}}, {{prop.city}}</span><span\n      v-show="prop.daddr !== \'Y\'">{{prop.city}}, {{prop.prov}}</span></div>\n  <div class="bdrms one-line" v-if="!prop.login">\n    <span class="rmbed" v-show="prop.rmbdrm != null || prop.bdrms != null || prop.tbdrms != null">\n      <span class="fa fa-rmbed">\n      </span>\n      <b class="num">{{rmbdrmStr(prop)}}</b>\n      <span v-if="prop.bdrms_diff!= null" class=\'diff\'>\n        <span :class="{red: prop.bdrms_diff < 0, green:prop.bdrms_diff > 0,blue:prop.bdrms_diff == 0}">\n          <span class="nopadding" :class="{\'fa-caret-up\': prop.bdrms_diff > 0, \'fa-caret-down\':prop.bdrms_diff < 0,\'fa-check-circle\':prop.bdrms_diff == 0 }"></span>\n          <span v-show=\'prop.bdrms_diff != 0\'>&nbsp;{{Math.abs(prop.bdrms_diff)}}</span>\n        </span>\n      </span>\n    </span>\n    <span v-show="prop.rmbthrm != null || prop.tbthrms != null || prop.bthrms != null">\n      <span class="fa fa-rmbath">\n      </span>\n      <b class="num">{{rmbthrmStr(prop)}}</b>\n      <span v-if="prop.bthrms_diff!= null" class=\'diff\'>\n        <span :class="{red: prop.bthrms_diff < 0, green:prop.bthrms_diff > 0,blue:prop.bthrms_diff == 0}">\n          <span class="nopadding" :class="{\'fa-caret-up\': prop.bthrms_diff > 0, \'fa-caret-down\':prop.bthrms_diff < 0,\'fa-check-circle\':prop.bthrms_diff == 0}"></span>\n          <span v-show=\'prop.bthrms_diff != 0\'>&nbsp;{{Math.abs(prop.bthrms_diff)}}</span>\n        </span>\n      </span>\n    </span>\n    <span v-show="prop.rmgr != null || prop.gr != null || prop.tgr != null">\n      <span class="fa fa-rmcar"></span>\n      <b class="num">{{rmgrStr(prop)}}</b>\n      <span v-if="prop.gr_diff!= null" class=\'diff\'>\n        <span :class="{red: prop.gr_diff < 0, green:prop.gr_diff > 0,blue:prop.gr_diff == 0}">\n          <span class="nopadding" :class="{\'fa-caret-up\': prop.gr_diff > 0, \'fa-caret-down\':prop.gr_diff < 0,\'fa-check-circle\':prop.gr_diff == 0}"></span>\n          <span v-show=\'prop.gr_diff != 0\'>&nbsp;{{Math.abs(prop.gr_diff)}}</span>\n        </span>\n      </span>\n    </span>\n    <span v-if="prop.marketRmProp && prop.sqft && (formPage == \'truthAssignList\')">\n      <span class="fa fa-area"></span>\n      <b class="num">{{prop.sqft}}</b>\n    </span>\n    <span v-show="prop.isAgreementVerified && !prop.marketRmProp">\n      <span class="fa fa-check-circle"></span>\n      <b class="num">{{$_(\'Verified\')}}</b>\n    </span>\n    <span v-if="(prop.private && (formPage == \'truthAssignList\'))">\n      <span class="num" style="color:#e03131">{{$_(\'Hide to the public\')}}</span>\n    </span>\n    <span class="sid">{{propSid(prop)}}</span>\n  </div>\n  <div class="bdrms" v-if="prop.login">{{$_(\'Please login to see this listing\')}}</div>\n  <div class=\'showing-info\' v-if="formPage == \'buylist\'">\n    <span class="left">\n      <span v-show=\'dispVar.isRealtor && create\'>\n        <span v-if=\'prop.clnt && prop.clnt.nm\'>{{prop.clnt.nm}}</span>\n      </span>\n      <span v-if=\'!create && prop.realtor && prop.realtor.fnm\' class=\'agent\' @click.stop=\'showAgentWesite(prop.realtor)\'>\n        <img :src="prop.realtor.avt || prop.realtor.wxavt || \'/img/logo.png\'" @error="prop.realtor.avt = \'/img/logo.png\'" referrerpolicy="same-origin">\n        <span class="d">{{prop.realtor.fnm}}</span>\n      </span>\n      <span class="showing-name" @click.stop=\'openShowing(prop)\' :class=\'{"pull-right":!create}\'>\n        <span class="link">{{$_(\'Showing\')}}</span>\n        <span>({{calcShowingName(prop)}})</span>\n      </span>\n    </span>\n    <span class="right" v-show=\'create\'>\n      <span class="pull-right delbtns btn btn-nooutline" v-show="prop.del">\n        <span class="cancle pull-right btn btn-nooutline" @click.stop.prevent="prop.del = false" >{{ $_(\'Cancel\')}}</span>\n        <span class="delete pull-right btn btn-negative" @click.stop.prevent="remove()" >{{ $_(\'Delete\')}}</span>\n      </span>\n      <span class="pull-right sprite16-18 sprite16-4-8" v-show="!prop.del" @click.stop.prevent="prop.del = true"></span>\n    </span>\n  </div>\n  <div v-if=\'computedShowInfo\' class=\'size bdrms\'>\n    <span v-if="prop.dist">{{$_(\'Distance\')}}\n      <span class=\'val pull-right\' >{{prop.dist}}{{$_(\'m\')}}</span>\n    </span>\n    <div v-if="prop.sqft || (prop.sqft1&&prop.sqft2) || prop.rmSqft">\n      {{$_(\'Size\')}}\n      <span class=\'val pull-right\'>\n        <span v-if="prop.sqft">{{parseSqft(prop.sqft)}}</span>\n        <span v-else-if="prop.sqft1&&prop.sqft2">{{prop.sqft1}}-{{prop.sqft2}}</span>\n        <span v-if="prop.rmSqft && dispVar.isLoggedIn">\n          <span v-if="prop.sqft && /-/.test(prop.sqft) && (prop.sqft!=prop.rmSqft)">&nbsp;({{parseSqft(prop.rmSqft)}})</span>\n          <span v-if=\'!prop.sqft\'>{{parseSqft(prop.rmSqft)}} ({{$_(\'Estimated\')}})</span>\n        </span>\n        <span>&nbsp;{{$_(\'ft&sup2;\')}}</span>\n        <span v-if="prop.sqft_diff!= null" class=\'diff\'>\n          <span :class="{red: prop.sqft_diff < 0, green:prop.sqft_diff > 0,blue:prop.sqft_diff == 0}">\n            <span class="nopadding" :class="{\'fa-caret-up\': prop.sqft_diff > 0, \'fa-caret-down\':prop.sqft_diff < 0,\'fa-check-circle\':prop.sqft_diff == 0}"></span>\n            <span v-if=\'prop.sqft_diff_abs && prop.sqft_diff_abs != 0\'>&nbsp;{{prop.sqft_diff_abs}} {{$_(\'ft&sup2;\')}}</span>\n          </span>\n        </span>\n      </span>\n    </div>\n    <div v-if="prop.front_ft">\n      {{$_(\'Lot\')}}\n      <span class=\'val pull-right\'>\n        {{prop.front_ft}}\n        <span v-if="prop.front_ft_diff!= null">(\n          <span :class="{red: prop.front_ft_diff < 0, green:prop.front_ft_diff > 0,blue:prop.front_ft_diff == 0}">\n            <span class="nopadding" :class="{\'fa-caret-up\': prop.front_ft_diff > 0, \'fa-caret-down\':prop.front_ft_diff < 0,\'fa-check-circle\':prop.front_ft_diff == 0}"></span>\n            <span v-if=\'prop.front_ft_diff != 0\'>&nbsp;{{Math.abs(prop.front_ft_diff)}}</span>\n          </span>)\n        </span>\n        &nbsp;* {{prop.depth}}\n        <span v-if="prop.depth_diff!= null">(\n          <span :class="{red: prop.depth_diff < 0, green:prop.depth_diff > 0,blue:prop.depth_diff == 0}">\n            <span class="nopadding" :class="{\'fa-caret-up\': prop.depth_diff > 0, \'fa-caret-down\':prop.depth_diff < 0,\'fa-check-circle\':prop.depth_diff == 0}"></span>\n            <span v-if=\'prop.depth_diff != 0\'>&nbsp;{{Math.abs(prop.depth_diff)}}</span>\n          </span>)\n        </span>\n        {{prop.lotsz_code}} {{prop.irreg}}\n      </span>\n    </div>\n    <div v-if="prop.weight && dispVar.isDevGroup">\n      {{$_(\'Weight\')}}\n      <span class=\'val pull-right\' v-if=\'prop.weight.wTotal\'>\n        {{prop.weight.wTotal}}\n      </span>\n    </div>\n  </div>\n  <div class="bdrms" v-if="prop.login">{{$_(\'Please login to see this listing\')}}</div>\n  <div class="noteInsave" v-if="prop.noteMemo || prop.noteCount>=0">\n    <div class="noteInfo" :style="(dispVar.isNoteAdmin || dispVar.isRealGroup)? \'justify-content:normal\':\'justify-content: space-between\'">\n      <input v-model="noteMemo" class="noteMemo" readonly="readonly" :placeholder="$_(\'Add a note\')" @click.stop="goToEditNote()"></input>\n      <span v-if="dispVar.isNoteAdmin || dispVar.isRealGroup" class="fa fa-rmmemo noteIcon" @click.stop="goToNoteList()"></span>\n      <span v-else class="fa fa-rmmemo noteIcon" :style="prop.noteCount? \'color: #428bca\':\'color:#777\'" @click.stop="goToEditNote()"></span>\n      <span v-if="dispVar.isNoteAdmin || dispVar.isRealGroup" @click.stop="goToNoteList()">{{prop.noteMemo?1:0}}/{{prop.noteCount}}</span>\n    </div>\n  </div>\n</div>\n  '},propFavActions={props:{dispVar:{type:Object,default:function(){return{lang:"zh-cn",allowedEditGrpName:!1,isLoggedIn:!1}}}},components:{},data:()=>({grp:null,grpName:"",cntr:1,MAX_GROUPS:5,grps:[],isArchived:!1,grpsSelect:!1,grpsEdit:!1,prop:{fav:!1,favGrp:[]},inputGrpName:"",grpsOptions:!1,mode:"new",showCrm:!1,clnt:null,strings:{clearTip:{key:"Are you sure you want to clear this folder?",ctx:""},deleteTip:{key:"Are you sure you want to delete this folder?",ctx:""},cancel:{key:"Cancel",ctx:""},delete:{key:"Delete",ctx:""},clear:{key:"Clear",ctx:""}},folderSort:"time"}),mounted(){if(!(e=window.bus))return console.error("global bus required!");var s=this,e=window.bus;e.$on("toggleFav",(function(e){s.dispVar.isLoggedIn?(s.grpsSelect=!0,s.prop=e,s.grpsEdit=!1,s.grps.length||s.getGroups({mode:"get"}),s.toggleGrpSelect()):location.href="/1.5/user/login"})),e.$on("editGroups",(function({grp:e,grpInfo:r}){s.grp=e,s.grpName=r.v,r.clnt&&r.clnt._id&&(s.clnt=r.clnt),s.grpsOptions=!s.grpsOptions})),e.$on("choosed-crm",(function(e){s.showCrm=!1,s.clnt=e,s.inputGrpName=s.inputGrpName.length?s.inputGrpName:e.nm})),e.$on("close-crm",(function(e){s.showCrm=!1})),e.$on("add-fav",(function(e){e.grps&&(s.grps=e.grps),s.addFav(e)})),e.$on("is-archived",(function(e){s.isArchived=e})),s.sortByMtWithoutDefault=window.sortByMtWithoutDefault},methods:{clientName:function(){return this.clnt&&this.clnt.nm?this.clnt.nm:""},showClientFn(){if(!this.dispVar.isVipRealtor){return(this.confirmVip||this.$parent.confirmVip)(this.dispVar.lang)}if(!this.clnt||!this.clnt.nm){this.showCrm=!0;var s={hide:!1,title:this.$_("Contacts","contactCrm")},e=RMSrv.appendDomain("/1.5/crm?noBar=1&isPopup=1&owner=1");RMSrv.getPageContent(e,"#callBackString",s,(function(s){if(":cancel"!=s)try{var e=JSON.parse(s);window.bus.$emit("choosed-crm",e)}catch(s){console.error(s)}else console.log("canceled")}))}},isInGrp(s){return(this.prop.favGrp||[]).indexOf(parseInt(s))>-1},editGrpName(){this.inputGrpName=this.grpName,this.addNewGrp(),this.mode="edit"},confirmDelOrClear(s){let e=this,r=`${s}Tip`,t=this.strings[s],p=this.strings[r],n=this.$_(p.key,p.ctx);var a=this.$_(this.strings.cancel.key,this.strings.cancel.ctx),i=this.$_(t.key,t.ctx);return RMSrv.dialogConfirm(n,(function(r){r+""=="2"&&("clear"==s?e.clearGrpFavs():"delete"==s&&e.removeGrp())}),"",[a,i])},clearGrpFavs(s={}){null!=this.grp&&this.getGroups({mode:"clear",grp:this.grp,noEmit:s.noEmit})},removeGrp(){this.getGroups({mode:"delete",grp:this.grp})},addNewGrp(){var s=this;if(!this.dispVar.allowedEditGrpName){return(s.confirmVip||s.$parent.confirmVip)(s.dispVar.lang)}this.grpsEdit=!0,this.grpsSelect=!1,this.grpsOptions=!1},reset(){this.grpsEdit=!1,this.grpsSelect=!1,this.grpsOptions=!1,window.bus.$emit("reset")},addGrpName(s){if(this.inputGrpName){var e={nm:this.inputGrpName};this.clnt&&this.clnt._id&&(e.clnt=this.clnt._id,e.cNm=this.clnt.nm),"edit"==this.mode?(e.mode="put",e.grp=this.grp):e.mode="set",this.getGroups(e)}},toggleGrpSelect(){this.grpsSelect=!0,this.grpsEdit=!1},parseGrps(s={}){var e=[];for(let r in s)"cntr"!==r&&e.push({idx:r,val:s[r],mt:s[r]?s[r].mt:-1});return this.sortByMtWithoutDefault(e)},getGroups(s){var e=this;e.reset(),trackEventOnGoogle("saves","properties",s.mode+"Group");fetchData("/1.5/props/propGroups",{body:s},(function(r,t){return r||t.err?RMSrv.dialogAlert(r||t.err):(e.inputGrpName="",t.grps&&(e.grps=e.parseGrps(t.grps),window.bus.$emit("prop-fav-retrieved",t.grps)),"set"!=s.mode&&"get"!=s.mode||e.toggleGrpSelect(),"set"==s.mode?e.selectGrp(e.grps[1]):((t.grps||"clear"==s.mode||"delete"==s.mode)&&(s.grps=t.grps,window.bus.$emit("afterFavAction",s)),void(e.clnt=null)))}))},selectGrp(s){var e=parseInt(s.idx)||0;this.addFav({prop:this.prop,grp:e})},addFav(s){var e=this,r="favour",t=s.prop;if(t.fav&&this.isInGrp(s.grp)&&(r="unfavor"),!e.loading){trackEventOnGoogle("saves","properties",r);var p={grp:s.grp,id:t._id,topTs:t.topTs,mode:r,src:t.src};fetchData("/1.5/props/favProp",{body:p},(function(p,n){if(p||n.err)return RMSrv.dialogAlert(p||n.err);var a="favour"==r;e.prop.favGrp=e.prop.favGrp||[],"favour"==r?(e.prop.favGrp||(e.prop.favGrp=[]),e.prop.favGrp.push(s.grp),e.grps.forEach((e=>{e&&e.idx==s.grp&&(e.mt=new Date)})),e.grps=e.sortByMtWithoutDefault(e.grps)):(e.prop.favGrp.splice(e.prop.favGrp.indexOf(s.grp),1),a=e.prop.favGrp.length),t.fav=a,e.grpsSelect=!1,window.bus.$emit("prop-fav-changed",{prop:t,grps:e.grps}),window.bus.$emit("flash-message",n.msg)}))}},archive(s){var e=this;if(!e.loading){trackEventOnGoogle("archive","folder");var r={grp:this.grp,flag:s};fetchData("/1.5/propFav/archive",{body:r},(function(s,r){if(s||r.e)return RMSrv.dialogAlert(s||r.e);e.isArchived=!e.isArchived,window.bus.$emit("flash-message",r.msg),e.reset(),window.bus.$emit("refresh-archive-folder",{grp:e.grp,isArchived:e.isArchived})}))}},resetClnt(){this.clnt=null,this.inputGrpName=this.inputGrpName.split(" ")[0]},gotoSaves(){var s="/1.5/saves/properties?grp=0";RMSrv.closeAndRedirectRoot?RMSrv.closeAndRedirectRoot(RMSrv.appendDomain(s)):window.location=s},sortFolder(s){this.grps=this.sortByMtWithoutDefault(this.grps,s),this.folderSort=s}},template:'\n  <div>\n  <div class="backdrop" :class=\'{show:(grpsOptions || grpsSelect ||grpsEdit),lowIndex:showCrm}\' @click=\'reset()\'></div>\n  <div class="modal modal-60pc" id="grpSelect" :class=\'{active:grpsSelect}\'>\n    <header class="bar bar-nav">{{$_(\'Save to\')}}\n      <span class="pull-right link" style="padding-right:0" @click="gotoSaves()">{{$_(\'All Saved\')}}</span>\n    </header>\n    <div class="folderSort" style="top: 44px;">\n      <span id="createBtn" @click="addNewGrp()">\n        <span class="fa icon icon-plus"></span>\n        <span>{{$_(\'Create New Folder\')}}</span>\n      </span>\n      <span class="sort">\n        <span :class="{select:folderSort == \'time\'}" @click="sortFolder(\'time\')">{{$_(\'Time\')}}<span class="fa fa-long-arrow-down"></span></span>\n        <span :class="{select:folderSort == \'name\'}" @click="sortFolder(\'name\')">{{$_(\'Name\')}}<span class="fa fa-long-arrow-up"></span></span>\n      </span>\n    </div>\n    <div class="content" style="padding-top: 97px;">\n      <ul class="table-view">\n        <li class="table-view-cell" v-for="g in grps" @click="selectGrp(g)">\n          <span class="fa fa-star" v-show="g.idx == \'0\'"></span>\n          <span class="fa" v-show="g.idx !== \'0\'"></span>\n          <span class="group-name">{{g.val.v}}</span>\n          <span class="pull-right fa" :class="{\'fa-heart-o\':!isInGrp(g.idx), \'fa-heart\':isInGrp(g.idx)}"></span>\n        </li>\n      </ul>\n    </div>\n  </div>\n  <div class="modal" id="grpEdit" :class="{active:grpsEdit,lowIndex:showCrm}">\n      <div class=\'bar bar-nav\'>{{$_(\'Edit Folder\')}}\n      </div>\n      <div class="addClient" @click.stop="showClientFn()" v-if="dispVar.isRealtor && mode != \'new\'"><span\n          class="sprite16-18 sprite16-3-6"></span>\n          <span class="editClientName">{{clientName() ||  $_("Choose a client")}}\n            <span class="lang" v-if="clientName() && clnt.lang">({{ $_(clnt.lang,\'lang\')}})</span>\n            <span class="lang" v-if="clientName() && !clnt.lang">(En)</span>\n          </span>\n        <span class="fa fa-rmclose" style="color:#aaa;padding:10px" v-show="clnt && clnt.nm " @click.stop="resetClnt()"></span>\n      </div>\n      <div class="bar bar-standard bar-header-secondary"><input v-model="inputGrpName" :placeholder="$_(\'Input folder name\')"/></div>\n      <div class="btn-cell bar bar-tab">\n      <a @click="addGrpName()" class="btn btn-tab btn-half btn-sharp btn-fill btn-negative">{{$_(\'Save\')}}</a>\n      <a @click="reset()" class="btn btn-tab btn-half btn-sharp btn-fill length">{{$_(\'Cancel\')}}</a>\n    </div>\n  </div>\n  <div class="modal" id="grpOpts" :class="{active:grpsOptions}">\n      <div class="content">\n          <ul class="table-view">\n              <li class="table-view-cell" @click="archive(true)" v-if=\'!isArchived && (grp != 0)\'><span>{{$_(\'Archive Folder\')}}</span></li>\n              <li class="table-view-cell" @click="archive(false)" v-if=\'isArchived && (grp != 0)\'><span>{{$_(\'Unarchive Folder\')}}</span></li>\n              <li class="table-view-cell" @click="editGrpName()"><span>{{$_(\'Edit Folder Name\')}}</span></li>\n              <li class="table-view-cell" @click="confirmDelOrClear(\'clear\')"><span>{{$_(\'Clear Folder\')}}</span></li>\n              <li class="table-view-cell" @click="confirmDelOrClear(\'delete\')"><span>{{$_(\'Delete Folder\')}}</span></li>\n          </ul>\n      </div>\n  </div>\n  <div style="display:none"><span v-for="(v,k) of strings">{{ $_(v.key, v.ctx)}}</span></div>\n</div>\n  '},LANGUAGE_OBJ=[{lang:"en",nm:"English"},{lang:"zh-cn",nm:"简体中文"},{lang:"zh",nm:"繁体中文"},{lang:"kr",nm:"한국어"}];function createLangTemplate(s){var e=`\n  <div style="z-index: 400;position: fixed;top: 0;right: 0;bottom: 0;left: 0;background-color: rgba(0, 0, 0, .8);">\n    <div class="language-box"\n      style="background: rgba(245,245,245,0.8);position: fixed;padding: 20px;border-radius: 15px;top: 50%;left: 50%;transform: translate(-50%, -50%);">\n      ${LANGUAGE_OBJ.map((e=>`\n        <div class="lang" style="padding: 10px 20px;border-radius: 5px;text-align: center;background: ${s==e.lang?"rgba(130,130,130,0.6)":"transparent"}" onClick="selectLang('${e.lang}')" onMouseOver="this.style.background='rgba(130,130,130,0.6)';" onMouseOut="this.style.background='transparent';">\n          ${e.nm}\n        </div>\n        `)).join("")}\n    </div>\n  </div>\n  `,r=document.createElement("div");r.innerHTML=e,r.id="langSelectBox",document.body.appendChild(r)}function closeLangTemplate(){var s=document.getElementById("langSelectBox");document.body.removeChild(s)}function selectLang(s){closeLangTemplate(),setLang(s)}var addToShowing={props:{dispVar:{type:Object,default:function(){return{lang:"zh-cn"}}},vars:{type:Object}},mounted(){var s=window.bus,e=this;s.$on("toggle-showing",(function(s){e.getShowingList(),e.selectedProp=s,e.showShowing=!0}))},data:()=>({vipReachedLimit:!1,isntVipReachedLimit:!1,isntVipReachedTotal:!1,propsLimit:0,showingList:[],selectedProp:[],showShowing:!1}),methods:{getTranslate:function(s){return TRANSLATES[s]||s},formatHM:function(s){return s?(s.getHours()<10?"0"+s.getHours():s.getHours())+":"+(s.getMinutes()<10?"0"+s.getMinutes():s.getMinutes()):""},date2Num:function(s){return s?""+s.getFullYear()+(s.getMonth()+1<10?"0"+(s.getMonth()+1):s.getMonth()+1)+(s.getDate()<10?"0"+s.getDate():s.getDate()):""},getShowingList(){var s=this;fetchData("/1.5/showing/upcoming",{body:{}},(function(e,r){if(e||r.err)return RMSrv.dialogAlert(e||r.err);r.ok?(s.showingList=r.list,s.propsLimit=r.propsLimit,s.vipReachedLimit=r.vipReachedLimit,s.isntVipReachedLimit=r.isntVipReachedLimit,s.isntVipReachedTotal=r.isntVipReachedTotal):alert(r)}))},addToShowingList(s){let e,r=this;var t=r.selectedProp;return this.vipReachedLimit,this.isntVipReachedLimit&&!s||this.isntVipReachedTotal&&!s?r.confirmVip(r.dispVar.lang):(e="string"==typeof t?1:t.length,s&&(s.props.length,this.propsLimit),void(s?this.addToShowing(t,s._id,vars.d):this.addToShowing(t,"",vars.d)))},addToShowing(s,e,r,t,p){if((this.isntVipReachedLimit||this.vipReachedLimit||this.isntVipReachedTotal)&&!e)return;if(!(s&&s.length||e))return;let n="/1.5/showing/detail?inFrame=1";s&&("string"!=typeof s&&(s=s.join(",")),n=n+"&propIds="+s),e&&(n=n+"&showingId="+e),r&&(n=n+"&d="+r),t&&(n=n+"&today="+t),openContent(n,{toolbar:!1}),this.reset()},reset(){this.showShowing=!1,window.bus.$emit("reset")}},template:'\n    <div class="backdrop" :class=\'{show:showShowing}\' @click=\'reset()\'></div>\n    <div class="modal modal-60pc" id=\'showingSelect\' :class="{\'active\':showShowing}">\n      <header class="bar bar-nav"> {{getTranslate(\'add\')}}</header>\n      <div class="content">\n          <ul class="table-view">\n              <li class="table-view-cell" id="createBtn" @click="addToShowingList()" :class="{cantAdd:vipReachedLimit}">\n                <span>{{getTranslate(\'createNShowing\')}}</span>\n                <span class="icon icon-plus"></span>\n              </li>\n              <li class="table-view-cell" v-for="showing in showingList" @click="addToShowingList(showing)" :class="{full:showing.props.length == propsLimit}">\n                <span>{{showing.dt}}</span>\n                <span style="padding-left:5px">{{showing.cNm||getTranslate(\'noClnt\')}}</span>\n                <span class="pull-right" v-show="showing.props.length == propsLimit">{{getTranslate(\'full\')}}</span>\n              </li>\n          </ul>\n      </div>\n    </div>\n    '},propSelTable={props:{favProps:{type:Array,default:()=>[]},showPropTable:{type:Boolean,default:!1},action:{type:String,default:""},propImgHeight:{type:Number,default:0},from:{type:String,default:""},currentGrp:{default:function(){return{idx:"0",val:{v:"Default"}}}},grps:{type:Array,default:()=>[]}},data:()=>({selectedProp:[],strings:{searchID:{key:"Search",ctx:""},alreadyExists:{key:"The current property already exists",ctx:""},saveFailed:{key:"Failed",ctx:""},noResults:{key:"No Results",ctx:""},showing:{key:"+ Showing",ctx:""},share:{key:"Share",ctx:""},cma:{key:"CMA",ctx:""},rm:{key:"RealMaster",ctx:""},NO_SELECTION:{key:"No Selection",ctx:""}}}),mounted(){if(!window.bus)return console.error("global bus required!");window.bus.$on("reset-sel-prop",(()=>{this.selectedProp=[]}))},methods:{reset(){window.bus.$emit("reset")},selectProp(s){var e=this,r=e.selectedProp.indexOf(s),t=e.favProps.find((e=>e._id==s));r>=0?(e.selectedProp.splice(r,1),t.selected=!1):(e.selectedProp.push(s),t.selected=!0),e.$forceUpdate()},formatPrice:s=>"number"==typeof s?"$"+s.toString().replace(/\B(?=(\d{3})+(?!\d))/g,","):s,parseSqft:s=>/\-/.test(s)?s:("number"==typeof s&&(s=""+s),/\./.test(s)?s.split(".")[0]:s),getPropdetail(s,e){var r=this;fetchData("/1.5/props/detail",{body:{_id:e,useThumb:!0}},(function(e,t){if(e||t.e)return RMSrv.dialogAlert(e||t.e);if(t.detail){let e=t.detail;e.fav=!0,e.thumbUrl||(e.thumbUrl=e.picUrls[0]),e.selected=!0,e.favGrp?e.favGrp.push(s):e.favGrp=[s],r.favProps.unshift(e)}else window.bus.$emit("flash-message",r.$_(r.strings.noResults.key,r.strings.noResults.ctx))}))},openAutoCompleteSearch(){var s=this;RMSrv.getPageContent("/1.5/autoCompleteSearch?isPopup=1&isMLS=1","#callBackString",{hide:!1,title:s.$_(s.strings.searchID.key,s.strings.searchID.ctx)},(function(e){if(":cancel"!=e){var r=JSON.parse(e).id;s.favProps.find((s=>s._id==r))?window.bus.$emit("flash-message",s.$_(s.strings.alreadyExists.key,s.strings.alreadyExists.ctx)):(s.selectedProp.push(r),window.bus.$emit("add-fav",{grp:s.currentGrp.idx,prop:{_id:r},grps:s.grps}),s.getPropdetail(s.currentGrp.idx,r))}}))},commitSelected(){var s=this;if(s.selectedProp.length<1)return window.bus.$emit("flash-message",s.$_(s.strings.NO_SELECTION.key,s.strings.NO_SELECTION.ctx));window.bus.$emit("commit-selected",s.selectedProp)},saveCMAForWeb(){if(this.selectedProp.length<1)return window.bus.$emit("flash-message",this.$_(this.strings.NO_SELECTION.key,this.strings.NO_SELECTION.ctx));fetchData("/1.5/saveCMA/saveCMAIds",{body:{ids:this.selectedProp}},((s,e)=>{if(!e.ok)return bus.$emit("flash-message",this.$_(this.strings.saveFailed.key,this.strings.saveFailed.ctx));bus.$emit("flash-message",e.msg)}))},dotdate(s,e,r="."){if(!s)return"";"number"==typeof s&&(s=(s+="").slice(0,4)+"/"+s.slice(4,6)+"/"+s.slice(6,8)),"string"!=typeof s||/\d+Z/.test(s)||(s+=" EST");var t=e?"年":r,p=e?"月":r,n=e?"日":"";if(/^2\d{3}-\d{1,2}-\d{1,2}/.test(s)&&!/\d+Z/.test(s)){var a=s.split(" ")[0].split("-");return a[0]+t+a[1]+p+a[2]+n}var i=new Date(s);return!i||isNaN(i.getTime())?s:i.getFullYear()+t+(i.getMonth()+1)+p+i.getDate()+n}},template:'\n    <div class="prop-table" v-if=\'showPropTable\'>\n      <div v-for="prop in favProps" :key="prop._id">\n        <div class="prop-info" @click=selectProp(prop._id) :class=\'{selected:prop.selected}\'>\n          <span class="stp" :class="prop.tagColor">\n            <span>{{prop.saleTpTag || prop.lstStr}}</span>\n          </span>\n          <span class="sort-number" v-if="selectedProp.indexOf(prop._id) >-1">\n            {{selectedProp.indexOf(prop._id)+1}}\n          </span>\n          <span class=\'table-image\'>\n            <img\n              :src="prop.thumbUrl || \'/img/noPic.png\'"\n              style="background-image: url(\'/img/noPic.png\');"\n              :style="{\'height\':propImgHeight+\'px\'}"\n              @error="e => { e.target.src = \'/img/noPic.png\'}"\n              referrerpolicy="same-origin">\n            <span class=\'dom\' v-if="!prop.login && prop.dom">{{prop.dom}} {{$_(\'days\')}}</span>\n            <span class="date stp" :class="prop.tagColor" v-show="(prop.tagColor == \'red\') && (prop.spcts||prop.mt||prop.ts) ">&nbsp;{{dotdate(prop.spcts||prop.mt||prop.ts)}}</span>\n          </span>\n          <div class="addr one-line" v-if="!prop.login">\n            <p v-show="prop.addr && prop.addr">{{prop.unt}} {{prop.addr}}</p>\n            <p v-show="(!prop.addr) && prop.cmty"> {{prop.cmty}}</p>\n            <p>{{prop.city}}, {{prop.prov}}</p>\n          </div>\n          <div class="addr one-line" v-if="prop.login"><span v-show="prop.addr">{{prop.addr}}, {{prop.city}}</span><span\n            v-show="!prop.addr">{{prop.city}}, {{prop.prov}}</span>\n          </div>\n          <p class="bdrms" v-if="!prop.login">\n            <span v-if="prop.bdrms"><span class="fa fa-rmbed"></span> {{prop.bdrms}}{{prop.br_plus?\'+\'+prop.br_plus:\'\'}}</span>\n            <span v-if="prop.rmbthrm || prop.tbthrms || prop.bthrms"><span class="fa fa-rmbath"></span> {{prop.rmbthrm || prop.tbthrms || prop.bthrms}}</span>\n            <span v-if="prop.rmgr||prop.tgr||prop.gr"><span class="fa fa-rmcar"></span> {{prop.rmgr||prop.tgr||prop.gr}}</span>\n          </p>\n          <p class="price" v-if="!prop.login">\n            <span>{{formatPrice(prop.sp || prop.lp || prop.lpr)}}</span>\n            <span v-if="prop.sp" class="price-change sold">{{formatPrice(prop.lp || prop.lpr)}}</span>\n          </p>\n          <p v-if="!prop.login && (prop.sqft || (prop.sqft1&&prop.sqft2) || prop.rmSqft)">\n              <span v-if="prop.sqft">{{parseSqft(prop.sqft)}}</span>\n              <span v-else-if="prop.sqft1&&prop.sqft2">{{prop.sqft1}}-{{prop.sqft2}}</span>\n              <span v-if="prop.rmSqft && prop.login">\n                <span v-if="prop.sqft && /-/.test(prop.sqft) && (prop.sqft!=prop.rmSqft)">&nbsp;({{parseSqft(prop.rmSqft)}})</span>\n                <span v-if=\'!prop.sqft\'>{{parseSqft(prop.rmSqft)}} ({{$_(\'Estimated\')}})</span>\n              </span>\n              <span>&nbsp;{{$_(\'ft&sup2;\')}}</span>\n            </span>\n          </p>\n          <p v-if="!prop.login && prop.front_ft">\n            {{prop.front_ft}}&nbsp;* {{prop.depth}}{{prop.lotsz_code}} {{prop.irreg}}\n          </p>\n        </div>\n      </div>\n      <div>\n        <div class="prop-info add-new-prop" @click="openAutoCompleteSearch()">\n          <span class="plus-icon" :style="{\'height\':propImgHeight+\'px\',\'line-height\':propImgHeight+\'px\'}">\n            <span class="fa fa-plus"></span>\n          </span>\n          <p class="addr">{{$_(\'Add prop by ID\')}}</p>\n        </div>\n      </div>\n    </div>\n    <div class="btn-cell bar bar-tab" v-show="showPropTable" style="display:flex;align-items: center;">\n      <a @click="commitSelected()" class="btn btn-tab btn-half btn-sharp btn-fill btn-negative">\n        <span v-if="action.length">{{$_(strings[action].key)}}&nbsp;</span>\n        <span class="badge" style="color:white">{{selectedProp.length}}</span>\n      </a>\n      <a @click="saveCMAForWeb()" class="btn btn-tab btn-half btn-sharp btn-fill btn-primary" style="color:white" v-if="action == \'cma\'">{{$_(\'Save for Web\')}}</a>\n      <a @click="reset()" class="btn btn-tab btn-half btn-sharp btn-fill length">{{$_(\'Cancel\')}}</a>\n    </div>\n  '},listingShareDesc={computed:{propSqft:function(){let s=this.prop||{};if(!s.sqft||/-/.test(s.sqft))return s.sqft;let e=parseInt(s.sqft);return isNaN(e)?s.sqft:e}},props:{isApp:{type:Boolean,default:!1},prop:{type:Object,default:function(){return{lp:0}}}},methods:{getTranslate:function(s){return TRANSLATES[s]||s},isArray:s=>Array.isArray(s),isRMProp:s=>/^RM/.test(s.id),getDesc:s=>s?s.length>70?s.substr(0,70)+"...":s:""},template:"\n  <div>\n  <span id=\"share-title-en\"><span v-if=\"!isApp\">RealMaster •  </span><span v-if=\"prop.ltp=='assignment'\">Assignment\n      • </span><span v-if=\"prop.ltp=='exlisting'\">Exclusive • </span><span\n      v-if=\"prop.ltp=='rent' && !prop.cmstn\">Landlord Rent • </span><span\n      v-if=\"prop.ltp=='rent' && prop.cmstn\">Rent • </span><span> {{(prop.lp || prop.lpr)}}\n      • <span v-if=\"prop.daddr !== 'N'\">{{prop.addr}} {{prop.unt||''}}, </span>{{prop.city_en || prop.city}}\n      {{prop.prov_en || prop.prop}}</span>\n  </span>\n  <span id=\"share-desc-en\">\n    {{isRMProp(prop) ? prop.id +', ' : (prop.sid || prop._id)+', '}}\n    {{ isArray(prop.ptp) ? prop.ptp[0] : (prop.ptype2_en?prop.ptype2_en.join(' '):'') }}\n    {{prop.pstyl_en}}\n    {{propSqft?', '+propSqft+' Sqft, ':''}}<span v-if=\"prop.bcf != 'b'\">Bedroom: {{prop.rmbdrm||prop.tbdrms||prop.bdrms}}, Kitchen:\n      {{prop.kch}}, Bathroom: {{prop.rmbthrm||prop.tbthrms||prop.bthrms}}, Parking:\n      {{prop.rmgr||prop.tgr||prop.gr}}</span>{{getDesc(prop.m)}}\n  </span>\n  <span id=\"share-title\"><span v-if=\"!isApp\">{{$_('RealMaster') }} • </span><span v-if=\"prop.ltp=='assignment'\">\n      {{$_('Assignment') }} • </span><span v-if=\"prop.ltp=='exlisting'\"> {{$_('Exclusive','realtor sale')}} • </span><span\n      v-if=\"prop.ltp=='rent' && !prop.cmstn\"> {{$_('Landlord Rent')}} • </span><span\n      v-if=\"prop.ltp=='rent' && prop.cmstn\"> {{$_('Rent','share-title rent')}} • </span><span>\n      {{(prop.lp || prop.lpr)}} • <span v-if=\"prop.daddr !== 'N'\">{{prop.addr}}\n        {{prop.unt||''}}, </span>{{prop.city}} {{prop.prov}}</span>\n      </span>\n  <span id=\"share-desc\">\n    {{isRMProp(prop) ? prop.id +', ' : (prop.sid || prop._id)+', '}}\n    {{ isArray(prop.ptp) ? prop.ptp[3] : (prop.ptype2?prop.ptype2.join(' '):'') }}\n    {{propSqft ? ', '+propSqft+' '+$_('Sqf','property')+', ':''}}<span v-if=\"prop.bcf != 'b'\">{{$_('Bedroom')}}:\n      {{prop.rmbdrm||prop.tbdrms||prop.bdrms}}, {{$_('Kitchen')}}: {{prop.kch}}, {{$_('Bathroom')}}: {{prop.rmbthrm||prop.tbthrms||prop.bthrms}},\n      {{$_('Parking')}}: {{prop.rmgr||prop.tgr||prop.gr}}</span>{{getDesc(prop.m || prop.m_zh)}}\n    </span>\n</div>\n  "},shareDialog={props:{option:{type:Object},noAdvance:{type:Boolean},height:{type:Number},wDl:{type:Boolean},wSign:{type:Boolean},wComment:{type:Boolean,default:!0},dispVar:{type:Object,default:function(){return{isRealtor:!1,isVipRealtor:!1,lang:"zh-cn"}}},noSign:{type:Boolean},noLang:{type:Boolean},prop:{type:Object,required:!0,default:function(){return{}}},showComment:{type:Boolean,default:function(){return!1}},noFlyer:{type:Boolean,default:function(){return!1}}},data:()=>({wSign2:!0,wDl2:!0,wCommentCheck:!0}),watch:{},mounted(){this.wCommentCheck=this.wComment,0==this.wSign&&(this.wSign2=!1)},computed:{isProj:function(){var s=this.prop;return!(!s.deposit_m&&!s.closingDate)},computedVersion:function(){return this.$parent.isNewerVer(this.dispVar.coreVer,"5.6.0")},showPromo:{cache:!1,get(){return this.dispVar.isRealtor&&!/^RM/.test(this.prop.id)}},showSignature:{cache:!1,get(){return this.dispVar.isRealtor}},wDlDisable:{cache:!1,get(){return!this.dispVar||!(!this.dispVar.isRealtor||this.dispVar.isVipRealtor)}},wSignDisable:function(){return!this.dispVar}},methods:{getTranslate:function(s){return TRANSLATES[s]||s},checkIsAllowed(s){if(!this.dispVar.shareLinks)return!1;var e=this.dispVar.shareLinks.l||[],r=this.dispVar.shareLinks.v||[],t=e.indexOf(s),p=this.dispVar.isVipRealtor||0===r[t];return t>-1&&p},rmShare(s,e){RMSrv.share(s,e)},rmCustWechatShare(){if(!this.computedVersion)return this.confirmUpgrade(this.dispVar.lang);var s="/1.5/htmltoimg/templatelist?id=",e=this.prop._id;/^RM/.test(this.prop.id)&&(e=this.prop.id),s+=e,RMSrv.openTBrowser(s,{title:this._("Choose Template")})},rmsrvDownloadImage(){var s,e="http://img.realmaster.com/mls/1/668/********.jpg";(s=document.querySelector("#share-image"))&&(e=s.textContent),RMSrv.downloadImage(e,{},(function(s,e){let r=s||e;RMSrv.dialogAlert(r)}))},toggleDrop(s){window.bus.$emit("toggle-drop",s)},cancelPromoteModal(){RMSrv.share("hide")},promote(s){if(!this.checkIsAllowed(s))return this.confirmVip(this.dispVar.lang);var e="/1.5/promote/mylisting?ml_num="+this.prop.sid+"&to="+s;this.$parent&&this.$parent.toggleDrop&&this.$parent.toggleDrop(),RMSrv.share("hide"),this.$parent.closeAndRedirect?this.$parent.closeAndRedirect(e):window.location=e},createWePage(s){if(!this.checkIsAllowed(s))return this.confirmVip(this.dispVar.lang);var e="/1.5/wecard/edit/"+(this.prop._id||this.prop.sid)+"?shSty=";"vt"==s||"blog"==s?e+=s:"mylisting"==s&&(e="/1.5/promote/mylisting?ml_num="+this.prop._id),this.$parent.closeAndRedirect?this.$parent.closeAndRedirect(e):window.location=e}},template:'\n<div id="shareDialog">\n  <div class="backdrop" id="backdrop" style="display:none"></div>\n  <nav class="menu slide-menu-bottom smb-md" :style="{height:height}">\n      <div class="first-row" :class="{visitor:!dispVar.isRealtor}">\n          <div @click="rmCustWechatShare()" v-show="dispVar.isRealtor && !isProj && !noFlyer"><span class="sprite50-45 sprite50-5-1"></span>\n              <div class="inline">{{$_(\'Wechat Flyer\')}}</div>\n          </div>\n          <div @click="rmShare(\'wechat-moment\')"><span class="sprite50-45 sprite50-5-3"></span>\n              <div class="inline">{{$_(\'Wechat Moment\')}}</div>\n          </div>\n          <div @click="rmShare(\'wechat-friend\')"><span class="sprite50-45 sprite50-5-2"></span>\n              <div class="inline">{{$_(\'Wechat Friend\')}}</div>\n          </div>\n          <div @click="rmShare(\'facebook-feed\')"><span class="sprite50-45 sprite50-5-4"></span>\n              <div class="inline">{{$_(\'Facebook\')}}</div>\n          </div>\n          <div @click="rmShare(\'qr-code\')"><span class="sprite50-45 sprite50-6-1"></span>\n              <div class="inline">{{$_(\'QR-Code\')}}</div>\n          </div>\n          <div @click="rmShare(\'other\')"><span class="sprite50-45 sprite50-5-5"></span>\n              <div class="inline">{{$_(\'More\')}}</div>\n          </div>\n      </div>\n      <div class="split" v-show="dispVar.isRealtor && !dispVar.listShareMode">\n          <div class="left inline"></div>\n          <div class="text inline"><span>{{$_(\'Advanced Features\')}}</span></div>\n          <div class="right inline"></div>\n      </div>\n      <div class="second-row" v-show="(dispVar.isRealtor || dispVar.isDevGroup) && !dispVar.listShareMode && !noAdvance">\n          <div id="shareToNews" v-show="dispVar.publishNews && dispVar.shareLinks.l.indexOf(\'news\')>-1"><span class="sprite50-45 sprite50-6-3"></span>\n              <div class="inline">{{$_(\'News\')}}</div>\n          </div>\n          <div @click="promote(\'58\')" ngClick="promote(\'58\', formData); toggleModal(\'savePromoteModal\')" v-show="dispVar.shareLinks && dispVar.shareLinks.l.indexOf(\'58\')>-1 && prop.pcls !== \'b\' && prop.src == \'TRB\'"><span class="sprite50-45 sprite50-4-4"></span>\n              <div class="inline">58.com</div>\n          </div>\n          <div @click="promote(\'market\')" ngClick="promote(\'market\', formData); toggleModal(\'savePromoteModal\')" v-show="dispVar.shareLinks && dispVar.shareLinks.l.indexOf(\'market\')>-1 && prop.pcls !== \'b\' && prop.src == \'TRB\'"><span class="sprite50-45 sprite50-4-2"></span>\n              <div class="inline">{{$_(\'Listing Market\')}}</div>\n          </div>\n          <div @click="createWePage(\'vt\')" v-show="dispVar.shareLinks && dispVar.shareLinks.l.indexOf(\'vt\')>-1 && prop.src == \'TRB\'"><span class="sprite50-45 sprite50-4-3"></span>\n              <div class="inline">{{$_(\'WePage Flyer\')}}</div>\n          </div>\n          <div @click="createWePage(\'blog\')" v-show="dispVar.shareLinks && dispVar.shareLinks.l.indexOf(\'blog\')>-1 && prop.src == \'TRB\'"><span class="sprite50-45 sprite50-4-5"></span>\n              <div class="inline">{{$_(\'WePage Blog\')}}</div>\n          </div>\n      </div>\n      <div class="cancel">\n          <div class="promoWrapper" v-show="!noSign">\n              <div class="inline" id="id_with_sign_wrapper" v-show="showSignature"><label id="id_with_sign"><input type="checkbox" v-model="wSign2" checked="true" :class="{disabled:wSignDisable}"/> {{$_(\'Signature\')}}</label></div>\n              <div class="inline" id="id_with_dl_wrapper" v-show="showPromo"><label id="id_with_dl" v-show="dispVar.isVipUser"><input type="checkbox" v-model="wDl2" checked="true" :class="{disabled:wDlDisable}" :disabled="wDlDisable"/>{{$_(\'Promo\')}}</label></div>\n              <div class="inline" v-show="showComment"><label id="id_with_cm"><input type="checkbox" v-model="wCommentCheck"/>{{$_(\'With Comments\')}}</label></div>\n          </div>\n          <div class="lang-selectors-wrapper">\n              <div class="segmented-control lang-selectors">\n              <a class="control-item lang-selector" id="id_share_lang_en" onclick="RMSrv.share(\'lang-en\');" href="javascript:;" v-if="dispVar.lang != \'en\'">En</a>\n              <a class="control-item lang-selector" id="id_share_lang_zh" onclick="RMSrv.share(\'lang-zh-cn\');" href="javascript:;" v-if="!(dispVar.lang == \'zh\'||dispVar.lang == \'zh-cn\')">Zh</a>\n              <a class="control-item lang-selector" id="id_share_lang_kr" onclick="RMSrv.share(\'lang-kr\');" href="javascript:;" v-if="dispVar.lang != \'kr\'">Kr</a>\n              <a class="control-item lang-selector active" id="id_share_lang_cur" onclick="RMSrv.share(\'lang-cur\');" href="javascript:;">\n                <span v-show="dispVar.lang == \'zh\'">繁</span>\n                <span v-show="dispVar.lang == \'zh-cn\'">中</span>\n                <span v-show="dispVar.lang == \'kr\'">한</span>\n                <span v-show="dispVar.lang !== \'zh\' && dispVar.lang !== \'zh-cn\' && dispVar.lang !== \'kr\'">En</span>\n              </a></div>\n          </div><a class="cancel-btn" href="javascript:;" @click="cancelPromoteModal()">{{$_(\'Cancel\')}}</a></div>\n  </nav>\n  <div class="pic" id="id_share_qrcode">\n      <div id="id_share_qrcode_holder"></div><br/>\n      <div style="border-bottom: 2px solid #F0EEEE; margin:10px 15px 10px 15px;"></div><button class="btn btn-block btn-long" onclick="RMSrv.share(\'qr-code-close\');" style="border:1px none;">{{$_(\'Close\')}}</button></div>\n    <div class="hide" style="display:none">{{$_(\'Available only for Premium VIP user! Upgrade and get more advanced features.\')}} {{$_(\'See More\')}} {{$_(\'Later\')}}\n  </div>\n</div>\n    '},properties={data:()=>({page:0,waiting:!1,hasMoreProps:!1,loading:!1,hasMap:!0,showPropTable:!1,favProps:[],currentGrp:{idx:"0",val:{v:"Default"}},grp:0,grps:[],grpsOptions:!1,showGrps:!1,showShowing:!1,showShare:!1,showFavGroups:!1,prop:{fav:!1,favGrp:[]},action:"",rcmdHeight:0,propImgHeight:0,selectedProp:[],shareList:[],cntTotal:0,wDl:!0,wSign:!0,scrollElement:null,dispVar:{lang:"zh-cn",sessionUser:{},allowedShareSignProp:!1,listShareMode:!0,isCip:!1,isDevGroup:!1,isRealGroup:!1,isLoggedIn:!1,isApp:!1,isNoteAdmin:!1},school:"University of Toronto",schoolProv:"ON",centerLat:43.66090860000001,centerLng:-79.39595179999999,isArchived:!1,archivedGrps:{},hasArchivedGrps:!1,datas:["allowedEditGrpName","lang","isVipRealtor","isRealtor","sessionUser","allowedShareSignProp","shareUID","isVipUser","isCip","shareAvt","distances","rentalSorts","languageAbbrObj","isDevGroup","isRealGroup","isLoggedIn","coreVer","isEmailVerified","isApp","isNoteAdmin"],sort:"dist-asc",dist:500,recos:[],favMap:{},archivedMap:{},clacClass:"one",init:!0,strings:{searchID:{key:"Search",ctx:""},alreadyExists:{key:"The current property already exists",ctx:""},saveFailed:{key:"Failed",ctx:""}},folderSort:"time",filterStatus:"All",statusType:[{k:"All",v:"All"},{k:"Sale",v:"For Sale"},{k:"Sold",v:"Sold"},{k:"Delisted",v:"Delisted"}]}),beforeMounted(){},mounted(){this.$getTranslate(this);var s=this;if(!(bus=window.bus))return console.error("global bus required!");vars.grp&&(s.grp=vars.grp),s.getFavProps(),s.getRecentProps(),window.showMapView||(window.showMapView=this.showMapView),s.scrollElement=document.getElementById("saves-container"),s.scrollElement.addEventListener("scroll",s.listScrolled),s.rcmdHeight=parseInt(window.innerWidth/1.6),s.propImgHeight=parseInt((window.innerWidth-48)/1.6/2),s.getPageData(s.datas,{},!0),bus.$on("pagedata-retrieved",(function(e){s.dispVar=Object.assign(s.dispVar,e)})),bus.$on("reset",(()=>{this.reset()})),bus.$on("afterFavAction",(s=>{this.afterFavAction(s)})),bus.$on("prop-fav-retrieved",(e=>{s.grps=s.parseGrps(e)})),bus.$on("refresh-archive-folder",(({grp:e,isArchived:r})=>{s.isArchived=r,s.refreshFolder(e,r)})),bus.$on("prop-fav-changed",(({prop:e,grps:r})=>{s.favProps.forEach((s=>{s._id==e._id&&(s=e)})),s.grps=r})),bus.$on("selectGroup",(s=>{this.getFavProps(s+"")})),bus.$on("commit-selected",(e=>{s.selectedProp=e,s.commitSelected()})),window.loadLazyImg&&loadLazyImg(),this.clacItemClass(),s.sortByMtWithoutDefault=window.sortByMtWithoutDefault},computed:{sessionUserContact:function(){var s,e=[];if(!this.wSign)return"";s=this.dispVar.sessionUser||{};var r="";return(r="en"==this.dispVar.lang?s.nm_en:s.nm_zh)?e.push(r):e.push(s.nm),s.mbl&&e.push(s.mbl),e.push(s.eml),e=e.join(", ")},shareImage:function(){return this.prop.thumbUrl?this.prop.thumbUrl:"/img/create_exlisting.png"},mShareImage:function(){return this.prop.thumbUrl?this.prop.thumbUrl:this.dispVar.shareAvt},mShareData:function(){var s=this.shareList,e=(this.prop,this.dispVar.lang),r=`id=${s.join(",")}&tp=listingList&lang=${e}&share=1`;return this.dispVar.allowedShareSignProp?(this.wDl&&(r+="&wDl=1"),this.wSign&&(r+="&aid="+this.dispVar.shareUID)):(r+="&wDl=1",r+="&uid="+this.dispVar.shareUID),r},shareData:function(){var s,e=this.prop,r=this.dispVar.lang;s=this.isRMProp(e)?e.id:e._id||e.sid;var t=this.dispVar.shareUID,p=`id=${s}&tp=listing&lang=${r}`;return this.isRMProp(e)&&(t&&!function(s){return(s.adrltr?s.adrltr._id:null)==s.uid}(e)||(t=e.uid)),this.dispVar.allowedShareSignProp?(this.wDl&&(p+="&wDl=1"),this.wSign&&(p+="&aid="+this.dispVar.shareUID)):(p+="&wDl=1",this.dispVar.isLoggedIn&&(p+="&uid="+t)),this.isRMProp(e)&&(/wDl=1/.test(p)||(p+="&wDl=1"),p+="&wSign=1"),p}},methods:{refreshFolder(s,e){var r=null,t=this;e?(r=t.favMap[s],delete t.favMap[s],t.archivedMap[s]=r):(r=t.archivedMap[s],delete t.archivedMap[s],t.favMap[s]=r),t.grps=t.parseGrps(t.favMap),t.archivedGrps=t.parseGrps(t.archivedMap),Object.keys(t.archivedMap).length>0?t.hasArchivedGrps=!0:t.hasArchivedGrps=!1,setTimeout((()=>{t.toggleArchived()}),1e3)},getRecentProps(){var s=this;if(null==typeof page||"student_rental"!=page){setLoaderVisibility("block"),fetchData("/1.5/props/recentFavProps",{},(function(e,r){if(setLoaderVisibility("none"),e||r.e)return RMSrv.dialogAlert(e||r.e);r.items&&r.items.length>0?r.items.map((s=>{s.selected=!1})):window.bus.$emit("flash-message",s.getTranslate("noResults")),s.recos=r.items}))}},isRMProp:s=>/^RM/.test(s.id),listScrolled:function(){var s=this;!s.waiting&&s.hasMoreProps&&(s.waiting=!0,s.loading=!0,setTimeout((function(){s.waiting=!1;var e=s.scrollElement;e.scrollHeight-e.scrollTop<=e.clientHeight+260&&s.hasMoreProps?(s.page+=1,s.getFavProps(null,"more")):s.loading=!1}),400))},openSchoolModal(){var s="/1.5/direction/places?prov="+this.schoolProv;RMSrv.getPageContent(s,"#callBackString",{hide:!1,title:"Select School"},(s=>{if(":cancel"!=s){var e;try{e=JSON.parse(s)}catch(s){e={}}this.school=e.nm,this.centerLat=e.lat,this.centerLng=e.lng,this.schoolProv=e.prov,this.getFavProps("studentRental")}}))},toggleArchived(){document.querySelector(".folderSort").classList.toggle("hide"),document.querySelector(".archivedGrps")&&(document.querySelector(".archivedGrps").classList.toggle("all"),document.querySelector(".icon-up-nav").classList.toggle("hide"),document.querySelector(".icon-down-nav").classList.toggle("hide"))},getFavProps:function(s,e){var r=this;s&&(r.grp=s,r.page=0),"archived"==e&&(r.isArchived=!0),"normal"==e&&(r.isArchived=!1),window.bus.$emit("is-archived",r.isArchived);var t="/1.5/props/favProps",p={grp:r.grp,page:r.page};"All"!==this.filterStatus&&(p.favSort=this.filterStatus),null!=typeof page&&"student_rental"==page&&(t="/1.5/props/search",p={ptype:"r",src:"mls",saletp:"lease",page:r.page,sort:r.sort,centerLat:r.centerLat,centerLng:r.centerLng,dist:r.dist}),setLoaderVisibility("block"),fetchData(t,{body:p},(function(s,t){if(setLoaderVisibility("none"),s||t.e)return RMSrv.dialogAlert(s||t.e);t.items&&t.items.length>0?t.items.map((s=>{s.selected=!1})):window.bus.$emit("flash-message",r.getTranslate("noResults")),"more"==e?r.favProps=r.favProps.concat(t.items):(r.favProps=t.items,r.selectedProp=[],window.bus.$emit("reset-sel-prop")),r.favMap=t.grps,r.grps=r.parseGrps(t.grps),t.archivedGrps&&Object.keys(t.archivedGrps).length>0?(r.archivedMap=t.archivedGrps,r.hasArchivedGrps=!0,r.archivedGrps=r.parseGrps(t.archivedGrps)):r.hasArchivedGrps=!1,r.showGrps=!1,r.init&&r.grps.length>=10&&(r.showGrps=!0,r.init=!1),r.cntTotal=t.cnt,r.favProps.length<r.cntTotal&&t.items.length?r.hasMoreProps=!0:r.hasMoreProps=!1}))},showGroups:function(){var s=this;s.showGrps=!s.showGrps,s.$forceUpdate()},popupAction:function(s){var e=this;e.action==s?e.reset():(trackEventOnGoogle("saves","properties",s),"edit"==s?(window.bus.$emit("editGroups",{grp:this.grp,grpInfo:this.currentGrp.val}),e.showPropTable=!1):(e.showPropTable=!0,e.action=s)),e.$forceUpdate()},reset:function(){var s=this;s.grpsOptions=!1,s.showGrps=!1,s.showShowing=!1,s.showFavGroups=!1,s.showPropTable=!1,s.action=""},toggleFav(s){this.showFavGroups=!0,this.prop=s},parseGrps(s={}){var e=[],r=this;for(let t in s)"cntr"!==t&&e.push({idx:t,val:s[t],mt:s[t].mt||-1}),t==r.grp&&(r.currentGrp={idx:t,val:s[t],mt:s[t].mt||-1});return r.sortByMtWithoutDefault(e,this.folderSort)},afterFavAction(s){var e=this;s.grps&&(e.grps=e.parseGrps(s.grps)),"clear"!=s.mode||s.noEmit?"delete"==s.mode&&(e.grp=0,e.getFavProps()):e.favProps=[]},formatPrice:s=>"number"==typeof s?"$"+s.toString().replace(/\B(?=(\d{3})+(?!\d))/g,","):s,getTranslate:function(s){return TRANSLATES[s]||s},goCMA(){let s=`/1.5/htmltoimg/dottemplate?lang=${this.dispVar.lang}&showSign=true&selModel=diff&nm=comparetable&ids=`;var e=this.selectedProp;e&&("string"!=typeof e&&(e=e.join(",")),s+=e);let r={title:this.getTranslate("rm")};openContent(s,r)},commitSelected(){var s=this;if(s.showPropTable=!1,"showing"==s.action)window.bus.$emit("toggle-showing",s.selectedProp);else if("cma"==s.action)this.goCMA();else if(1==s.selectedProp.length){for(var e of s.favProps)e._id==s.selectedProp[0]&&(this.prop=e);RMSrv.showSMB("show")}else RMSrv.showSMB("show","m-share-");s.shareList=s.selectedProp,window.bus.$emit("reset-sel-prop"),s.selectedProp=[],s.favProps.forEach((s=>{s.selected=!1})),s.$forceUpdate()},showMapView(){var s=vars.d||"";s&&(s=`&d=${s}`);var e=`/1.5/mapSearch?appMapMode=fav&mode=map&grp=${this.grp}${s}`;window.location=e},clacItemClass:function(){1==this.recos.length?this.clacClass="one":2==this.recos.length?this.clacClass="two":this.clacClass="three"},getdotdatetime(s){if(!s)return"";s=new Date(s);var e=new Date;return s.getFullYear()==e.getFullYear()&&s.getMonth()==e.getMonth()&&s.getDate()==e.getDate()?s.getHours()+":"+(s.getMinutes()<10?"0":"")+s.getMinutes():s.getFullYear()+"."+(s.getMonth()+1)+"."+s.getDate()},sortFolder(s){this.grps=this.sortByMtWithoutDefault(this.grps,s),this.folderSort=s},selecteStatus(s){if(this.filterStatus==s)return;this.page=0;let e=document.querySelector(".prop-list");e&&e.scrollIntoView(!0),this.filterStatus=s,this.getFavProps(this.grp,"normal",s)}}};initUrlVars();var app=Vue.createApp(properties);app.component("prop-item",propItem),app.component("prop-element-scroll-x",propElementScrollX),app.component("prop-fav-actions",propFavActions),app.component("prop-sel-table",propSelTable),app.component("add-to-showing",addToShowing),app.component("share-dialog",shareDialog),app.component("listing-share-desc",listingShareDesc),app.component("flash-message",flashMessage),app.component("check-notification",checkNotification),app.mixin(baseMixins),app.mixin(pageDataMixins),trans.install(app,{ref:Vue.ref}),app.mount("#properties");
