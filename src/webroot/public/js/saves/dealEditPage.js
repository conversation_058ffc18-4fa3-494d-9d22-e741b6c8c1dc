var propItem={props:{prop:{type:Object,default:function(){return{pnSrc:[]}}},loading:{type:Boolean},lang:{type:String},tag:{type:String,default:""},cantClick:{type:Boolean,default:!1}},mounted(){},computed:{showBlur:function(){return!("sold"!=this.tag&&"fast"!=this.tag||!this.prop.login)}},methods:{openDetail:function(s){if(!this.cantClick){var a=/^RM/.test(s.id)?s.id:s._id;openPopup(`/1.5/prop/detail/inapp?id=${a}`,this.$_("RealMaster"))}},getPropDate:s=>"U"==s.status_en?s.sldd:s.mt?s.mt.substr(0,10):"",formatPrice:s=>"number"==typeof s?"$"+(s=Math.ceil(s)).toString().replace(/\B(?=(\d{3})+(?!\d))/g,","):s,popupSrcList(){window.bus.$emit("popup-match-list",this.prop)},cntSrc(s){if(!s)return 0;var a=0;return s.forEach((s=>{a+=s.v.length})),a},formateSrc(s){if(!s||"string"==typeof s)return s;var a=[];return s.forEach((s=>{s.nm?a.push(s.nm):a.push(s)})),a.join(",")},showLpInDelisted(s){return"Delisted"==s.saleTpTag_en?this.formatPrice(s.lp||s.lpr):this.formatPrice(s.sp||s.lp||s.lpr)}},template:'\n    <div class="prop" :class="loading?\'loading\':\'\'" @click="openDetail(prop)" data-sub="open detail" :data-id="prop._id">\n      <div class="detail">\n        <div class="addr one-line" :class=\'{blur:showBlur}\'>\n          <span v-if="prop.addr">{{prop.unt}} {{prop.addr}}</span>\n        </div>\n        <div class="prov">{{prop.city}}, {{prop.prov}}</div>\n        <div class="bdrms" :class=\'{blur:showBlur}\'>\n          <span v-if="prop.bdrms"><span class="fa fa-rmbed"></span> {{prop.bdrms}}{{prop.br_plus?\'+\'+prop.br_plus:\'\'}}</span>\n          <span v-if="prop.rmbthrm || prop.tbthrms || prop.bthrms"><span class="fa fa-rmbath"></span> {{prop.rmbthrm || prop.tbthrms || prop.bthrms}}</span>\n          <span v-if="prop.rmgr||prop.tgr||prop.gr"><span class="fa fa-rmcar"></span> {{prop.rmgr||prop.tgr||prop.gr}}</span>\n        </div>\n        <div v-if="showBlur" class=\'prov\'>{{$_(\'Please login to see this listing!\')}}</div>\n        <div v-else>\n          <div v-if="prop.lp == \'$0\'" class="price">{{$_(\'To Be Negotiated\')}}</div>\n          <div v-else-if="tag == \'loss\'" class="price">\n            <span class=\'black\'>-{{formatPrice(prop.lsp - prop.sp)}}</span>\n            <span v-if="prop.lspDifPct && (tag == \'loss\')" class="price-change">{{Math.round(prop.lspDifPct * 10000)/100}}%</span>\n          </div>\n          <div v-else class="price">\n            <span>{{showLpInDelisted(prop)}}</span>\n            <span v-if="prop.pc && (prop.status == \'A\') && prop.pcts" class="price-change">\n              <span :class="prop.pc > 0?\'plus\':\'mins\'">{{prop.pc>0?\'+\':\'-\'}}</span>\n              <span>{{formatPrice(Math.abs(prop.pc))}}</span>\n            </span>\n            <span v-if="prop.lstStr && prop.lst == \'Sc\'" class="price-change">{{prop.lstStr}}</span>\n            <span v-if="(prop.sp && prop.priceValStrRed) && (tag != \'loss\')" class="price-change sold">{{formatPrice(prop.lp || prop.lpr)}}</span>\n          </div>\n        </div>\n        <div v-if="prop.ohdate" class="oh"><span class="fa fa-rmhistory"></span>{{$_(\'Open House\')}}: {{prop.ohdate}}</div>\n      </div>\n      <div class="img" :class=\'{blur:showBlur}\'>\n        <img class="lazy" :src="prop.thumbUrl || \'/img/no-photo.png\'" onerror="this.src=\'/img/no-photo.png\'" referrerpolicy="same-origin" />\n        <div class="tag" :class="prop.tagColor">\n          <span v-if="prop.ytvid || prop.vurlcn"><span class="fa fa-youtube-play"></span></span>\n          <span v-if="prop.ltp == \'exlisting\'" class="ltp">{{$_(\'Exclusive\')}}</span>\n          <span v-if="prop.ltp == \'assignment\'" class="ltp">{{$_(\'Assignment\')}}</span>\n          <span v-if="prop.ltp == \'rent\'" class="ltp">{{$_(\'Rental\')}}</span>\n          <span v-if="prop.type" class="type">{{prop.type}}</span>\n          <span>{{prop.saleTpTag || prop.lstStr}}</span>\n        </div>\n        <div v-if="prop.sldDom >= 0" class="dom">{{prop.sldDom}} {{$_(\'days\')}}</div>\n        \x3c!--div v-else class="dom">{{getPropDate(prop)}}</div--\x3e\n      </div>\n      <div v-if="prop.pnSrc && prop.pnSrc.length>0" class=\'match\' @click.stop=\'popupSrcList(prop)\' data-sub="feeds pnsrc" :data-id="prop._id">\n        <span class="fa fa-radar"></span>\n        <span class="pnSrc" v-if="src = prop.pnSrc[0]">\n        <span class=\'name\'>{{src.transK}} </span>\n          {{formateSrc(src.v)}}\n        </span>\n        <span class="count">{{cntSrc(prop.pnSrc)}}</span>\n        <span class="fa fa-caret-down"></span>\n      </div>\n    </div>\n  '},dealsEdit={mounted(){window.bus;var s=this;s.$getTranslate(this),s.getPageData(s.datas,{},!0),window.bus.$on("pagedata-retrieved",(function(a){s.dispVar=Object.assign(s.dispVar,a)})),s.showBackdrop=vars.popup,s.getClaimInfo()},data:()=>({claimRoleMap:{listing:"Listing agent",coop:"Coop agent",dual:"Dual agent"},claimTypeRoleMap:{Sale:["Seller","Buyer"],Rent:["Landlord","Tenant"]},tokenKeyMap:{"RM Direct":"Firsthand","RM Referral":"Secondary",Self:"Self"},stakeholdersRole:[],waitEdit:-1,peopleTemplate:{role:"",nm:"",source:"",pcOfShr:100,claimSystemToken:0,_id:""},showRole:!1,claimTemplate:{side:"",m:"",people:[]},claimMap:{},claimList:[],role:"",prop:{},loading:!1,strings:{CHECKINFO:{key:"Please confirm all information is correct. After submitting, only the manager can assist with deletion."},CANCLE:{key:"Cancel"},COMFIRM:{key:"Confirm"},SOURCENOTMATCH:{key:"Source not match."},BADCONTACT:{key:"Bad Contacts info."}},datas:["sessionUser","isClaimAdmin","isRealGroup"],dispVar:{sessionUser:{},isClaimAdmin:!1,isRealGroup:!1},claimSystemTokenMap:{}}),methods:{checkInput:s=>s&&s.length?replaceJSContent(s):s,calcHeight(s){var a=document.querySelector("#"+s);a.style.height="inherit",a.style.height=`${a.scrollHeight}px`},getClaimInfo(){var s=this;fetchData("/propClaim/detail",{body:{id:vars.id}},(function(a,p){var e=a||p.e;s.loading=!1,e?window.bus.$emit("flash-message",e.toString()):(s.prop=p.prop,p.list&&(s.claimList=p.list),p.prop.claimSystemToken&&(s.stakeholdersRole=s.claimTypeRoleMap[p.prop.claimInfo.tp],s.claimSystemTokenMap=p.prop.claimSystemToken))}))},closeModals(s){var a=":cancel";s&&(a=JSON.stringify(s)),setTimeout((function(){window.rmCall(":ctx:"+a)}),1e3)},createClaimTemplate(s){var a="listing"==s?0:1,p=this.prop.claimInfo[s].maxPcOfShr,e=Object.assign({},this.peopleTemplate,{role:this.stakeholdersRole[a],pcOfShr:p,maxPcOfShr:p});return Object.assign({},this.claimTemplate,{side:s,people:[e]})},setClaimRole(s){this.role=s,this.claimMap={},"dual"==s?(this.claimMap.listing=this.createClaimTemplate("listing"),this.claimMap.coop=this.createClaimTemplate("coop")):this.claimMap[s]=this.createClaimTemplate(s);let a=document.getElementsByClassName("memo");for(var p=0;p<a.length;p++)a[p].style.height="21px"},editClaim(){let s=Object.assign({},this.claimMap),a=this;a.loading||a.canClaim&&(s.pid=a.prop._id,s.lst=a.prop.lst,s.sldd=a.prop.sldd,a.loading=!0,fetchData("/propClaim/edit",{body:s},(function(s,p){var e=s||p.e;a.loading=!1,e?window.bus.$emit("flash-message",e.toString()):(p.ok&&a.closeModals({claimed:1}),p.msg&&window.bus.$emit("flash-message",p.msg))})))},confirmClaim(){var s=this;this.loading||this.canClaim&&RMSrv.dialogConfirm(this.$_(this.strings.CHECKINFO.key),(function(a){a+""=="2"&&s.editClaim()}),"",[this.$_(this.strings.CANCLE.key),this.$_(this.strings.COMFIRM.key)])},showClientFn(s,a,p){var e=this;if((e.dispVar.isClaimAdmin||e.dispVar.isRealGroup)&&0!=s.maxPcOfShr){var t={hide:!1,title:this.$_("Contacts","contactCrm")};RMSrv.getPageContent("/1.5/crm?noBar=1&isPopup=1&owner=1&claim=1","#callBackString",t,(function(t){if(":cancel"!=t)try{var r=JSON.parse(t);let{source:i,_id:n,nm:o}=r;if(!n||!o||!i)return window.bus.$emit("flash-message",e.$_(e.strings.BADCONTACT.key));let l=e.tokenKeyMap[i];if(!l)return window.bus.$emit("flash-message",e.$_(e.strings.SOURCENOTMATCH.key));s._id=n,s.nm=o,s.source=i,s.claimSystemToken=e.claimSystemTokenMap[l],s.sourceKey=l,e.$set(e.claimMap[p].people,a,s),e.$forceUpdate()}catch(s){console.error(s)}}))}},checkPcOfShrRange(s){s.pcOfShr&&(Number.isNaN(s.pcOfShr)&&(s.pcOfShr="0"),s.pcOfShr=parseInt(s.pcOfShr),s.pcOfShr>s.maxPcOfShr&&(s.pcOfShr=s.maxPcOfShr),s.pcOfShr<0&&(s.pcOfShr="0"))}},computed:{canClaim:function(){let s=this.claimMap,a=!0;if(0==Object.keys(s).length)return!1;for(let p in s)s[p].people.forEach((s=>{0==s.maxPcOfShr?(s.claimSystemToken=0,s.nm="",s.pcOfShr=0,s.source="",s._id="",s.sourceKey="",a=!1):a=!0,s.pcOfShr&&s._id&&s.source&&s.sourceKey&&s.claimSystemToken||(a=!1)}));return a}}};initUrlVars();var app=Vue.createApp(dealsEdit);app.component("flash-message",flashMessage),app.component("prop-item",propItem),app.mixin(pageDataMixins),trans.install(app,{ref:Vue.ref}),app.mount("#dealsEdit");
