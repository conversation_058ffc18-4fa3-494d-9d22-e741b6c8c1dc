"use strict";$((function(){var t,e;return t=function(t){var e,a,r,i;return i=vars.strOptTip,a=vars.strLater,r=vars.strSeemore,e=function(t){if("https://www.realmaster.ca/membership",t+""=="2")return RMSrv.showInBrowser("https://www.realmaster.ca/membership")},RMSrv.dialogConfirm(i,e,"VIP",[a,r])},(e=function(e){var a,r,i,s;for(a="",r=0,i=e.length;r<i;r++)s=e[r],a+="<li class='list-item table-view-cell' data-name='".concat(s.folder,"' data-useable='").concat(s.isUseable,"'>"),a+="<img src='".concat(s.demoimg,"'></img>"),a+="<div class='wrapper'>",a+="<div class='nm'>".concat(s.name,"</div>"),a+="<div class='desc'>".concat(s.description,"</div>"),a+="</div>",a+="</li>";return $("#tempList").append(a),$("#tempList .table-view-cell").bind("click",(function(e){var a,r,i;return(a=$(this)).data("useable")+""!="true"?t(vars.lang):(r=a.data("name"),i="/1.5/htmltoimg/dottemplate?nm=".concat(r,"&id=").concat(vars.id),RMSrv.openTBrowser(i))}))})(vars.templateList),$("#reloadTemplates").on("click",(function(t){var a;return $(t.target),a={},$.post("/1.5/htmltoimg/reload",a,(function(t){if(t.ok)return $("#tempList").empty(),e(t.l)}))}))}));
