var propItem={props:{prop:{type:Object},rcmdHeight:{type:Number},index:{type:Number},formPage:{type:String,default:""},showFav:{type:Boolean,default:!0},create:{type:Boolean,default:!1},dispVar:{default:function(){return{lang:"zh-cn",isNoteAdmin:!1,isRealGroup:!1}}}},data:()=>({noteMemo:""}),mounted(){if(!(bus=window.bus))return console.error("global bus required!")},computed:{computedSaletp:function(){let s=this.prop.saletp;return s?Array.isArray(s)?s.join(" "):s:this.prop.lpunt},computedBgImg:function(){return this.prop.thumbUrl||"/img/noPic.png"},computedShowInfo(){return"similar"==this.formPage&&(!!this.prop.dist||(!!(this.prop.sqft||this.prop.sqft1&&this.prop.sqft2||this.prop.rmSqft)||(!!this.prop.front_ft||!(!this.prop.weight||!this.dispVar.isAdmin))))}},watch:{prop:{handler(s){this.prop.noteMemo&&("noTag"==this.prop.noteMemo[0].tag?this.noteMemo=this.prop.noteMemo[0].m:this.noteMemo=`[${this.prop.noteMemo[0].tag}]${this.prop.noteMemo[0].m}`)},immediate:!0,deep:!0}},methods:{showAgentWesite(s){var p=`/1.5/wesite/${s._id}?inFrame=1`;RMSrv.openTBrowser(p)},getStatus:function(s){return"U"==s.status?"sold":"sale"},openDetail:function(s){var p="/1.5/prop/detail/inapp?id="+(/^RM/.test(s.id)?s.id:s._id);this.formPage&&(p+=`&formPage=${this.formPage}`),openPopup(p,this.$_("RealMaster"))},openShowing:s=>window.bus.$emit("open-showing",s),getPropDate:s=>"U"==s.status_en?s.sldd:s.mt.substr(0,10),soldOrLeased:function(s){return"Sold"==s.saleTpTag_en||["Sld","Lsd","Pnd","Cld"].includes(s.lst)},saletpIsSale:function(s){return!s.saletp_en||!!/sale/.test(s.saletp_en.toString().toLowerCase())},isTop:function(s){return"A"==s.status&&new Date(s.topTs)>=new Date},propSid:function(s){return s.isProj?"":s.sid?s.sid:/^RM/.test(s.id)?s.id:s._id?s._id.substr(3):""},rmgrStr:function(s){return s.rmgr||s.tgr||s.gr},rmbdrmStr:function(s){if(s.rmbdrm)return s.rmbdrm;let p=s.bdrms||s.tbdrms;return p+=s.br_plus?"+ "+s.br_plus:"",p},rmbthrmStr:function(s){return s.rmbthrm||s.tbthrms||s.bthrms},formatPrice:s=>"number"==typeof s?"$"+s.toString().replace(/\B(?=(\d{3})+(?!\d))/g,","):s,isInGrp(s){return(this.prop.favGrp||[]).indexOf(parseInt(s))>-1},toggleFav(s){window.bus.$emit("toggleFav",s)},computedVideoUrl:function(){return this.dispVar.isCip?this.prop.vurlcn:this.prop.ytvid?"https://www.youtube.com/watch?v="+this.prop.ytvid:null},isPropUnavailable:function(){return(!this.prop.id||"RM"!=this.prop.id.substr(0,2))&&"A"!==this.prop.status_en},calcShowingName:s=>s.showing?s.showing.dt:"",remove(){window.bus.$emit("remove",this.prop)},clearNoteInfo(){delete this.prop.noteMemo,delete this.prop.noteId,this.noteMemo="",this.prop.noteCount--},goToEditNote(){if(!this.prop.uaddr||"undefined"==this.prop.uaddr)return bus.$emit("flash-message",this.$_(NOTE_TIPS.NOUADDR));if(!this.prop._id||"undefined"==this.prop._id)return bus.$emit("flash-message",this.$_(NOTE_TIPS.NOPROPID));let s=this;fetchData("/1.5/notes/findListingByID",{body:{id:this.prop._id}},((p,t)=>{if(!t.ok)return bus.$emit("flash-message",this.$_(NOTE_TIPS.NOPROPID));let r=t.result.uaddr;if(!r||"undefined"==r)return bus.$emit("flash-message",this.$_(NOTE_TIPS.NOUADDR));let e=`/1.5/notes/editNotes?propId=${this.prop._id}&uaddr=${encodeURIComponent(r)}`;this.prop.noteId&&(e+=`&id=${this.prop.noteId}`),s.dispVar.isRealGroup||s.dispVar.isNoteAdmin||!t.result.unt||(e+=`&unt=${encodeURIComponent(t.result.unt)}`);let n={toolbar:!1};s.dispVar&&s.dispVar.isApp&&RMSrv.getPageContent(e,"#callBackString",n,(function(p){if(":cancel"!=p)try{let t=JSON.parse(p);if("delete"==t.type)return void s.clearNoteInfo();let r=t.note;s.prop.noteMemo||(s.prop.noteCount+=1),r.memo?s.prop.noteMemo=r.memo:s.prop.noteMemo=[{tag:"noTag",m:" "}],s.prop.noteId=r._id}catch(s){console.error(s)}}))}))},goToNoteList(){let s=this;if(!this.prop.uaddr)return bus.$emit("flash-message",this.$_(NOTE_TIPS.NOUADDR));if(!this.prop._id)return bus.$emit("flash-message",this.$_(NOTE_TIPS.NOPROPID));let p=`/1.5/notes?propId=${this.prop._id}&uaddr=${encodeURIComponent(this.prop.uaddr)}&addr=${encodeURIComponent(this.prop.addr)}&lat=${this.prop.lat}&lng=${this.prop.lng}&isBuilding=${this.prop.isBuilding}`,t={toolbar:!1};s.dispVar&&s.dispVar.isApp&&RMSrv.getPageContent(p,"#callBackString",t,(function(p){if(":cancel"!=p)try{let t=JSON.parse(p).noteDetail;t&&s.prop.noteMemo?s.prop.noteMemo=t.memo?t.memo:[{tag:"noTag",m:" "}]:t&&!s.prop.noteMemo?(s.prop.noteMemo=t.memo?t.memo:[{tag:"noTag",m:" "}],s.prop.noteId=t._id,s.prop.noteCount+=1):!t&&s.prop.noteMemo&&s.clearNoteInfo()}catch(s){console.error(s)}}))},parseSqft:s=>/\-/.test(s)?s:("number"==typeof s&&(s=""+s),/\./.test(s)?s.split(".")[0]:s),dotdate(s,p,t="."){if(!s)return"";"number"==typeof s&&(s=(s+="").slice(0,4)+"/"+s.slice(4,6)+"/"+s.slice(6,8)),"string"!=typeof s||/\d+Z/.test(s)||(s+=" EST");var r=p?"年":t,e=p?"月":t,n=p?"日":"";if(/^2\d{3}-\d{1,2}-\d{1,2}/.test(s)&&!/\d+Z/.test(s)){var a=s.split(" ")[0].split("-");return a[0]+r+a[1]+e+a[2]+n}var i=new Date(s);return!i||isNaN(i.getTime())?s:i.getFullYear()+r+(i.getMonth()+1)+e+i.getDate()+n}},template:'\n<div class="prop" @click="openDetail(prop)">\n  <div class="img" :style="{ \'height\':rcmdHeight+\'px\'}">\n    <img :src="computedBgImg"\n      style="background-image: url(\'/img/noPic.png\');background-size: 100% 100%;width:100%;"\n      :style="{\'height\':rcmdHeight+\'px\'}"\n      @error="e => { e.target.src = \'/img/noPic.png\'}"\n      referrerpolicy="same-origin">\n    <div class="on-img-top">\n      <span class="top pull-left" v-if="prop.isTop && prop.marketRmProp">{{$_(\'TOP\')}}</span>\n      <span class="tp" v-show="prop.type">{{prop.type}}</span>\n      <span class="pull-right fav fa" :class="{\'fa-heart-o\':!prop.fav, \'fa-heart\':prop.fav}"\n        @click.stop.prevent="toggleFav(prop)" v-show="!prop.login && !prop.isProj" v-if=\'showFav\'></span>\n    </div>\n  </div>\n  <div class="price" :class="{\'blur\':prop.login}">\n    <span class="val" v-if=\'prop.priceValStrRed\'>{{prop.priceValStrRed}}</span>\n    <span class="val" v-else-if=\'prop.askingPriceStr\'>{{prop.askingPriceStr}}</span>\n    <span class="desc" v-if="prop.priceValStrRedDesc" :class="{\'through\':soldOrLeased}">{{soldOrLeased?prop.askingPriceStr:prop.priceValStrRedDesc}}</span>\n    <span class="desc" v-if="prop.lstStr && (prop.tagColor != \'red\')">({{prop.lstStr}})</span>\n    <div class="displayFlex maxWidth">\n      <span class="stp" v-if="prop.saleTpTag && prop.saleTpTag_en !=\'Delisted\'" :class="prop.tagColor">\n        <span>{{prop.saleTpTag}}</span>\n        <span v-if="(prop.tagColor == \'red\'|| prop.tagColor == \'green\') && (prop.spcts||prop.mt||prop.ts) && !prop.marketRmProp">&nbsp;{{dotdate(prop.spcts||prop.mt||prop.ts)}}</span>\n      </span>\n      <span class="stp green" v-show="[\'exlisting\',\'assignment\',\'rent\'].indexOf(prop.ltp) > -1 && !prop.marketRmProp">\n        <span v-show="prop.ltp == \'exlisting\'">{{$_(\'Exclusive\')}}</span>\n        <span v-show="prop.ltp == \'assignment\'">{{$_(\'Assignment\')}}</span>\n        <span v-show="prop.ltp == \'rent\' && !prop.cmstn">{{$_(\'Landlord Rental\')}}</span>\n        <span v-show="prop.ltp == \'rent\' && prop.cmstn">{{$_(\'Exclusive Rental\')}}</span>\n      </span>\n      <span class="dist" v-show="prop.dist && (formPage != \'similar\')" >{{prop.dist}}</span>\n      <span class="stp" v-show="prop.isAgreementVerified && !prop.marketRmProp">\n        <span class="fa fa-check"></span>\n      </span>\n      <span class="stp vid" v-show="computedVideoUrl()">\n        <span class="fa fa-youtube-play"></span>{{$_(\'Video\')}}\n      </span>\n      <span class="stp oh" v-if="prop.hasOh">{{$_(\'Open House\')}}</span>\n      <div class="stp viewTrusted" v-if="prop.marketRmProp"><span class="fa fa-check-circle trustedCir"></span>{{$_(prop.marketRmProp)}}</div>\n    </div>\n  </div>\n  <div class="addr one-line" v-if="!prop.login"><span v-show="prop.daddr !== \'N\'"><span v-show="prop.addr">{{prop.unt}}\n        {{prop.addr}},</span> {{prop.city}}, {{prop.prov}}</span><span v-show="prop.daddr == \'N\'"><span\n        v-show="prop.cmty">{{prop.cmty}},</span>{{prop.city}}, {{prop.prov}}</span>\n  </div>\n  <div class="addr one-line" v-if="prop.login"><span v-show="prop.daddr == \'Y\'">{{prop.addr}}, {{prop.city}}</span><span\n      v-show="prop.daddr !== \'Y\'">{{prop.city}}, {{prop.prov}}</span></div>\n  <div class="bdrms one-line" v-if="!prop.login">\n    <span class="rmbed" v-show="prop.rmbdrm != null || prop.bdrms != null || prop.tbdrms != null">\n      <span class="fa fa-rmbed">\n      </span>\n      <b class="num">{{rmbdrmStr(prop)}}</b>\n      <span v-if="prop.bdrms_diff!= null" class=\'diff\'>\n        <span :class="{red: prop.bdrms_diff < 0, green:prop.bdrms_diff > 0,blue:prop.bdrms_diff == 0}">\n          <span class="nopadding" :class="{\'fa-caret-up\': prop.bdrms_diff > 0, \'fa-caret-down\':prop.bdrms_diff < 0,\'fa-check-circle\':prop.bdrms_diff == 0 }"></span>\n          <span v-show=\'prop.bdrms_diff != 0\'>&nbsp;{{Math.abs(prop.bdrms_diff)}}</span>\n        </span>\n      </span>\n    </span>\n    <span v-show="prop.rmbthrm != null || prop.tbthrms != null || prop.bthrms != null">\n      <span class="fa fa-rmbath">\n      </span>\n      <b class="num">{{rmbthrmStr(prop)}}</b>\n      <span v-if="prop.bthrms_diff!= null" class=\'diff\'>\n        <span :class="{red: prop.bthrms_diff < 0, green:prop.bthrms_diff > 0,blue:prop.bthrms_diff == 0}">\n          <span class="nopadding" :class="{\'fa-caret-up\': prop.bthrms_diff > 0, \'fa-caret-down\':prop.bthrms_diff < 0,\'fa-check-circle\':prop.bthrms_diff == 0}"></span>\n          <span v-show=\'prop.bthrms_diff != 0\'>&nbsp;{{Math.abs(prop.bthrms_diff)}}</span>\n        </span>\n      </span>\n    </span>\n    <span v-show="prop.rmgr != null || prop.gr != null || prop.tgr != null">\n      <span class="fa fa-rmcar"></span>\n      <b class="num">{{rmgrStr(prop)}}</b>\n      <span v-if="prop.gr_diff!= null" class=\'diff\'>\n        <span :class="{red: prop.gr_diff < 0, green:prop.gr_diff > 0,blue:prop.gr_diff == 0}">\n          <span class="nopadding" :class="{\'fa-caret-up\': prop.gr_diff > 0, \'fa-caret-down\':prop.gr_diff < 0,\'fa-check-circle\':prop.gr_diff == 0}"></span>\n          <span v-show=\'prop.gr_diff != 0\'>&nbsp;{{Math.abs(prop.gr_diff)}}</span>\n        </span>\n      </span>\n    </span>\n    <span v-if="prop.marketRmProp && prop.sqft && (formPage == \'truthAssignList\')">\n      <span class="fa fa-area"></span>\n      <b class="num">{{prop.sqft}}</b>\n    </span>\n    <span v-show="prop.isAgreementVerified && !prop.marketRmProp">\n      <span class="fa fa-check-circle"></span>\n      <b class="num">{{$_(\'Verified\')}}</b>\n    </span>\n    <span v-if="(prop.private && (formPage == \'truthAssignList\'))">\n      <span class="num" style="color:#e03131">{{$_(\'Hide to the public\')}}</span>\n    </span>\n    <span class="sid">{{propSid(prop)}}</span>\n  </div>\n  <div class="bdrms" v-if="prop.login">{{$_(\'Please login to see this listing\')}}</div>\n  <div class=\'showing-info\' v-if="formPage == \'buylist\'">\n    <span class="left">\n      <span v-show=\'dispVar.isRealtor && create\'>\n        <span v-if=\'prop.clnt && prop.clnt.nm\'>{{prop.clnt.nm}}</span>\n      </span>\n      <span v-if=\'!create && prop.realtor && prop.realtor.fnm\' class=\'agent\' @click.stop=\'showAgentWesite(prop.realtor)\'>\n        <img :src="prop.realtor.avt || prop.realtor.wxavt || \'/img/logo.png\'" @error="prop.realtor.avt = \'/img/logo.png\'" referrerpolicy="same-origin">\n        <span class="d">{{prop.realtor.fnm}}</span>\n      </span>\n      <span class="showing-name" @click.stop=\'openShowing(prop)\' :class=\'{"pull-right":!create}\'>\n        <span class="link">{{$_(\'Showing\')}}</span>\n        <span>({{calcShowingName(prop)}})</span>\n      </span>\n    </span>\n    <span class="right" v-show=\'create\'>\n      <span class="pull-right delbtns btn btn-nooutline" v-show="prop.del">\n        <span class="cancle pull-right btn btn-nooutline" @click.stop.prevent="prop.del = false" >{{ $_(\'Cancel\')}}</span>\n        <span class="delete pull-right btn btn-negative" @click.stop.prevent="remove()" >{{ $_(\'Delete\')}}</span>\n      </span>\n      <span class="pull-right sprite16-18 sprite16-4-8" v-show="!prop.del" @click.stop.prevent="prop.del = true"></span>\n    </span>\n  </div>\n  <div v-if=\'computedShowInfo\' class=\'size bdrms\'>\n    <span v-if="prop.dist">{{$_(\'Distance\')}}\n      <span class=\'val pull-right\' >{{prop.dist}}{{$_(\'m\')}}</span>\n    </span>\n    <div v-if="prop.sqft || (prop.sqft1&&prop.sqft2) || prop.rmSqft">\n      {{$_(\'Size\')}}\n      <span class=\'val pull-right\'>\n        <span v-if="prop.sqft">{{parseSqft(prop.sqft)}}</span>\n        <span v-else-if="prop.sqft1&&prop.sqft2">{{prop.sqft1}}-{{prop.sqft2}}</span>\n        <span v-if="prop.rmSqft && dispVar.isLoggedIn">\n          <span v-if="prop.sqft && /-/.test(prop.sqft) && (prop.sqft!=prop.rmSqft)">&nbsp;({{parseSqft(prop.rmSqft)}})</span>\n          <span v-if=\'!prop.sqft\'>{{parseSqft(prop.rmSqft)}} ({{$_(\'Estimated\')}})</span>\n        </span>\n        <span>&nbsp;{{$_(\'ft&sup2;\')}}</span>\n        <span v-if="prop.sqft_diff!= null" class=\'diff\'>\n          <span :class="{red: prop.sqft_diff < 0, green:prop.sqft_diff > 0,blue:prop.sqft_diff == 0}">\n            <span class="nopadding" :class="{\'fa-caret-up\': prop.sqft_diff > 0, \'fa-caret-down\':prop.sqft_diff < 0,\'fa-check-circle\':prop.sqft_diff == 0}"></span>\n            <span v-if=\'prop.sqft_diff_abs && prop.sqft_diff_abs != 0\'>&nbsp;{{prop.sqft_diff_abs}} {{$_(\'ft&sup2;\')}}</span>\n          </span>\n        </span>\n      </span>\n    </div>\n    <div v-if="prop.front_ft">\n      {{$_(\'Lot\')}}\n      <span class=\'val pull-right\'>\n        {{prop.front_ft}}\n        <span v-if="prop.front_ft_diff!= null">(\n          <span :class="{red: prop.front_ft_diff < 0, green:prop.front_ft_diff > 0,blue:prop.front_ft_diff == 0}">\n            <span class="nopadding" :class="{\'fa-caret-up\': prop.front_ft_diff > 0, \'fa-caret-down\':prop.front_ft_diff < 0,\'fa-check-circle\':prop.front_ft_diff == 0}"></span>\n            <span v-if=\'prop.front_ft_diff != 0\'>&nbsp;{{Math.abs(prop.front_ft_diff)}}</span>\n          </span>)\n        </span>\n        &nbsp;* {{prop.depth}}\n        <span v-if="prop.depth_diff!= null">(\n          <span :class="{red: prop.depth_diff < 0, green:prop.depth_diff > 0,blue:prop.depth_diff == 0}">\n            <span class="nopadding" :class="{\'fa-caret-up\': prop.depth_diff > 0, \'fa-caret-down\':prop.depth_diff < 0,\'fa-check-circle\':prop.depth_diff == 0}"></span>\n            <span v-if=\'prop.depth_diff != 0\'>&nbsp;{{Math.abs(prop.depth_diff)}}</span>\n          </span>)\n        </span>\n        {{prop.lotsz_code}} {{prop.irreg}}\n      </span>\n    </div>\n    <div v-if="prop.weight && dispVar.isDevGroup">\n      {{$_(\'Weight\')}}\n      <span class=\'val pull-right\' v-if=\'prop.weight.wTotal\'>\n        {{prop.weight.wTotal}}\n      </span>\n    </div>\n  </div>\n  <div class="bdrms" v-if="prop.login">{{$_(\'Please login to see this listing\')}}</div>\n  <div class="noteInsave" v-if="prop.noteMemo || prop.noteCount>=0">\n    <div class="noteInfo" :style="(dispVar.isNoteAdmin || dispVar.isRealGroup)? \'justify-content:normal\':\'justify-content: space-between\'">\n      <input v-model="noteMemo" class="noteMemo" readonly="readonly" :placeholder="$_(\'Add a note\')" @click.stop="goToEditNote()"></input>\n      <span v-if="dispVar.isNoteAdmin || dispVar.isRealGroup" class="fa fa-rmmemo noteIcon" @click.stop="goToNoteList()"></span>\n      <span v-else class="fa fa-rmmemo noteIcon" :style="prop.noteCount? \'color: #428bca\':\'color:#777\'" @click.stop="goToEditNote()"></span>\n      <span v-if="dispVar.isNoteAdmin || dispVar.isRealGroup" @click.stop="goToNoteList()">{{prop.noteMemo?1:0}}/{{prop.noteCount}}</span>\n    </div>\n  </div>\n</div>\n  '},propFavActions={props:{dispVar:{type:Object,default:function(){return{lang:"zh-cn",allowedEditGrpName:!1,isLoggedIn:!1}}}},components:{},data:()=>({grp:null,grpName:"",cntr:1,MAX_GROUPS:5,grps:[],isArchived:!1,grpsSelect:!1,grpsEdit:!1,prop:{fav:!1,favGrp:[]},inputGrpName:"",grpsOptions:!1,mode:"new",showCrm:!1,clnt:null,strings:{clearTip:{key:"Are you sure you want to clear this folder?",ctx:""},deleteTip:{key:"Are you sure you want to delete this folder?",ctx:""},cancel:{key:"Cancel",ctx:""},delete:{key:"Delete",ctx:""},clear:{key:"Clear",ctx:""}},folderSort:"time"}),mounted(){if(!(p=window.bus))return console.error("global bus required!");var s=this,p=window.bus;p.$on("toggleFav",(function(p){s.dispVar.isLoggedIn?(s.grpsSelect=!0,s.prop=p,s.grpsEdit=!1,s.grps.length||s.getGroups({mode:"get"}),s.toggleGrpSelect()):location.href="/1.5/user/login"})),p.$on("editGroups",(function({grp:p,grpInfo:t}){s.grp=p,s.grpName=t.v,t.clnt&&t.clnt._id&&(s.clnt=t.clnt),s.grpsOptions=!s.grpsOptions})),p.$on("choosed-crm",(function(p){s.showCrm=!1,s.clnt=p,s.inputGrpName=s.inputGrpName.length?s.inputGrpName:p.nm})),p.$on("close-crm",(function(p){s.showCrm=!1})),p.$on("add-fav",(function(p){p.grps&&(s.grps=p.grps),s.addFav(p)})),p.$on("is-archived",(function(p){s.isArchived=p})),s.sortByMtWithoutDefault=window.sortByMtWithoutDefault},methods:{clientName:function(){return this.clnt&&this.clnt.nm?this.clnt.nm:""},showClientFn(){if(!this.dispVar.isVipRealtor){return(this.confirmVip||this.$parent.confirmVip)(this.dispVar.lang)}if(!this.clnt||!this.clnt.nm){this.showCrm=!0;var s={hide:!1,title:this.$_("Contacts","contactCrm")},p=RMSrv.appendDomain("/1.5/crm?noBar=1&isPopup=1&owner=1");RMSrv.getPageContent(p,"#callBackString",s,(function(s){if(":cancel"!=s)try{var p=JSON.parse(s);window.bus.$emit("choosed-crm",p)}catch(s){console.error(s)}else console.log("canceled")}))}},isInGrp(s){return(this.prop.favGrp||[]).indexOf(parseInt(s))>-1},editGrpName(){this.inputGrpName=this.grpName,this.addNewGrp(),this.mode="edit"},confirmDelOrClear(s){let p=this,t=`${s}Tip`,r=this.strings[s],e=this.strings[t],n=this.$_(e.key,e.ctx);var a=this.$_(this.strings.cancel.key,this.strings.cancel.ctx),i=this.$_(r.key,r.ctx);return RMSrv.dialogConfirm(n,(function(t){t+""=="2"&&("clear"==s?p.clearGrpFavs():"delete"==s&&p.removeGrp())}),"",[a,i])},clearGrpFavs(s={}){null!=this.grp&&this.getGroups({mode:"clear",grp:this.grp,noEmit:s.noEmit})},removeGrp(){this.getGroups({mode:"delete",grp:this.grp})},addNewGrp(){var s=this;if(!this.dispVar.allowedEditGrpName){return(s.confirmVip||s.$parent.confirmVip)(s.dispVar.lang)}this.grpsEdit=!0,this.grpsSelect=!1,this.grpsOptions=!1},reset(){this.grpsEdit=!1,this.grpsSelect=!1,this.grpsOptions=!1,window.bus.$emit("reset")},addGrpName(s){if(this.inputGrpName){var p={nm:this.inputGrpName};this.clnt&&this.clnt._id&&(p.clnt=this.clnt._id,p.cNm=this.clnt.nm),"edit"==this.mode?(p.mode="put",p.grp=this.grp):p.mode="set",this.getGroups(p)}},toggleGrpSelect(){this.grpsSelect=!0,this.grpsEdit=!1},parseGrps(s={}){var p=[];for(let t in s)"cntr"!==t&&p.push({idx:t,val:s[t],mt:s[t]?s[t].mt:-1});return this.sortByMtWithoutDefault(p)},getGroups(s){var p=this;p.reset(),trackEventOnGoogle("saves","properties",s.mode+"Group");fetchData("/1.5/props/propGroups",{body:s},(function(t,r){return t||r.err?RMSrv.dialogAlert(t||r.err):(p.inputGrpName="",r.grps&&(p.grps=p.parseGrps(r.grps),window.bus.$emit("prop-fav-retrieved",r.grps)),"set"!=s.mode&&"get"!=s.mode||p.toggleGrpSelect(),"set"==s.mode?p.selectGrp(p.grps[1]):((r.grps||"clear"==s.mode||"delete"==s.mode)&&(s.grps=r.grps,window.bus.$emit("afterFavAction",s)),void(p.clnt=null)))}))},selectGrp(s){var p=parseInt(s.idx)||0;this.addFav({prop:this.prop,grp:p})},addFav(s){var p=this,t="favour",r=s.prop;if(r.fav&&this.isInGrp(s.grp)&&(t="unfavor"),!p.loading){trackEventOnGoogle("saves","properties",t);var e={grp:s.grp,id:r._id,topTs:r.topTs,mode:t,src:r.src};fetchData("/1.5/props/favProp",{body:e},(function(e,n){if(e||n.err)return RMSrv.dialogAlert(e||n.err);var a="favour"==t;p.prop.favGrp=p.prop.favGrp||[],"favour"==t?(p.prop.favGrp||(p.prop.favGrp=[]),p.prop.favGrp.push(s.grp),p.grps.forEach((p=>{p&&p.idx==s.grp&&(p.mt=new Date)})),p.grps=p.sortByMtWithoutDefault(p.grps)):(p.prop.favGrp.splice(p.prop.favGrp.indexOf(s.grp),1),a=p.prop.favGrp.length),r.fav=a,p.grpsSelect=!1,window.bus.$emit("prop-fav-changed",{prop:r,grps:p.grps}),window.bus.$emit("flash-message",n.msg)}))}},archive(s){var p=this;if(!p.loading){trackEventOnGoogle("archive","folder");var t={grp:this.grp,flag:s};fetchData("/1.5/propFav/archive",{body:t},(function(s,t){if(s||t.e)return RMSrv.dialogAlert(s||t.e);p.isArchived=!p.isArchived,window.bus.$emit("flash-message",t.msg),p.reset(),window.bus.$emit("refresh-archive-folder",{grp:p.grp,isArchived:p.isArchived})}))}},resetClnt(){this.clnt=null,this.inputGrpName=this.inputGrpName.split(" ")[0]},gotoSaves(){var s="/1.5/saves/properties?grp=0";RMSrv.closeAndRedirectRoot?RMSrv.closeAndRedirectRoot(RMSrv.appendDomain(s)):window.location=s},sortFolder(s){this.grps=this.sortByMtWithoutDefault(this.grps,s),this.folderSort=s}},template:'\n  <div>\n  <div class="backdrop" :class=\'{show:(grpsOptions || grpsSelect ||grpsEdit),lowIndex:showCrm}\' @click=\'reset()\'></div>\n  <div class="modal modal-60pc" id="grpSelect" :class=\'{active:grpsSelect}\'>\n    <header class="bar bar-nav">{{$_(\'Save to\')}}\n      <span class="pull-right link" style="padding-right:0" @click="gotoSaves()">{{$_(\'All Saved\')}}</span>\n    </header>\n    <div class="folderSort" style="top: 44px;">\n      <span id="createBtn" @click="addNewGrp()">\n        <span class="fa icon icon-plus"></span>\n        <span>{{$_(\'Create New Folder\')}}</span>\n      </span>\n      <span class="sort">\n        <span :class="{select:folderSort == \'time\'}" @click="sortFolder(\'time\')">{{$_(\'Time\')}}<span class="fa fa-long-arrow-down"></span></span>\n        <span :class="{select:folderSort == \'name\'}" @click="sortFolder(\'name\')">{{$_(\'Name\')}}<span class="fa fa-long-arrow-up"></span></span>\n      </span>\n    </div>\n    <div class="content" style="padding-top: 97px;">\n      <ul class="table-view">\n        <li class="table-view-cell" v-for="g in grps" @click="selectGrp(g)">\n          <span class="fa fa-star" v-show="g.idx == \'0\'"></span>\n          <span class="fa" v-show="g.idx !== \'0\'"></span>\n          <span class="group-name">{{g.val.v}}</span>\n          <span class="pull-right fa" :class="{\'fa-heart-o\':!isInGrp(g.idx), \'fa-heart\':isInGrp(g.idx)}"></span>\n        </li>\n      </ul>\n    </div>\n  </div>\n  <div class="modal" id="grpEdit" :class="{active:grpsEdit,lowIndex:showCrm}">\n      <div class=\'bar bar-nav\'>{{$_(\'Edit Folder\')}}\n      </div>\n      <div class="addClient" @click.stop="showClientFn()" v-if="dispVar.isRealtor && mode != \'new\'"><span\n          class="sprite16-18 sprite16-3-6"></span>\n          <span class="editClientName">{{clientName() ||  $_("Choose a client")}}\n            <span class="lang" v-if="clientName() && clnt.lang">({{ $_(clnt.lang,\'lang\')}})</span>\n            <span class="lang" v-if="clientName() && !clnt.lang">(En)</span>\n          </span>\n        <span class="fa fa-rmclose" style="color:#aaa;padding:10px" v-show="clnt && clnt.nm " @click.stop="resetClnt()"></span>\n      </div>\n      <div class="bar bar-standard bar-header-secondary"><input v-model="inputGrpName" :placeholder="$_(\'Input folder name\')"/></div>\n      <div class="btn-cell bar bar-tab">\n      <a @click="addGrpName()" class="btn btn-tab btn-half btn-sharp btn-fill btn-negative">{{$_(\'Save\')}}</a>\n      <a @click="reset()" class="btn btn-tab btn-half btn-sharp btn-fill length">{{$_(\'Cancel\')}}</a>\n    </div>\n  </div>\n  <div class="modal" id="grpOpts" :class="{active:grpsOptions}">\n      <div class="content">\n          <ul class="table-view">\n              <li class="table-view-cell" @click="archive(true)" v-if=\'!isArchived && (grp != 0)\'><span>{{$_(\'Archive Folder\')}}</span></li>\n              <li class="table-view-cell" @click="archive(false)" v-if=\'isArchived && (grp != 0)\'><span>{{$_(\'Unarchive Folder\')}}</span></li>\n              <li class="table-view-cell" @click="editGrpName()"><span>{{$_(\'Edit Folder Name\')}}</span></li>\n              <li class="table-view-cell" @click="confirmDelOrClear(\'clear\')"><span>{{$_(\'Clear Folder\')}}</span></li>\n              <li class="table-view-cell" @click="confirmDelOrClear(\'delete\')"><span>{{$_(\'Delete Folder\')}}</span></li>\n          </ul>\n      </div>\n  </div>\n  <div style="display:none"><span v-for="(v,k) of strings">{{ $_(v.key, v.ctx)}}</span></div>\n</div>\n  '},trustedAssigm={data:()=>({propList:[],rcmdHeight:0,page:0,scrollElement:"",waiting:!1,hasMoreProps:!1,loading:!1,LIMIT:20,dispVar:{isLoggedIn:!1,lang:"zh-cn",isCip:!1,userCity:{o:"Toronto",n:"多伦多"}},datas:["lang","isCip","allowedEditGrpName","isLoggedIn","userCity"],sortCondition:"sortOrder-desc",selCity:"City",cityList:[],selStyle:"Style",ptype2s:[],showStyle:!1,showCity:!1}),mounted(){let s=this;if(!(bus=window.bus))return console.error("global bus required!");if(s.$getTranslate(s),s.getPtype2sAndCity(),s.getPageData(s.datas,{},!0),bus.$on("pagedata-retrieved",(function(p){s.dispVar=Object.assign(s.dispVar,p)})),s.getList(),s.rcmdHeight=parseInt(window.innerWidth/1.6),s.scrollElement=document.getElementById("trustedAssigm"),s.scrollElement.addEventListener("scroll",s.listScrolled),vars.id){var p="/1.5/prop/detail/inapp?id="+vars.id;setTimeout((()=>{openPopup(p,s.$_("RealMaster"))}),100)}},methods:{listScrolled:function(){var s=this;!s.waiting&&s.hasMoreProps&&(s.waiting=!0,s.loading=!0,setTimeout((function(){s.waiting=!1;var p=s.scrollElement;p.scrollHeight-p.scrollTop<=p.clientHeight+260&&s.hasMoreProps?(s.page++,s.getList("more")):s.loading=!1}),400))},goBack(){window.rmCall(":ctx::cancel")},getList(s){s||(this.propList=[],this.page=0),setLoaderVisibility("block");let p={ltp:"assignment",page:this.page,ptype:"Assignment",sort:this.sortCondition,src:"rm",rmProp:!0,limit:this.LIMIT};"City"!=this.selCity&&(p.city=this.selCity),"Style"!=this.selStyle&&(p.ptype2=this.selStyle),fetchData("/1.5/props/search",{body:p},((p,t)=>{if(setLoaderVisibility("none"),!t.ok)return bus.$emit("flash-message","Error");this.propList=s?this.propList.concat(t.items):t.items,t.cnt>this.propList.length?this.hasMoreProps=!0:this.hasMoreProps=!1}))},selectCity(s){this.selCity=s.isProv?"City":s.o,this.page=0,this.getList(),this.showCity=!1},getPtype2sAndCity(){let s=this;fetchData("/truAssigm/cityAndPtype2List",{body:{ptype:"Assignment"}},((p,t)=>{t.ok&&(s.ptype2s=t.result.ptype2s,s.cityList=t.result.city)}))},selectPypte2s(s){this.selStyle=s,this.page=0,this.getList(),this.showStyle=!1},showSelectOprion(s,p){this.showStyle=!1,this.showCity=!1,s&&(this[s]=!p)}}};initUrlVars();var app=Vue.createApp(trustedAssigm);trans.install(app,{ref:Vue.ref}),app.mixin(pageDataMixins),app.component("prop-item",propItem),app.component("prop-fav-actions",propFavActions),app.component("flash-message",flashMessage),app.mount("#trustedAssigm");
