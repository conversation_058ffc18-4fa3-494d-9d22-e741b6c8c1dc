cityHelper = require '../lib/cityHelper'
helpers = require '../lib/helpers'
{CONTACT_REALTOR_VALUES} = require './common'
{genListingThumbUrlForList,isRMListing} = require './properties'
{filterSpecialCase} = require '../lib/helpers_string'

config = null
module.exports._config_sections = ['robotPrevention','oauthLogin']
module.exports._config = _config = (cfg)->
  if not config
    config = cfg

###
return web detail url with given prop
https://www.realmaster.com/en/toronto-on/8-mckee-ave/1107-willowdale-east-C4717032
###
module.exports.getPropDetailUrl = getPropDetailUrl = (prop,lang,opt)->
  domain = ''
  domain = "#{protocol}://#{host}" if opt and (protocol = opt.protocol) and (host = opt.host)
  domain += "/#{lang}" if lang
  prov = cityHelper.getProvAbbrName(prop.prov_en or prop.prov) or prop.prov_en or prop.prov
  cityPart = "#{(prop.city_en or prop.city or 'city').replace(/\//g,'-')}-#{prov}"
  unt = if prop.unt then "#{prop.unt}-" else ''
  cmnty = prop.cmty_en or prop.cmty
  cmnty = if cmnty then (''+cmnty).replace(/[\/\s]/g,'-') + '-' else ''
  addr = (''+(prop.addr or prop.k or 'address')).replace(/\//g,'-')
  url = filterSpecialCase("/#{cityPart}/#{addr}/#{unt + cmnty}".toLowerCase().replace(/[\-+\s+\|]/g, '-'))
  propId = prop._id
  propId = prop.id if /^RM/.test(prop.id)
  return encodeURI("#{domain}#{url}#{propId}")

module.exports.getPropAlternateUrls = (prop, curLang, cfg) ->
  alternateUrls = {}
  for k,v of (cfg.langPrefix or {})
    if (curLang isnt v) and (not alternateUrls[v])
      alternateUrls[v] = {url: getPropDetailUrl(prop,v,cfg), lang: v}
  return Object.values(alternateUrls)

module.exports.buildWebCityUrl = (saletp,city)->
  "/#{saletp}/#{encodeURIComponent(city.o).replace(/\-/g,'%2D')}-#{city.p_ab}"

module.exports.buildWebCmtyUrl = (saletp,cityProv,cmty) ->
  "/#{saletp}/#{cityProv}/#{encodeURIComponent(cmty).replace(/\-/g,'%2D')}"

module.exports.buildWebHistoryUrl = (saletp, loc) ->
  url = "#{saletp}/#{encodeURIComponent(loc.city).replace(/\-/g,'%2D')}-#{loc.pr}"
  url += "/#{encodeURIComponent(loc.cmty).replace(/\-/g,'%2D')}" if loc.cmty
  url

module.exports.FOR_SALE = FOR_SALE = 'for-sale'
module.exports.FOR_RENT = FOR_RENT = 'for-rent'
module.exports.SOLD_PRICE = SOLD_PRICE = 'sold-price'
module.exports.OPEN_HOUSE = OPEN_HOUSE = 'open-house'
module.exports.LEASE_PRICE = LEASE_PRICE = 'lease-price'
module.exports.ASSIGNMENT = ASSIGNMENT = 'assignment'
module.exports.EXCLUSIVE_FOR_SALE = EXCLUSIVE_FOR_SALE = 'exclusive-for-sale'
module.exports.EXCLUSIVE_FOR_RENT = EXCLUSIVE_FOR_RENT = 'exclusive-for-rent'
module.exports.TRUSTED_ASSIGNMENT = TRUSTED_ASSIGNMENT = 'trusted-assignment'
module.exports.isSaletpInMyListing = (saletp)->
  kvObj = {}
  kvObj[ASSIGNMENT] = 1
  kvObj[EXCLUSIVE_FOR_SALE] = 1
  kvObj[EXCLUSIVE_FOR_RENT] = 1
  kvObj[TRUSTED_ASSIGNMENT] = 1
  return kvObj[saletp]
module.exports.isSaletpValid = (saletp)->
  kvObj = {}
  kvObj[FOR_SALE] = 1
  kvObj[FOR_RENT] = 1
  kvObj[SOLD_PRICE] = 1
  kvObj[OPEN_HOUSE] = 1
  kvObj[LEASE_PRICE] = 1
  kvObj[ASSIGNMENT] = 1
  kvObj[EXCLUSIVE_FOR_SALE] = 1
  kvObj[EXCLUSIVE_FOR_RENT] = 1
  kvObj[TRUSTED_ASSIGNMENT] = 1
  return kvObj[saletp]

module.exports.getDispVar = (req,cb)->
  req.getLoggedInUid req, (uid)->
    dispVar =
      isCip: req.isChinaIP()
      isLoggedIn: uid?
      lang: req.locale()
      isVipUser: req.isAllowed 'vipUser'
      webBackEnd: req.isAllowed 'webBackEnd'
      reqHost: req.host
      hideDownloadAppInWeb: req.setting.hideDownloadAppInWeb
      isNoteAdmin: req.isAllowed 'noteAdmin'
      # Feature flags from config
      noVerifyRobot: not config?.robotPrevention?.enabled
      no3rdPartyLogin: not config?.oauthLogin?.enabled
    cb dispVar if cb

exports.getPropShareUrl = ({prop,page,locale,shareHost})->
  propId = if isRMListing(prop) then prop.id else (prop._id or prop.id)

  if page is CONTACT_REALTOR_VALUES.PROJECT
    # NOTE: projects 不需要login
    return "#{shareHost}/1.5/prop/projects/detail?id=#{prop._id or prop.id}"
  if page is CONTACT_REALTOR_VALUES.EVALUATION
    return "#{shareHost}/1.5/evaluation/result.html?share=1&uaddr=#{prop.uaddr}&id=#{prop.histId}"
  # 分享房源不判断login,需要添加share参数
  return "#{shareHost}/1.5/prop/detail?share=1&id=#{propId}&lang=#{locale}"

#make sure genListingThumbUrlForList only run once
initedImages = (prop={})->
  if not isRMListing(prop)
    return true
  return prop.thumbUrl?

#获取prop需要数据，如图片
exports.getPropField = (list,{isCip,lang,redirect, isPad,formPage,use3rdPic,hostNameCn})->
  return [] unless list?.length
  if not initedImages(list[0]) #isRMListing(list[0])
    genListingThumbUrlForList(list, isCip, isPad,use3rdPic,hostNameCn)
  for p in list
    continue unless p
    if p.adrltr
      {nm_en,nm,nm_zh} = p.adrltr
      if lang is 'en'
        p.adrltr.nm = nm_en or nm or nm_zh
      else
        p.adrltr.nm = nm_zh or nm or nm_en
    p.isTop = (p.status is 'A') and (new Date(p.topTs) >= new Date())
    p.lp_price = helpers.currency (p.lp or p.lpr),'$',0
    p.fullAddress = if p.addr then "#{p.unt or ''} #{p.addr}," else ''
    p.sp_price = helpers.currency (p.sp),'$',0 if p.sp
    p.webUrl = "#{p.webUrl}?d=#{redirect}"
    if formPage
      p.webUrl += "&formPage=#{formPage}"
  return list

getPagingQuery = (filters) ->
  query = ''
  if filters
    for k,v of filters
      query += "&#{k}=#{v}"
  query

exports.PROP_PSIZE = PROP_PSIZE = 30

exports.getPaging = (numProps,baseUrl,filters) ->
  paging = {
    page: filters.page or 0
  }
  if filters
    delete filters.page
  curPage = parseInt(paging.page)
  if numProps >= PROP_PSIZE
    nextPage = 1 + curPage
    paging.next = "#{baseUrl}?page=#{nextPage}#{getPagingQuery filters}"
  if curPage is 0
    paging.prev = false
  else
    prevPage = curPage - 1
    paging.prev = "#{baseUrl}?page=#{prevPage}#{getPagingQuery filters}"
  paging

exports.setPropWebUrl = (req,prop)->
  if 'app' isnt req.getDevType()
    if prop.login # web端的列表页未登录时，鼠标放在需要登录的房源卡片上时浏览器左下角显示的链接显示登录链接
      domain = ''
      domain += "/#{req.locale()}" if req.locale()
      prop.webUrl = req.fullUrl encodeURI("#{domain}/www/login")
    else
      prop.webUrl = req.fullUrl getPropDetailUrl(prop,req.locale())

exports.blurProp = ({prop,isApp=false,MSG_STRINGS})->
  prop.priceValStrRed = 'Listing Price'
  prop.lp = MSG_STRINGS.NA
  prop.lpr = MSG_STRINGS.NA
  prop.sp = MSG_STRINGS.NA
  prop.saletp = prop.saletp_en = []
  prop.saleTpTag = 'Sale'
  prop.lstStr = 'Status'
  prop.askingPriceStr = 'Asking price'
  prop.askingPriceStrDesc = 'Desc'
  prop.addr = 'Prop Addr'
  prop.unt = 'Unt' if prop.unt
  prop.dom = 'Dom'
  prop.onD = 'OnD'
  prop.offD = 'OffD'
  prop.sqft = 'sqft'
  prop.rltr = 'rltr' if prop.rltr
  prop.sid = 'sid' if prop.sid
  prop.id = 'id' if prop.id
  prop.title = '' if prop.title
  prop.jsonldImg = '' if prop.jsonldImg
  prop.lat = 0
  prop.lng = 0
  prop.zip = 'zip'

  #hot fix on 20220923 native autocomplete show blur prop without login msg
  if isApp
    prop.sid = MSG_STRINGS.NEED_LOGIN
  unless isApp
    prop.ptype2 = prop.ptype2_en = []
    prop.rmbdrm = MSG_STRINGS.NA
    prop.bdrms = MSG_STRINGS.NA
    prop.rmbthrm = MSG_STRINGS.NA
    prop.bthrms = MSG_STRINGS.NA
    prop.rmgr = MSG_STRINGS.NA
    prop.gr = MSG_STRINGS.NA