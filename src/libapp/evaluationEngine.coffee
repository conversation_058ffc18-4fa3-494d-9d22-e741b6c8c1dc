regression = require 'regression'
Stats = require('fast-stats').Stats
CHECKPOINT = 'checkpoint'
helpers = require '../lib/helpers'
verbose = 0
geolib = require 'geolib'
debugHelper = require '../lib/debug'
debug = debugHelper.getDebugger()
{search} = require './propElasticSearch'
# debug = (lvl,msg)->
#   unless msg
#     msg = lvl
#     lvl = 0
#   if lvl <= verbose
#     console.log msg

queryNumber = (v)->
  if "number" is typeof v
    return parseInt v
  if 'string' is typeof v
    if m = v.match /(\d)\+/
      return {$gte:parseInt(m[1])}
  0
oneDay = 24*3600000
startDateTime = new Date()
startDateTime = startDateTime.setFullYear(startDateTime.getFullYear() - 30)
getDays = (d)->
  d ?= new Date()
  d = helpers.inputToDate(d)
  Math.round(Math.abs((d.getTime() - startDateTime)/oneDay))

addToSet = (ary,id)->
  if ary.indexOf(id) < 0
    ary.push id
  ary
addToSetArray = (ary,toAdd)->
  for a in toAdd
    ary = addToSet ary,a
  ary

# weight for 1 room, ^2 diff
roomWeight = (weight, fld)->
  (prop, input)->
    fldnm = 'w'+ helpers.strCapitalize(fld)
    if !prop[fld] and !input[fld]
      prop['weight'][fldnm] = 0
      return prop['weight'][fldnm]

    diff = Math.abs((prop[fld]||0) - (input[fld]||0))
    diff *= diff

    prop['weight'][fldnm] = +((weight * diff).toFixed(2))
    prop['weight'][fldnm]

# weight for 6 month, liner
tsWeight = (weight)->
  (prop) ->
    diff = ((new Date()).getTime() - (new Date(prop.mt)).getTime()) / (24* 3600000 * 30 * 6)
    prop.weight.wTs = +((diff * weight).toFixed(2))
    prop.weight.wTs

# weight for true/false
sldWeight = (weight)->
  (prop) ->
    if prop.sp
      prop.weight.wSld =  0
    else
      prop.weight.wSld = weight
    prop.weight.wSld

# weight for max range
rangeWeight = (weight, type)->
  (prop, input) ->
    rangePercent = prop.dist/MAX_DIST_RANGE[input.type]
    prop.weight.wRange =  +((rangePercent * weight).toFixed(2))
    prop.weight.wRange

# weight for diff percentage of input
sqftWeight = (weight)->
  (prop, input) ->
    if !input.sqft
      prop.weight.wSqft = 0
      return prop.weight.wSqft
    propSqft = getPropSqft prop
    unless propSqft
      prop.weight.wSqft = weight
      return weight
    diff = Math.abs(propSqft - input.sqft)
    prop.weight.wSqft = +((weight * diff/input.sqft).toFixed(2))
    prop.weight.wSqft

getSize = (prop) ->
  return 0 unless prop.front_ft and prop.depth and (prop.lotsz_code == 'Metres' || prop.lotsz_code == 'Feet')
  prop.sqsize = prop.front_ft * prop.front_ft
  prop.sqsize = prop.sqsize * 10.76391 if prop.lotsz_code == 'Metres'
  prop.sqsize

# weight diff percentage of input
sizeWeight = (weight)->
  (prop, input) ->
    unless input.sqsize
      prop.weight.wSize = 0
      return prop.weight.wSize
    input.sqsize = getSize input
    prop.sqsize = getSize prop
    unless input.sqsize and prop.sqsize
      prop.weight.wSize = weight
      return weight
    diff = Math.abs(prop.sqsize - input.sqsize)
    prop.weight.wSize = +((weight * diff/input.sqsize).toFixed(2))
    prop.weight.wSize

# weight when 20 percentage of input differencen
priceWeight = (weight)->
  (prop, input) ->
    if !input.lp or input.rental
      prop.weight.wPrice = 0
      return prop.weight.wPrice
    unless prop.lp
      prop.weight.wPrice = weight
      return prop.weight.wPrice
    diff = Math.abs(prop.lp - input.lp)
    diff20p = diff * 5 / input.lp
    prop.weight.wPrice =  +((diff20p * diff20p * weight).toFixed(2))
    prop.weight.wPrice

CMD_WEIGHT =
  Detached:
    gr: roomWeight 30, 'gr'
    sld: sldWeight 20
    bdrms: roomWeight 20, 'bdrms'
    ts: tsWeight 17
    range: rangeWeight 15
    sqft: sqftWeight 15
    size: sizeWeight 15
    bthrms: roomWeight 10, 'bthrms'
    price: priceWeight 15
  'Semi-Detached':
    gr: roomWeight 30, 'gr'
    sld: sldWeight 20
    bdrms: roomWeight 20, 'bdrms'
    ts: tsWeight 17
    range: rangeWeight 15
    sqft: sqftWeight 15
    size: sizeWeight 10
    bthrms: roomWeight 10, 'bthrms'
    price: priceWeight 20
  Townhouse:
    gr: roomWeight 30, 'gr'
    bdrms: roomWeight 23, 'bdrms'
    sld: sldWeight 20
    sqft: sqftWeight 20
    bthrms: roomWeight 15, 'bthrms'
    ts: tsWeight 15
    range: rangeWeight 15
    size: sizeWeight 10
    price: priceWeight 40
  Apartment:
    range: rangeWeight 40
    bdrms: roomWeight 30, 'bdrms'
    br_plus: roomWeight 30, 'br_plus'
    bthrms: roomWeight 25, 'bthrms'
    sqft: sqftWeight 25
    sld: sldWeight 20
    ts: tsWeight 15
    gr: roomWeight 15, 'gr'
    price: priceWeight 40

getMaxWeight = (type) ->
  maxWeight = 0
  prop =
    weight : {}
    bthrms : 3
    gr: 3
    bdrms : 3
    mt: new Date().getTime() - 3600 * 1000 * 24 * 30 * 12
    sld: 'Sale'
    br_plus: 3
    dist: MAX_DIST_RANGE[type]
    lp:80

  p =
    bthrms : 5
    gr: 5
    bdrms : 5
    br_plus: 5
    lp:100
    type: type

  for k, fn of CMD_WEIGHT[type]
    maxWeight += fn prop, p
  maxWeight

getTotalDiff = (prop, p) ->
  prop.weight = {}
  prop.weight.wTotal = 0
  for k, fn of CMD_WEIGHT[p.type]
    prop.weight.wTotal += fn prop, p
  prop.weight.wTotal = +(prop.weight.wTotal.toFixed(2))

getPropSqft = (l) ->
  propSqft = 0
  if l.sqft and helpers.isNumber l.sqft
    propSqft = l.sqft
  else if l.rmSqft and (not isNaN(l.rmSqft))
    propSqft = parseInt(l.rmSqft)
  else if l.sqftEstm and helpers.isNumber l.sqftEstm
    propSqft = l.sqftEstm
  else if l.sqft1 and l.sqft2
    propSqft = Math.round((l.sqft1+l.sqft2)/2)
  else
    propSqft = l.sqft1 || l.sqft2
  return propSqft
###
input: query
output: {mean,accuracy}
###
MAP_PROP_FIELDS =
  lp:1
  lpr:1
  status:1
  daddr:1
  prov:1
  city:1
  unt:1
  st:1
  cmty_en:1
  addr:1
  # apt_num:1
  bdrms:1
  tbdrms:1
  bthrms:1
  topup_pts:1
  topTs:1
  gr:1
  tgr:1
  sid:1
  ptype:1
  ptype2:1
  lat:1
  lng:1
  tax:1
  taxyr:1
  ddfID:1 #used in listingPicUrls
  phosrc:1 #used in listingPicUrls
  picUrl:1
  pho:1
  # phoUrls:1
  # favU:1
  pclass:1 #used in mapSearch icon
  lst:1 #used in list
  saletp:1
  ts:1
  mt:1
  trbtp:1
  zip:1
  sqft:1
  sqft1:1
  sqft2:1
  sqftEstm:1
  rmSqft:1,
  depth:1
  front_ft:1
  lotsz_code:1
  sp:1
  sldt:1
  br_plus:1
  thumbUrl:1
  phoP:1
  tnLH:1
  src:1
  ListingKey:1 # use for DDF pic

#TODO: need to move this part under properties
searchRoutine = (p,input, rental, cb)->
  query = input.q
  sqft = p.sqft
  p.rental = rental
  maxWeight = getMaxWeight(p.type)
  options = Object.assign {}, query, {fields:MAP_PROP_FIELDS,sort:{sldd:1, mt:1}, limit:200}
  search options, (err,ret)->
    if err
      console.error err
      return cb null
    # debug.debug 3,ret
    return cb null unless ret?.props?.length

    # check if finalRound
    range = query.range if query.range
    if query.dist?
      range = query.dist
    notFinalRound = (range < MAX_DIST_RANGE[p.type])
    nosqft = false
    if (p.type is 'Apartment') and sqft
      for prop in ret?.props
        if !prop.sqft || !helpers.isNumber prop.sqft
          nosqft = true;
          break;
    #   return cb null if !nosqft and (ret.length < 2) and p.nocalc and notFinalRound
    #   return cb null if nosqft and (ret.length < 3) and p.nocalc and notFinalRound
    # else
    # return null if less then 3 props
    return cb null if ret?.props?.length < 3 if p.nocalc

    #sort by props weight and then limit to 100., the less the similar
    for prop in ret?.props
      if p.lat and p.lng and prop.lat and prop.lng
        prop.dist = geolib.getDistance(
          {latitude: p.lat, longitude: p.lng},
          {latitude: prop.lat, longitude: prop.lng}
        )
      getTotalDiff prop, p

    ret.props.sort (a,b)-> a.weight.wTotal - b.weight.wTotal;
    ret.props.length = 50 if ret.props.length > 50
    # debug.debug 'before filter by avgt*2:'+ret.length

    #t: avg of first three weight, delete those twice bigger then t
    len = if ret.props.length > 3 then 3 else ret.props.length
    t = 0
    for i in [0..len-1]
      # debug ret[i].weight
      t += +(ret.props[i].weight.wTotal)
    avgt = t/len

    # debug.debug 'len:' + len
    # debug.debug 't:' + t
    # debug.debug 'avgt:' + avgt
    # if avgt>30 then avgt*2 else avgt + 30
    threshold = avgt*2
    ret.props = ret.props.filter (a)-> a.weight.wTotal < threshold
    # debug.debug 'after filter by avgt*2:'+ret.length

    usedProps = ret.props.slice(0)

    weightSum = 0
    for prop in ret.props
      weightSum += +(prop.weight.wTotal)
    weightAvg = weightSum / ret.props.length

    # debug.debug 'maxWeight:' + maxWeight
    # debug.debug 'weightAvg'+weightAvg

    # get data
    m = query.m if query.m
    if query.ts
      month = query.ts
      # debug.debug month
      m = Math.round((new Date()-new Date(month))/(1000*3600*24*30))

    if (weightAvg * 3 > maxWeight) and notFinalRound
      # and is quiet high priority, always add brackets
      # debug.debug "result not good enough: #{range} < #{MAX_DIST_RANGE[p.type]}"
      return cb null

    data = []
    ids = []
    maxDays = 0
    todays = getDays new Date()
    oneMonthDays = todays + 30
    if ret.props[0]
      weightLowEnd = ret.props[0].weight.wTotal
    else
      debug.debug "ret[0] is undefined. #{ret.props.length} #{query.propIds or query.notPropId} #{query.saletp}"
      weightLowEnd = 0
    if ret.props[ret.props.length-1]
      weightHighEnd = ret.props[ret.props.length-1].weight.wTotal
    else
      debug.debug "ret[last] is undefined. #{ret.props.length} #{query.propIds or query.notPropId} #{query.saletp}"
      weightLowEnd = maxWeight
    weightRange= weightHighEnd - weightLowEnd
    level1third = weightRange / 3 + weightLowEnd
    level2third = weightRange * 2 / 3 + weightLowEnd

    for l in ret.props
      if (d = getDays(l.sldd or l.ts or l.mt)) > maxDays
        maxDays = d
      val = l.sp or l.lp or l.lpr
      val = val/l.sqft if !nosqft and l.sqft and p.type =='Apartment' and sqft
      data.push [d,val]
      # if sqft similar, double this point
      # propSqft = getPropSqft l
      # if propSqft && Math.abs(propSqft - sqft) < 50
      #   data.push [d,val]
      weight = l.weight.wTotal
      if weight < level2third
        data.push [d,val]
      if weight < level1third
        data.push [d,val]
      ids.push l._id

    if (p.nocalc)
      return  cb null,{used:ids,usedProps:usedProps, last:m, range :range}

    data.push [todays,null]
    data.push [oneMonthDays,null]
    # debug.debug 2,data
    # if (data.length >= 7) and ((todays - maxDays) <= 15)
    #   # there are enough samples and the latest one is close to today, use polynomial
    #   r = regression 'polynomial', data, 2
    # else
    r = regression 'linear',data, {precision: 4} # 'polynomial', data, 2
    # debug.debug 2,r
    # r2:the coefficient of determination (R2)
    priceToday = r.points[r.points.length - 2][1]
    priceOneMonth = r.points[r.points.length - 1][1]
    delta = 0

    # 2 asking value
    for i in [0..data.length-3]
      break unless data[i][1]
      delta += (data[i][1] - r.points[i][1])**2
    std = Math.sqrt(delta / (data.length - 2))
    std = if std > priceToday * 0.3 then priceToday * 0.3 else std
    aa = Math.abs(std/priceToday)
    debug.debug "searchRoutine std:#{std} aa:#{aa}"

    priceToday = priceToday * sqft if !nosqft and p.type =='Apartment' and sqft
    if ret.props.length > 2
      priceOneMonth = priceOneMonth * sqft if !nosqft and p.type =='Apartment' and sqft

    cb null, {maxWeight: maxWeight, aa:aa, std:std,v:priceToday,v1:priceOneMonth,r2:r.r2,used:ids, usedProps:usedProps, last:m, range :range}

backMonth = (month)->
  now = new Date()
  now.setMonth(now.getMonth() - month)
  now

MAX_DIST_RANGE =
  'Apartment': 400
  'Townhouse': 800
  'Semi-Detached' : 2000
  'Detached': 3000


CMD_OBJ =
  Detached:
    range:
      val:[600,3000]
    ts:
      val:[4,8]
  'Semi-Detached':
    range:
      val:[400,2000]
    ts:
      val:[4,8]
  Townhouse:
    range:
      val:[200,800]
    ts:
      val:[4,8]
  Apartment:
    range:
      val:[80,400]
    ts:
      val:[4,8]


evaluateStage1v3 =(p, rental, cb)->
  queries = []
  if p.ids?.length > 0
    ids = if rental then p.rentalids else p.ids
    # queries = [{_id:{$in: ids}}]
    queries = [{propIds:ids}]
  else
    # CAUTION: if cmdObj may be changed (on left side of =, push, shift...),
    #          must use deepCopy, otherwise the original CMD_OBJ will be changed
    #          permenantly. Many fatal bugs come from this kind of operation. Fred
    unless cmdObj = CMD_OBJ[p.type]
      return cb "evaluateStage1v3: Unknown Type: #{p.type}"
    userRange = cmdObj.range.val
    if (range = if rental then p.range_r else p.range)
      userRange = [range]
    userMonth = cmdObj.ts.val
    if (month = if rental then p.last_r else p.last)
      userMonth = [month]

    for i in [0..1]
      query={ignoreStatus:true}
      query.notPropId = p.mlsid if p.mlsid
      query.queryShould = {bool:{
        should:[
          {bool:{must:[{term:{status:'A'}}]}},
          {bool:{must:[{exists:{field:'sp'}}]}}
        ]
      }}
      if userRange[i]
        query.saletp = if rental then 'Lease' else 'Sale'
        query.ptype = 'r'
        query.ptype2 = p.type
        query.geoq = 100
        query.centerLat = p.lat
        query.centerLng = p.lng
        query.dist = userRange[i]
        query.ts = backMonth userMonth[i]
        queries.push query

  results = []
  debug.debug 3, queries
  doOne = (err)->
    if input = queries.shift()
      # debug.debug 'query:'
      # debug.debug 2, input
      searchRoutine p, {q:input}, rental, (err,result)->
        # debug.debug  'searchRoutine'
        # debug.debug   result
        results.push result if result
        return summarizeStage1 results,cb if result
        return doOne()
    else
      summarizeStage1 results,cb

  doOne()

setAccuracy = (ret,aa)->
  ret.aa = aa # > 15 high; < 6 low;
  ret.a = if aa < 0.05 then 'high' else if aa < 0.1 then 'medium' else 'low'

summarizeStage1 = (results,cb)->
  ret = {}
  unless results?.length > 0
    return cb null,ret
  # debug.debug 1,results
  s = new Stats()
  s1 = new Stats() # one month later
  std = new Stats()
  aa = 0
  used = []
  maxWeight = 0
  usedProps =   []
  for r in results
    s.push r.v if r.v
    s1.push r.v1 if r.v1
    std.push r.std if r.std
    aa += r.aa if r.aa
    maxWeight = r.maxWeight
    used = addToSetArray used,r.used if Array.isArray r.used
    usedProps = addToSetArray usedProps,r.usedProps if Array.isArray r.usedProps
  ret.v = v = Math.round(s.median())  #.toFixed(0)
  ret.v1 = Math.round(s1.median()) #.toFixed(0)
  range = std.median()
  f = v - range * 2
  t = v + range * 2
  #f = v - (v - f) * 2 # double the gap
  #t = v + (t - v) * 2
  f = vmin if f > (vmin = (v * 0.97)) # at least 3% difference
  t = vmax if t < (vmax = (v * 1.03))
  ret.f = f
  ret.t = t
  ret.used = used
  ret.usedProps = usedProps
  ret.last = results[results?.length-1].last
  ret.range = results[results?.length-1].range
  ret.aa = aa
  r.maxWeight = maxWeight
  setAccuracy ret,aa
  debug.debug 'summarizeStage1 aa:' + ret.aa
  cb null,ret

taxAdjustment = (p,s1res,s2res,cb)->
  unless p.tax?
    return cb null,s1res
  tax = parseInt p.tax
  unless ('number' is typeof tax) and (tax > 500) # no less than 500 dollars
    debug.debug "tax not number #{tax}"
    return cb null,s1res
  if s1res?.v? and ((tax < s1res.v * 0.003) or (tax > s1res.v * 0.02))
    # if has price estimate, tax shall within 0.3% ~ 2%
    debug.debug "tax out of range #{tax / s1res.v}"
    return cb null,s1res
  newRes = Object.assign {}, s1res
  newRes.s1 = s1res
  # debug.debug tax
  # debug.debug s2res.p2t
  # debug.debug s2res.p2t1
  # debug.debug s1res.v
  priceFromTax = tax * s2res.p2t
  price1FromTax = tax * s2res.p2t1
  diff = (priceFromTax - s1res.v) / s1res.v
  debug.debug "Tax Diff:" + diff
  if Math.abs(diff) < 0.25 # < 25% difference
    mixinRatio = if p.type == 'Apartment' then 0.1 else 0.3
    newRes.v = (priceFromTax * mixinRatio) + (s1res.v * (1-mixinRatio))
    #newRes.v1 = (price1FromTax * mixinRatio) + (s1res.v1 * (1-mixinRatio))
    delta = s2res.std * tax
    newRes.f = newRes.v - delta
    newRes.t = newRes.v + delta
    setAccuracy newRes,(s1res.aa - 0.0125) # somehow accurate
  else
    newRes = s1res
    setAccuracy newRes,s1res.aa # may not so accurate
  newRes.v1 = (price1FromTax / priceFromTax) * newRes.v
  debug.debug 1,"taxAdjustment:"
  debug.debug 1,newRes
  cb null,newRes

conditionAdjustment = (p,s1res,s2res,cb)->
  if !p.reno or (p.reno is 3)
    return cb null,s1res
  reno = p.reno - 3 # 2 to -2
  newRes = Object.assign {}, s1res
  renoRatio = if p.type=='Apartment' then 0.03 else 0.05
  #delta = newRes.v * renoRatio * ( s2res.std / s2res.p2t )
  delta = newRes.v * renoRatio
  renoV = delta * reno
  newRes.v += renoV
  newRes.v1 += renoV
  newRes.f += renoV
  newRes.t += renoV
  newRes.s1 = s1res
  debug.debug 1,"conditionAdjustment:"
  debug.debug 1,newRes
  cb null,newRes

evaluateStage2 = (p,s1res,SaleRental,cb)->
  options = {
    propIds: s1res.used,
    must: [{range:{tax:{gt:500}}}],
    fields:{tax:1,lp:1,lpr:1,sp:1,ts:1,mt:1,sldd:1}
  }
  # debug.debug query
  search options,(err,ret)->
    if err then return cb err
    return cb null,s1res unless ret?.props?.length
    data = []
    maxDays = 0
    todays = getDays new Date()
    oneMonthDays = todays + 30
    # debug.debug '********************'
    # debug.debug ret

    for l in ret?.props
      # if p.excl && p.excl.indexOf(l._id) > -1
      #   continue;
      if (d = getDays(l.sldd or l.ts or l.mt)) > maxDays
        maxDays = d
      data.push [d,(l.sp or l.lp) / l.tax]

    dataLength = data.length
    data.push [todays,null]
    data.push [oneMonthDays,null]
    # debug.debug data
    # if (data.length >= 10) and ((todays - maxDays) <= 15)
    #   # there are enough samples and the latest one is close to today, use polynomial
    #   r = regression 'polynomial', data, 2
    # else
    r = regression 'linear',data,{precision: 4} # 'polynomial', data, 2
    debug.debug 2,r
    points = r.points
    delta = 0
    for i in [0..(dataLength-1)]
      break unless data[i][1]
      delta += (data[i][1] - points[i][1])**2
    std = Math.sqrt(delta / dataLength)
    p2t = points[points.length - 2][1] # price / tax now
    p2t1 = points[points.length - 1][1] # one month later
    s2res = std:std,p2t:p2t,p2t1:p2t1
    # debug.debug 1,"stage2:"
    # debug.debug 1,s2res
    taxAdjustment p,s1res,s2res,(err,s1resTaxAdjusted)->
      conditionAdjustment p,s1resTaxAdjusted,s2res,(err,result)->
        result.ver = "1.0"
        result.used = s1res.used if s1res.used
        result.usedProps = s1res.usedProps if s1res.usedProps
        cb err,result

evaluateSale = (p, rental, cb)->
  evaluateStage1v3 p, rental, (err,s1res)->
    if err then return cb err
    # debug.debug 's1res:'
    # debug.debug s1res
    return cb null,s1res if !s1res.v or rental or p.nocalc
    return evaluateStage2 p, s1res, 'Sale', cb

evaluate = (p,cb)->
  evaluateSale p, false, (errS,saleResult)->
    if errS then return cb errS
    # debug.debug 'saleResult'
    # debug.debug  saleResult
    evaluateSale p, true, (errR,rentalResult)->
      # debug.debug 'rentalResult'
      # debug.debug rentalResult
      cb errR,{sale:saleResult,rent:rentalResult}

getMaxDistRange = () ->
  return Object.assign {}, MAX_DIST_RANGE

module.exports.evaluate = evaluate
module.exports.distRange = getMaxDistRange()
